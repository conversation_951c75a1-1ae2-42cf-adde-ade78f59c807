package com.operation.electric.yykb.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.operation.electric.common.core.util.R;
import com.operation.electric.common.log.annotation.SysLog;
import com.operation.electric.yykb.entity.DwIgwSysApplicationDirectionEntity;
import com.operation.electric.yykb.service.DwIgwSysApplicationDirectionService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.operation.electric.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 应用方向
 *
 * <AUTHOR>
 * @date 2025-01-13 14:40:21
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/dwIgwSysApplicationDirection" )
@Tag(description = "dwIgwSysApplicationDirection" , name = "应用方向管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class DwIgwSysApplicationDirectionController {

    private final  DwIgwSysApplicationDirectionService dwIgwSysApplicationDirectionService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param dwIgwSysApplicationDirection 应用方向
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysApplicationDirection_view')" )
    public R getDwIgwSysApplicationDirectionPage(@ParameterObject Page page, @ParameterObject DwIgwSysApplicationDirectionEntity dwIgwSysApplicationDirection) {
        LambdaQueryWrapper<DwIgwSysApplicationDirectionEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.like(StringUtils.isNotBlank(dwIgwSysApplicationDirection.getName()) ,DwIgwSysApplicationDirectionEntity::getName,dwIgwSysApplicationDirection.getName());
        return R.ok(dwIgwSysApplicationDirectionService.page(page, wrapper));
    }


    /**
     * 通过id查询应用方向
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysApplicationDirection_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(dwIgwSysApplicationDirectionService.getById(id));
    }

    /**
     * 新增应用方向
     * @param dwIgwSysApplicationDirection 应用方向
     * @return R
     */
    @Operation(summary = "新增应用方向" , description = "新增应用方向" )
    @SysLog("新增应用方向" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysApplicationDirection_add')" )
    public R save(@RequestBody DwIgwSysApplicationDirectionEntity dwIgwSysApplicationDirection) {
        return R.ok(dwIgwSysApplicationDirectionService.save(dwIgwSysApplicationDirection));
    }

    /**
     * 修改应用方向
     * @param dwIgwSysApplicationDirection 应用方向
     * @return R
     */
    @Operation(summary = "修改应用方向" , description = "修改应用方向" )
    @SysLog("修改应用方向" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysApplicationDirection_edit')" )
    public R updateById(@RequestBody DwIgwSysApplicationDirectionEntity dwIgwSysApplicationDirection) {
        return R.ok(dwIgwSysApplicationDirectionService.updateById(dwIgwSysApplicationDirection));
    }

    /**
     * 通过id删除应用方向
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除应用方向" , description = "通过id删除应用方向" )
    @SysLog("通过id删除应用方向" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysApplicationDirection_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(dwIgwSysApplicationDirectionService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param dwIgwSysApplicationDirection 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysApplicationDirection_export')" )
    public List<DwIgwSysApplicationDirectionEntity> export(DwIgwSysApplicationDirectionEntity dwIgwSysApplicationDirection,Long[] ids) {
        return dwIgwSysApplicationDirectionService.list(Wrappers.lambdaQuery(dwIgwSysApplicationDirection).in(ArrayUtil.isNotEmpty(ids), DwIgwSysApplicationDirectionEntity::getId, ids));
    }
}