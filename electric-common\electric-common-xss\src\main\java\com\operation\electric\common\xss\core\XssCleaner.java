/*
 * Copyright (c) 2019-2029, Dreamlu 卢春梦 (<EMAIL> & www.dreamlu.net).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.operation.electric.common.xss.core;

/**
 * xss 清理器
 *
 * <AUTHOR>
 */
public interface XssCleaner {

	/**
	 * 清理 html
	 * @param html html
	 * @return 清理后的数据
	 */
	String clean(String html);

}
