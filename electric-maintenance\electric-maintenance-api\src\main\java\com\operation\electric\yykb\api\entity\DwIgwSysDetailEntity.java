package com.operation.electric.yykb.api.entity;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * igw应用列表
 *
 * <AUTHOR>
 * @date 2024-03-25 15:30:42
 */
@Data
@TableName("dw_igw_sys_detail")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "igw应用列表")
public class DwIgwSysDetailEntity extends Model<DwIgwSysDetailEntity> {

	/**
	* 序号
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="序号")
    private String xh;

	/**
	* 应用编号
	*/
    @Schema(description="应用编号")
    private String appId;

	/**
	* 应用名称
	*/
    @Schema(description="应用名称")
    private String sysName;

	/**
	* 图标路径
	*/
    @Schema(description="图标路径")
    private String sysImgUrl;

	/**
	* 应用链接
	*/
    @Schema(description="应用链接")
    private String sysLink;



	/**
	* 应用环境类型
	*/
    @Schema(description="应用环境类型")
    private String sysType;

	/**
	* 应用类别
	*/
    @Schema(description="应用类别")
    private String sysClass;

	@Schema(description = "应用方向")
	private String sysDirect;

	/**
	* 排序编号
	*/
    @Schema(description="排序编号")
    private Integer sortNm;

	/**
	* 应用所属厂家
	*/
    @Schema(description="应用所属厂家")
    private String sysFac;

    @Schema(description = "应用所属厂家专责")
	private String sysFacPeo;

    @Schema(description = "应用所属厂家专责邮箱")
	private String sysFacPeoMail;

	/**
	* 应用打开方式
	*/
    @Schema(description="应用打开方式")
    private String openType;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	 * 图片访问地址
	 */

	@Schema(description="图片访问地址")
	private String imgUrl;

	/**
	 * 配置文本
	 */
	@Schema(description="配置文本")
	private String configurationText;

	/**
	 * 点击参数
	 */
	@Schema(description="点击参数")
	private Long clicksNumber;

	@Schema(description = "应用状态")
	private Integer applicationState;

	@Schema(description = "应用状态更新时间")
	private LocalDateTime stateUpdateTime;

	@Schema(description = "应用大类")
	private Long applicationClass;

	@Schema(description = "应用方向")
	private Long applicationDirect;

	@Schema(description = "在线状态")
	private String online;
}
