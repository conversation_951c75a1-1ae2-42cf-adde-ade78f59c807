-- 该脚本不要直接执行， 注意维护菜单的父节点ID 默认 父节点-1 , 默认租户 1

-- 菜单SQL
insert into sys_menu ( menu_id,parent_id, path, permission, menu_type, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50030, '-1', '/yykbLog/logSysInfo/index', '', '0', 'icon-bangzhushouji', '0', null , '8', null , '应用系统档案管理', 1);

-- 菜单对应按钮SQL
insert into sys_menu ( menu_id,parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50031,50030, 'yykbLog_logSysInfo_view', '1', null, '1',  '0', null, '0', null, '应用系统档案查看', 1);

insert into sys_menu ( menu_id,parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50032,50030, 'yykbLog_logSysInfo_add', '1', null, '1',  '0', null, '1', null, '应用系统档案新增', 1);

insert into sys_menu (menu_id, parent_id, permission, menu_type, path, icon,  del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50033,50030, 'yykbLog_logSysInfo_edit', '1', null, '1',  '0', null, '2', null, '应用系统档案修改', 1);

insert into sys_menu (menu_id, parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50034,50030, 'yykbLog_logSysInfo_del', '1', null, '1',  '0', null, '3', null, '应用系统档案删除', 1);

insert into sys_menu ( menu_id,parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50035,50030, 'yykbLog_logSysInfo_export', '1', null, '1',  '0', null, '3', null, '导入导出', 1);