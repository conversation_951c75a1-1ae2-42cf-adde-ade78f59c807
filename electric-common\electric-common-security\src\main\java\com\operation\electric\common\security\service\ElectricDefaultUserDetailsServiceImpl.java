/*
 *    Copyright (c) 2018-2025, electric All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: electric
 */

package com.operation.electric.common.security.service;

import com.operation.electric.admin.api.dto.UserInfo;
import com.operation.electric.admin.api.feign.RemoteUserService;
import com.operation.electric.common.core.constant.CacheConstants;
import com.operation.electric.common.core.constant.SecurityConstants;
import com.operation.electric.common.core.constant.enums.UserTypeEnum;
import com.operation.electric.common.core.util.R;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Primary;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

/**
 * 用户详细信息 default
 *
 * <AUTHOR>
 */
@Slf4j
@Primary
@RequiredArgsConstructor
public class ElectricDefaultUserDetailsServiceImpl implements ElectricUserDetailsService {

	private final RemoteUserService remoteUserService;

	private final CacheManager cacheManager;

	/**
	 * 用户密码登录
	 * @param username 用户名
	 * @return
	 * @throws UsernameNotFoundException
	 */
	@Override
	@SneakyThrows
	public UserDetails loadUserByUsername(String username) {
		Cache cache = cacheManager.getCache(CacheConstants.USER_DETAILS);
		if (cache != null && cache.get(username) != null) {
			return cache.get(username, ElectricUser.class);
		}

		R<UserInfo> result = remoteUserService.info(username, SecurityConstants.FROM_IN);
		UserDetails userDetails = getUserDetails(result);
		cache.put(username, userDetails);
		return userDetails;
	}

	/**
	 * 通过用户实体查询
	 * @param electricUser user
	 * @return
	 */
	@Override
	public UserDetails loadUserByUser(ElectricUser electricUser) {
		// 避免 C端用户通过接口调用B端接口的安全问题
		if (UserTypeEnum.TOB.getStatus().equals(electricUser.getUserType())) {
			return loadUserByUsername(electricUser.getUsername());
		}
		return null;
	}

	@Override
	public int getOrder() {
		return Integer.MIN_VALUE;
	}

}
