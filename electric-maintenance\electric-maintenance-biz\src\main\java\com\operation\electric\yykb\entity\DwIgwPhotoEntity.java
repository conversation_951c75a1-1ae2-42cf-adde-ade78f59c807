package com.operation.electric.yykb.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 问题反馈图片表
 *
 * <AUTHOR>
 * @date 2025-06-20 12:01:49
 */
@Data
@TableName("dw_igw_photo")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "问题反馈图片表")
public class DwIgwPhotoEntity extends Model<DwIgwPhotoEntity> {


	/**
	* 图片ID
	*/
    @TableId(type = IdType.AUTO)
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="图片ID")
    private Long id;
 
	/**
	* feedbackId
	*/
    @Schema(description="feedbackId")
    private Long feedbackId;

	/**
	* 文件
	*/
    @Schema(description="文件")
    private String filePath;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	 * 临时会话ID
	 */
	@Schema(description="临时会话ID")
	private String sessionId;

	/**
	 * 是否已关联到反馈
	 */
	@Schema(description="是否已关联到反馈")
	private Boolean isLinked = false;

}