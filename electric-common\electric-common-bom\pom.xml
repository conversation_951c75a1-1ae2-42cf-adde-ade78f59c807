<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>group.springframework</groupId>
        <artifactId>spring-cloud-dependencies-parent</artifactId>
        <version>2022.0.0</version>
        <relativePath/>
    </parent>

    <groupId>com.operation</groupId>
    <artifactId>electric-common-bom</artifactId>
    <packaging>pom</packaging>
    <version>${electric.version}</version>
    <description>electric 公共版本控制</description>

    <properties>
        <electric.version>5.3.0</electric.version>
        <mybatis-plus.version>3.5.4</mybatis-plus.version>
        <mybatis-plus-join.version>1.4.6</mybatis-plus-join.version>
        <dynamic-ds.version>4.2.0</dynamic-ds.version>
        <druid.version>1.2.20</druid.version>
        <hutool.version>5.8.22</hutool.version>
        <mysql.connector.version>8.0.33</mysql.connector.version>
        <oracle.version>********</oracle.version>
        <sqlserver.version>8.4.1.jre8</sqlserver.version>
        <dm.version>*********</dm.version>
        <highgo.version>6.2.0</highgo.version>
        <knife4j.version>3.0.3</knife4j.version>
        <springdoc.version>2.1.0</springdoc.version>
        <swagger.core.version>2.2.14</swagger.core.version>
        <mp.weixin.version>4.4.0</mp.weixin.version>
        <ijpay.version>2.9.6-17</ijpay.version>
        <groovy.version>3.0.3</groovy.version>
        <javax.version>4.0.1</javax.version>
        <jsoup.version>1.13.1</jsoup.version>
        <aviator.version>5.3.3</aviator.version>
        <flowable.version>6.8.0</flowable.version>
        <security.oauth.version>2.5.2.RELEASE</security.oauth.version>
        <fastjson.version>1.2.83</fastjson.version>
        <xxl.job.version>2.3.0</xxl.job.version>
        <aliyun.version>3.0.52.ALL</aliyun.version>
        <aws.version>1.12.261</aws.version>
        <javers.version>7.3.3</javers.version>
        <seata.version>1.7.0</seata.version>
        <asm.version>7.1</asm.version>
        <log4j2.version>2.17.1</log4j2.version>
        <docker.plugin.version>0.33.0</docker.plugin.version>
        <cloud.plugin.version>1.0.0</cloud.plugin.version>
        <sentinel.version>1.8.4</sentinel.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-common-core</artifactId>
                <version>${electric.version}</version>
            </dependency>
            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-common-audit</artifactId>
                <version>${electric.version}</version>
            </dependency>
            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-common-data</artifactId>
                <version>${electric.version}</version>
            </dependency>
            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-common-gateway</artifactId>
                <version>${electric.version}</version>
            </dependency>
            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-common-gray</artifactId>
                <version>${electric.version}</version>
            </dependency>
            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-common-datasource</artifactId>
                <version>${electric.version}</version>
            </dependency>
            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-common-idempotent</artifactId>
                <version>${electric.version}</version>
            </dependency>
            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-common-job</artifactId>
                <version>${electric.version}</version>
            </dependency>
            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-common-log</artifactId>
                <version>${electric.version}</version>
            </dependency>
            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-common-oss</artifactId>
                <version>${electric.version}</version>
            </dependency>
            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-common-security</artifactId>
                <version>${electric.version}</version>
            </dependency>
            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-common-sentinel</artifactId>
                <version>${electric.version}</version>
            </dependency>
            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-common-feign</artifactId>
                <version>${electric.version}</version>
            </dependency>
            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-common-sequence</artifactId>
                <version>${electric.version}</version>
            </dependency>
            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-common-swagger</artifactId>
                <version>${electric.version}</version>
            </dependency>
            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-common-seata</artifactId>
                <version>${electric.version}</version>
            </dependency>
            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-common-xss</artifactId>
                <version>${electric.version}</version>
            </dependency>
            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-common-websocket</artifactId>
                <version>${electric.version}</version>
            </dependency>
            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-common-encrypt-api</artifactId>
                <version>${electric.version}</version>
            </dependency>
            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-common-excel</artifactId>
                <version>${electric.version}</version>
            </dependency>
            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-upms-api</artifactId>
                <version>${electric.version}</version>
            </dependency>

            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-app-server-api</artifactId>
                <version>${electric.version}</version>
            </dependency>

            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-flow-task-api</artifactId>
                <version>${electric.version}</version>
            </dependency>

            <dependency>
                <groupId>com.operation</groupId>
                <artifactId>electric-flow-engine-api</artifactId>
                <version>${electric.version}</version>
            </dependency>

            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm</artifactId>
                <version>${asm.version}</version>
            </dependency>
            <!--  seata kryo 序列化-->
            <dependency>
                <groupId>io.seata</groupId>
                <artifactId>seata-serializer-kryo</artifactId>
                <version>${seata.version}</version>
            </dependency>
            <!--mybatis plus extension,包含了mybatis plus core-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <!--mybatis-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>${dynamic-ds.version}</version>
            </dependency>
            <!-- 连表查询依赖	-->
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-boot-starter</artifactId>
                <version>${mybatis-plus-join.version}</version>
            </dependency>
            <!-- 连表查询依赖	-->
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-annotation</artifactId>
                <version>${mybatis-plus-join.version}</version>
            </dependency>
            <!-- druid 连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!--mysql 驱动-->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.connector.version}</version>
            </dependency>
            <!--oracle 驱动-->
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc8</artifactId>
                <version>${oracle.version}</version>
            </dependency>
            <!-- mssql -->
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>${sqlserver.version}</version>
            </dependency>
            <!--DM8-->
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>${dm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmDialect-for-hibernate5.3</artifactId>
                <version>${dm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.highgo</groupId>
                <artifactId>HgdbJdbc</artifactId>
                <version>${highgo.version}</version>
            </dependency>
            <!--fastjson-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!--计算引擎-->
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>${aviator.version}</version>
            </dependency>
            <!-- 对象对比工具-->
            <dependency>
                <groupId>org.javers</groupId>
                <artifactId>javers-core</artifactId>
                <version>${javers.version}</version>
            </dependency>
            <!--springdoc -->
            <dependency>
                <groupId>io.springboot</groupId>
                <artifactId>knife4j-openapi3-ui</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!--springdoc -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webflux-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations-jakarta</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>
            <!--微信依赖-->
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-mp</artifactId>
                <version>${mp.weixin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-cp</artifactId>
                <version>${mp.weixin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-common</artifactId>
                <version>${mp.weixin.version}</version>
            </dependency>
            <!--支付相关SDK-->
            <dependency>
                <groupId>com.github.javen205</groupId>
                <artifactId>IJPay-WxPay</artifactId>
                <version>${ijpay.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.javen205</groupId>
                <artifactId>IJPay-AliPay</artifactId>
                <version>${ijpay.version}</version>
            </dependency>
            <!--定义groovy 版本-->
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>${javax.version}</version>
            </dependency>
            <!--稳定版本，替代spring security bom内置-->
            <dependency>
                <groupId>org.springframework.security.oauth</groupId>
                <artifactId>spring-security-oauth2</artifactId>
                <version>${security.oauth.version}</version>
            </dependency>
            <!--jsoup html 解析组件-->
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>
            <!--工作流依赖-->
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-process</artifactId>
                <version>${flowable.version}</version>
            </dependency>
            <!--  指定 log4j 版本-->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-to-slf4j</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-bom</artifactId>
                <version>${log4j2.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--hutool bom-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-core</artifactId>
                <version>${sentinel.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-web-servlet</artifactId>
                <version>${sentinel.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-transport-simple-http</artifactId>
                <version>${sentinel.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-parameter-flow-control</artifactId>
                <version>${sentinel.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-api-gateway-adapter-common</artifactId>
                <version>${sentinel.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!--  增加云效nexus示例仓库 （演示使用，可自行删除）  -->
<!--    <distributionManagement>-->
<!--        <repository>-->
<!--            <id>rdc-releases</id>-->
<!--            <url>https://packages.aliyun.com/maven/repository/2161442-release-DcBZC1/</url>-->
<!--        </repository>-->
<!--        <snapshotRepository>-->
<!--            <id>rdc-snapshots</id>-->
<!--            <url>https://packages.aliyun.com/maven/repository/2161442-snapshot-FzKqZK/</url>-->
<!--        </snapshotRepository>-->
<!--    </distributionManagement>-->
</project>
