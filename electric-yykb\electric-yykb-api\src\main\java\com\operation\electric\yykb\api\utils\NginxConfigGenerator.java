package com.operation.electric.yykb.api.utils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

public class NginxConfigGenerator {

    public static void main(String[] args) {
        // 目标文件路径
        String filePath = "/etc/nginx/conf.d/locations.conf";

        // 配置内容
        String configContent =
                "location /10064/ {\n" +
                        "    proxy_pass http://127.0.0.1:9666/;\n" +
                        "    proxy_redirect off;\n" +
                        "    proxy_set_header X-Real-IP $remote_addr;\n" +
                        "    proxy_read_time 6000;\n" +
                        "    client_max_body_size 515m;\n" +
                        "}\n" +
                        "location /10065/ {\n" +
                        "    root /usr/share/nginx/html;\n" +
                        "    index index.html index.htm;\n" +
                        "}";

        // 创建配置文件并重启 NGINX
        createNginxConfigFile(filePath, configContent);
        restartNginx();
    }

    /**
     * 创建或覆盖 NGINX 配置文件
     * @param filePath 配置文件路径
     * @param content 配置内容
     */
    public static void createNginxConfigFile(String filePath, String content) {
        BufferedWriter writer = null;
        try {
            // 创建文件对象
            File configFile = new File(filePath);

            // 确保目标文件所在的目录存在
            File parentDir = configFile.getParentFile();
            if (!parentDir.exists()) {
                parentDir.mkdirs();
            }

            // 创建 BufferedWriter 来写入内容，覆盖原有文件
            writer = new BufferedWriter(new FileWriter(configFile));

            // 写入内容到文件
            writer.write(content);
            System.out.println("NGINX 配置文件已成功生成： " + filePath);

        } catch (IOException e) {
            e.printStackTrace();
            System.err.println("创建 NGINX 配置文件时出错！");
        } finally {
            try {
                if (writer != null) {
                    writer.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 重启 NGINX 服务
     */
    public static void restartNginx() {
        try {
            // 执行系统命令重启 NGINX
            Process process = new ProcessBuilder("sudo", "systemctl", "restart", "nginx").start();

            // 等待命令执行完毕
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                System.out.println("NGINX 服务已成功重启！");
            } else {
                System.err.println("重启 NGINX 时出现错误，退出代码：" + exitCode);
            }

        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
            System.err.println("执行重启 NGINX 命令时出错！");
        }
    }
}

