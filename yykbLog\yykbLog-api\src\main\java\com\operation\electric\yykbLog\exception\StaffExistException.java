package com.operation.electric.yykbLog.exception;

public class StaffExistException extends RuntimeException {
    public StaffExistException(String staffNo, String sysId, Throwable cause) {
        super(msgFormat(staffNo, sysId));
    }

    public StaffExistException(String staffNo, String sysId) {
        super(msgFormat(staffNo, sysId));
    }

    static String msgFormat(String staffNo, String sysId) {
        return String.format("系统编号为%s中已存在对应的OA账号%s，请务重复登记", sysId, staffNo);
    }
}
