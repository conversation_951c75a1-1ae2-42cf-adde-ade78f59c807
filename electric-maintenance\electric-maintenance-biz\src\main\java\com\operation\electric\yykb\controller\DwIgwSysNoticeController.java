package com.operation.electric.yykb.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.operation.electric.common.core.util.R;
import com.operation.electric.common.log.annotation.SysLog;
import com.operation.electric.yykb.entity.DwIgwMessageNoticeEntity;
import com.operation.electric.yykb.entity.DwIgwSysNoticeEntity;
import com.operation.electric.yykb.service.DwIgwSysNoticeService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.operation.electric.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * APP首页最上方广播通知表
 *http://localhost:8888/api/maintenance/dwIgwSysNotice/page?current=1&size=10&descs=&ascs=
 * <AUTHOR>
 * @date 2025-01-16 15:10:17
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/dwIgwSysNotice" )
@Tag(description = "dwIgwSysNotice" , name = "APP首页最上方广播通知表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class DwIgwSysNoticeController {

    private final  DwIgwSysNoticeService dwIgwSysNoticeService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param dwIgwSysNotice APP首页最上方广播通知表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('yykb_dwIgwSysNotice_view')" )
    public R getDwIgwSysNoticePage(@ParameterObject Page page, @ParameterObject DwIgwSysNoticeEntity dwIgwSysNotice) {
        LambdaQueryWrapper<DwIgwSysNoticeEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtils.isNotBlank(dwIgwSysNotice.getMsgContent()) , DwIgwSysNoticeEntity::getMsgContent , dwIgwSysNotice.getMsgContent())
       		 .like(StringUtils.isNotBlank(dwIgwSysNotice.getMsgType()) , DwIgwSysNoticeEntity::getMsgType , dwIgwSysNotice.getMsgType())
				.like(null != dwIgwSysNotice.getMsgEmergency() && dwIgwSysNotice.getMsgEmergency().intValue() != -1 , DwIgwSysNoticeEntity::getMsgEmergency , dwIgwSysNotice.getMsgEmergency());
        return R.ok(dwIgwSysNoticeService.page(page, wrapper));
    }


	/**
	 * 分页查询
	 * @param page 分页对象
	 * @param dwIgwSysNotice APP首页最上方广播通知表
	 * @return
	 */
	@Operation(summary = "获取通知信息" , description = "获取通知信息" )
	@GetMapping("/getDwIgwSysNotice" )
	public R getDwIgwSysNotice(@ParameterObject Page page, @ParameterObject DwIgwSysNoticeEntity dwIgwSysNotice) {
		LambdaQueryWrapper<DwIgwSysNoticeEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.orderByDesc(DwIgwSysNoticeEntity::getCreateTime);
		wrapper.last("limit 1");
		return R.ok(dwIgwSysNoticeService.getOne(wrapper));
	}



	/**
     * 通过id查询APP首页最上方广播通知表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('yykb_dwIgwSysNotice_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(dwIgwSysNoticeService.getById(id));
    }

    /**
     * 新增APP首页最上方广播通知表
     * @param dwIgwSysNotice APP首页最上方广播通知表
     * @return R
     */
    @Operation(summary = "新增APP首页最上方广播通知表" , description = "新增APP首页最上方广播通知表" )
    @SysLog("新增APP首页最上方广播通知表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('yykb_dwIgwSysNotice_add')" )
    public R save(@RequestBody DwIgwSysNoticeEntity dwIgwSysNotice) {
    	//需要做一下转义处理
		String s1 = dwIgwSysNotice.getMsgContent().replaceAll("& lt;", "<");
		String s2 = s1.replaceAll("& gt;", ">");
		dwIgwSysNotice.setMsgContent(s2);
		this.dwIgwSysNoticeService.addNotice(dwIgwSysNotice);
        return R.ok();
    }

    /**
     * 修改APP首页最上方广播通知表
     * @param dwIgwSysNotice APP首页最上方广播通知表
     * @return R
     */
    @Operation(summary = "修改APP首页最上方广播通知表" , description = "修改APP首页最上方广播通知表" )
    @SysLog("修改APP首页最上方广播通知表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('yykb_dwIgwSysNotice_edit')" )
    public R updateById(@RequestBody DwIgwSysNoticeEntity dwIgwSysNotice) {
        return R.ok(dwIgwSysNoticeService.updateById(dwIgwSysNotice));
    }

    /**
     * 通过id删除APP首页最上方广播通知表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除APP首页最上方广播通知表" , description = "通过id删除APP首页最上方广播通知表" )
    @SysLog("通过id删除APP首页最上方广播通知表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('yykb_dwIgwSysNotice_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(dwIgwSysNoticeService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param dwIgwSysNotice 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('yykb_dwIgwSysNotice_export')" )
    public List<DwIgwSysNoticeEntity> export(DwIgwSysNoticeEntity dwIgwSysNotice,Long[] ids) {
        return dwIgwSysNoticeService.list(Wrappers.lambdaQuery(dwIgwSysNotice).in(ArrayUtil.isNotEmpty(ids), DwIgwSysNoticeEntity::getId, ids));
    }
}