<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.operation.electric.app.mapper.AppPageMapper">

  <resultMap id="appPageMap" type="com.operation.electric.app.api.entity.AppPageEntity">
        <id property="id" column="id"/>
        <result property="pageType" column="page_type"/>
        <result property="pageName" column="page_name"/>
        <result property="pageData" column="page_data"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
  </resultMap>
</mapper>
