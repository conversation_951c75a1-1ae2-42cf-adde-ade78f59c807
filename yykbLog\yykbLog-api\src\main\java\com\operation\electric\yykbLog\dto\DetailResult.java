package com.operation.electric.yykbLog.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 日志系统，系统使用详情日志记录信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-25
 */
@Data
public class DetailResult implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 日志记录唯一编号UUID
     */
    private String id;

    /**
     * 数据记录时间
     */
    private Date createdAt;

    /**
     * 数据更新时间
     */
    private Date updatedAt;

    /**
     * 电力统一权限登陆平台OA账号
     */
    private String staffNo;

    /**
     * 动作时间，使用app操作的时间点
     */
    private Date timestamp;

    /**
     * 一级操作名称。例如：登陆/退出登陆/超超时退出
     */
    private String operationClass1;

    /**
     * 二级操作名称。例如：进入数字员工的电费明细清单机器人列表。则一级菜单为营销。二级操作名称为：电费明细清单打印机器人
     */
    private String operationClass2;

    private String operationClass3;

    private String operationClass4;

    private String operationClass5;

    /**
     * 使用系统时客户的电脑IP
     */
    private String ip;

    /**
     * 接入日志系统 的第三方应用/系统的编号
     */
    private String sysId;

    /**
     * 接入日志系统 的第三方应用/系统的名称
     */
    private String sysName;

    /**
     * 部门名称
     */
    private String stdDeptNm;

    /**
     * 单位名称
     */
    private String stdOrgNm;

    /**
     * 用户名称
     */
    private String staffNm;

    /**
     * 接入日志系统 的第三方应用/系统的版本
     */
    private String sysVersion;

    /**
     * 时长
     */
    private BigDecimal onlineTime;

    /**
     * 动作时间，使用app操作的时间点，字符串返回
     */
    private String operationTime;

    /**
     * 重要用户标记
     */
    private Integer vipFlag;

}
