package com.operation.electric.yykb.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.operation.electric.common.core.util.R;
import com.operation.electric.common.log.annotation.SysLog;
import com.operation.electric.yykb.entity.DwIgwMessageNoticeEntity;
import com.operation.electric.yykb.service.DwIgwMessageNoticeService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.operation.electric.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 消息中心系统通知
 *
 * <AUTHOR>
 * @date 2025-06-19 14:54:01
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/dwIgwMessageNotice" )
@Tag(description = "dwIgwMessageNotice" , name = "消息中心系统通知管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class DwIgwMessageNoticeController {

    private final DwIgwMessageNoticeService dwIgwMessageNoticeService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param dwIgwMessageNotice 消息中心系统通知
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('electric_dwIgwMessageNotice_view')" )
    public R getDwIgwMessageNoticePage(@ParameterObject Page page, @ParameterObject DwIgwMessageNoticeEntity dwIgwMessageNotice) {
        LambdaQueryWrapper<DwIgwMessageNoticeEntity> wrapper = Wrappers.lambdaQuery();
		// 根据更新时间降序排列
		wrapper.orderByDesc(DwIgwMessageNoticeEntity::getUpdateTime);
        return R.ok(dwIgwMessageNoticeService.page(page, wrapper));
    }


    /**
     * 通过id查询消息中心系统通知
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('electric_dwIgwMessageNotice_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(dwIgwMessageNoticeService.getById(id));
    }

    /**
     * 新增消息中心系统通知
     * @param dwIgwMessageNotice 消息中心系统通知
     * @return R
     */
    @Operation(summary = "新增消息中心系统通知" , description = "新增消息中心系统通知" )
    @SysLog("新增消息中心系统通知" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('electric_dwIgwMessageNotice_add')" )
    public R save(@RequestBody DwIgwMessageNoticeEntity dwIgwMessageNotice) {
        return R.ok(dwIgwMessageNoticeService.save(dwIgwMessageNotice));
    }

    /**
     * 修改消息中心系统通知
     * @param dwIgwMessageNotice 消息中心系统通知
     * @return R
     */
    @Operation(summary = "修改消息中心系统通知" , description = "修改消息中心系统通知" )
    @SysLog("修改消息中心系统通知" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('electric_dwIgwMessageNotice_edit')" )
    public R updateById(@RequestBody DwIgwMessageNoticeEntity dwIgwMessageNotice) {
        return R.ok(dwIgwMessageNoticeService.updateById(dwIgwMessageNotice));
    }

    /**
     * 通过id删除消息中心系统通知
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除消息中心系统通知" , description = "通过id删除消息中心系统通知" )
    @SysLog("通过id删除消息中心系统通知" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('electric_dwIgwMessageNotice_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(dwIgwMessageNoticeService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param dwIgwMessageNotice 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('electric_dwIgwMessageNotice_export')" )
    public List<DwIgwMessageNoticeEntity> export(DwIgwMessageNoticeEntity dwIgwMessageNotice,Long[] ids) {
        return dwIgwMessageNoticeService.list(Wrappers.lambdaQuery(dwIgwMessageNotice).in(ArrayUtil.isNotEmpty(ids), DwIgwMessageNoticeEntity::getId, ids));
    }
}