package com.operation.electric.yykbLog.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.operation.electric.common.core.util.R;
import com.operation.electric.common.log.annotation.SysLog;
import com.operation.electric.yykbLog.entity.LogImportantStaffEntity;
import com.operation.electric.yykbLog.service.LogImportantStaffService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.operation.electric.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 重要用户
 *
 * <AUTHOR>
 * @date 2024-03-21 17:24:45
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/logImportantStaff" )
@Tag(description = "logImportantStaff" , name = "重要用户管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class LogImportantStaffController {

    private final  LogImportantStaffService logImportantStaffService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param logImportantStaff 重要用户
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('yykbLog_logImportantStaff_view')" )
    public R getLogImportantStaffPage(@ParameterObject Page page, @ParameterObject LogImportantStaffEntity logImportantStaff) {
        LambdaQueryWrapper<LogImportantStaffEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.like(StrUtil.isNotBlank(logImportantStaff.getStaffNo()),LogImportantStaffEntity::getStaffNo,logImportantStaff.getStaffNo());
        return R.ok(logImportantStaffService.page(page, wrapper));
    }


    /**
     * 通过id查询重要用户
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('yykbLog_logImportantStaff_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(logImportantStaffService.getById(id));
    }

    /**
     * 新增重要用户
     * @param logImportantStaff 重要用户
     * @return R
     */
    @Operation(summary = "新增重要用户" , description = "新增重要用户" )
    @SysLog("新增重要用户" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('yykbLog_logImportantStaff_add')" )
    public R save(@RequestBody LogImportantStaffEntity logImportantStaff) {
        return R.ok(logImportantStaffService.save(logImportantStaff));
    }

    /**
     * 修改重要用户
     * @param logImportantStaff 重要用户
     * @return R
     */
    @Operation(summary = "修改重要用户" , description = "修改重要用户" )
    @SysLog("修改重要用户" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('yykbLog_logImportantStaff_edit')" )
    public R updateById(@RequestBody LogImportantStaffEntity logImportantStaff) {
        return R.ok(logImportantStaffService.updateById(logImportantStaff));
    }

    /**
     * 通过id删除重要用户
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除重要用户" , description = "通过id删除重要用户" )
    @SysLog("通过id删除重要用户" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('yykbLog_logImportantStaff_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(logImportantStaffService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param logImportantStaff 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('yykbLog_logImportantStaff_export')" )
    public List<LogImportantStaffEntity> export(LogImportantStaffEntity logImportantStaff,Long[] ids) {
        return logImportantStaffService.list(Wrappers.lambdaQuery(logImportantStaff).in(ArrayUtil.isNotEmpty(ids), LogImportantStaffEntity::getId, ids));
    }
}