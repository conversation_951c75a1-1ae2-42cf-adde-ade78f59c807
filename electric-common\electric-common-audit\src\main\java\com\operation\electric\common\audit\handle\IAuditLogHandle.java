package com.operation.electric.common.audit.handle;

import com.operation.electric.admin.api.entity.SysAuditLog;
import com.operation.electric.common.audit.annotation.Audit;
import org.javers.core.Changes;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/26
 *
 * 审计日志处理器
 */
public interface IAuditLogHandle {

	void handle(Audit audit, Changes changes);

	void asyncSend(List<SysAuditLog> auditLogList);

}
