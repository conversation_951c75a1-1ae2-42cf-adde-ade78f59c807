package com.operation.electric.yykbLog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.operation.electric.yykbLog.dto.StaffResult;
import com.operation.electric.yykbLog.entity.LogStaffEntity;
import com.operation.electric.yykbLog.logRest.v1.vm.StaffVm;

public interface LogStaffService extends IService<LogStaffEntity> {

    /**
     * 查询单条数据
     * @param staffNo OA账号
     * @param sysId 系统编号ID
     * @return
     */
    StaffResult findBySpec(String staffNo, String sysId);

    StaffVm addStaff(StaffVm vm);

}