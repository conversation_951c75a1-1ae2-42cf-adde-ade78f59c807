CREATE DATABASE yykb_test;
USE yykb_test;
/*
 Navicat Premium Data Transfer

 Source Server         : 127.0.0.1
 Source Server Type    : MySQL
 Source Server Version : 80036
 Source Host           : 127.0.0.1:3311
 Source Schema         : yykb_test

 Target Server Type    : MySQL
 Target Server Version : 80036
 File Encoding         : 65001

 Date: 26/05/2025 11:38:02
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for log_detail
-- ----------------------------
DROP TABLE IF EXISTS `log_detail`;
CREATE TABLE `log_detail`  (
  `id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '日志记录唯一编号UUID',
  `created_at` datetime NULL DEFAULT NULL COMMENT '数据记录时间',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '数据更新时间',
  `staff_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '电力统一权限登陆平台OA账号',
  `timestamp` datetime NOT NULL COMMENT '动作时间，使用app操作的时间点',
  `operation_class1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '一级操作名称。例如：登陆/退出登陆/超超时退出',
  `operation_class2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '二级操作名称。例如：进入数字员工的电费明细清单机器人列表。则一级菜单为营销。二级操作名称为：电费明细清单打印机器人',
  `operation_class3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '三级操作名称',
  `operation_class4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '四级操作名称',
  `operation_class5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '五级操作名称',
  `ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '使用系统时客户的电脑IP',
  `sys_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '接入日志系统 的第三方应用/系统的编号',
  `sys_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接入日志系统 的第三方应用/系统的名称',
  `sys_version` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接入日志系统 的第三方应用/系统的版本',
  `online_time` decimal(10, 2) NULL DEFAULT NULL COMMENT '在线时间(秒)',
  `vip_flag` int NULL DEFAULT 0 COMMENT '重要用户标记',
  `server_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '推送日志服务器ip',
  `response_time` int NULL DEFAULT NULL COMMENT '响应时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `staff_no_idx`(`staff_no` ASC) USING BTREE,
  INDEX `sys_id_idx`(`sys_id` ASC) USING BTREE,
  INDEX `index_staff_created`(`created_at` ASC, `staff_no` ASC) USING BTREE,
  INDEX `index_sys_time`(`sys_id` ASC, `created_at` ASC) USING BTREE,
  INDEX `index_sys_nm`(`sys_name` ASC) USING BTREE,
  INDEX `index_time_stamp`(`timestamp` ASC) USING BTREE,
  INDEX `index_sys_user`(`created_at` ASC) USING BTREE,
  INDEX `staff_no`(`staff_no` ASC, `operation_class1` ASC) USING BTREE,
  INDEX `staff_no_2`(`staff_no` ASC, `operation_class1` ASC, `operation_class2` ASC) USING BTREE,
  INDEX `staff_no_3`(`staff_no` ASC, `operation_class1` ASC, `operation_class2` ASC, `operation_class3` ASC) USING BTREE,
  INDEX `staff_no_4`(`staff_no` ASC, `operation_class1` ASC, `operation_class2` ASC, `operation_class3` ASC, `operation_class4` ASC) USING BTREE,
  INDEX `staff_no_5`(`staff_no` ASC, `operation_class1` ASC, `operation_class2` ASC, `operation_class3` ASC, `operation_class4` ASC, `operation_class5` ASC) USING BTREE,
  INDEX `operation_class1`(`operation_class1` ASC, `operation_class2` ASC, `operation_class3` ASC, `operation_class4` ASC, `operation_class5` ASC, `sys_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '日志系统，系统使用详情日志记录信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of log_detail
-- ----------------------------
INSERT INTO `log_detail` VALUES ('e7b6aa9fb14b83efa57f080f614c72a6', '2024-04-01 18:39:24', '2024-04-01 18:39:24', 'chenzz12', '2024-04-01 18:39:47', '位置导航', '查客户表箱', NULL, NULL, NULL, '10.216.7.36', '00081', '现场综合应用', NULL, 8.00, 0, NULL, 23);
INSERT INTO `log_detail` VALUES ('e893982e224b54274f0de5888f8bf829', '2024-05-16 16:34:16', '2024-05-16 16:34:16', 'jiangl119', '2024-05-16 16:34:53', '位置导航', '查客户表箱', NULL, NULL, NULL, '10.150.6.113', '00081', '现场综合应用', NULL, 5.00, 0, NULL, 37);
INSERT INTO `log_detail` VALUES ('e89418c634cf234332099e2d9d8d56bd', '2024-05-24 11:55:44', '2024-05-24 11:55:44', 'shengzm1', '2024-05-24 11:56:25', '位置导航', '查客户表箱', NULL, NULL, NULL, '10.136.24.197', '00081', '现场综合应用', NULL, 20.00, 0, NULL, 41);
INSERT INTO `log_detail` VALUES ('e89bfc3b36def47e619ec2d6d78b29da', '2024-05-20 09:45:00', '2024-05-20 09:45:00', 'lizm20', '2024-05-20 09:45:39', '位置导航', '查客户表箱', NULL, NULL, NULL, '10.16.128.192', '00081', '现场综合应用', NULL, 4.00, 0, NULL, 39);
INSERT INTO `log_detail` VALUES ('e8a7efcb62279cc5d670a291d5ca694b', '2024-05-20 10:01:22', '2024-05-20 10:01:22', 'zhangl142', '2024-05-20 10:02:01', '位置导航', '查客户表箱', NULL, NULL, NULL, '10.120.214.143', '00081', '现场综合应用', NULL, 11.00, 0, NULL, 39);
INSERT INTO `log_detail` VALUES ('e9218aab15041eb94da028e133386823', '2024-05-20 10:41:26', '2024-05-20 10:41:26', 'zhourx3', '2024-05-20 10:42:05', '位置导航', '查客户表箱', NULL, NULL, NULL, '10.100.252.29', '00081', '现场综合应用', NULL, 13.00, 0, NULL, 39);
INSERT INTO `log_detail` VALUES ('e9aaf48648efd83583b5385ef995d840', '2024-05-24 11:24:58', '2024-05-24 11:24:58', 'zhourx3', '2024-05-24 11:25:39', '位置导航', '查客户表箱', NULL, NULL, NULL, '192.168.8.120', '00081', '现场综合应用', NULL, 37.00, 0, NULL, 41);
INSERT INTO `log_detail` VALUES ('e9bded2e9d189f1df877310dd327db84', '2024-05-15 15:31:11', '2024-05-15 15:31:11', 'shaox2', '2024-05-15 15:31:47', '位置导航', '查客户表箱', NULL, NULL, NULL, '100.89.30.153', '00081', '现场综合应用', NULL, 34.00, 0, NULL, 36);
INSERT INTO `log_detail` VALUES ('ea5c9a46fafa38ffb661cd8bdabbcc49', '2024-05-17 10:21:24', '2024-05-17 10:21:24', 'luoj76', '2024-05-17 10:22:02', '位置导航', '查客户表箱', NULL, NULL, NULL, '10.94.155.91', '00081', '现场综合应用', NULL, 6.00, 0, NULL, 38);
INSERT INTO `log_detail` VALUES ('eb3c07081a79c82e09958fdc94254e56', '2024-05-23 22:12:17', '2024-05-23 22:12:17', 'liuzy395', '2024-05-23 22:12:58', '位置导航', '查客户表箱', NULL, NULL, NULL, '10.146.147.81', '00081', '现场综合应用', NULL, 0.00, 0, NULL, 41);
INSERT INTO `log_detail` VALUES ('eb672ad2a27e5b5d2d787bb5a74af6b3', '2024-05-22 13:41:13', '2024-05-22 13:41:13', 'dengjb8', '2024-05-22 13:41:53', '位置导航', '查客户表箱', NULL, NULL, NULL, '10.226.23.9', '00081', '现场综合应用', NULL, 8.00, 0, NULL, 40);
INSERT INTO `log_detail` VALUES ('ec36cd3544e2b9e3a1165586fb69952e', '2024-05-23 15:35:46', '2024-05-23 15:35:46', 'longs8', '2024-05-23 15:36:26', '位置导航', '查客户表箱', NULL, NULL, NULL, '172.16.1.4', '00081', '现场综合应用', NULL, 9.00, 0, NULL, 40);
INSERT INTO `log_detail` VALUES ('eca7f54ec34efdebdf6e6dcdabbd7ff8', '2024-05-16 09:03:47', '2024-05-16 09:03:47', 'chenzz12', '2024-05-16 09:04:24', '位置导航', '查客户表箱', NULL, NULL, NULL, '192.168.0.104', '00081', '现场综合应用', NULL, 12.00, 0, NULL, 37);
INSERT INTO `log_detail` VALUES ('ed451f8e5a45b3a47ae2d4953fd3f223', '2024-05-24 11:35:53', '2024-05-24 11:35:53', 'zhourx3', '2024-05-24 11:36:34', '位置导航', '查客户表箱', NULL, NULL, NULL, '192.168.8.120', '00081', '现场综合应用', NULL, 68.00, 0, NULL, 41);
INSERT INTO `log_detail` VALUES ('ee24cb82b7d63fdb5311b640d1563387', '2024-05-17 10:22:48', '2024-05-17 10:22:48', 'luoj76', '2024-05-17 10:23:26', '位置导航', '查客户表箱', NULL, NULL, NULL, '10.94.155.91', '00081', '现场综合应用', NULL, 18.00, 0, NULL, 38);
INSERT INTO `log_detail` VALUES ('eeb812c553cd1c12e4052471f62e97fe', '2024-05-17 18:29:55', '2024-05-17 18:29:55', 'xiaozy8', '2024-05-17 18:30:33', '位置导航', '查客户表箱', NULL, NULL, NULL, '192.168.3.55', '00081', '现场综合应用', NULL, 6.00, 0, NULL, 38);
INSERT INTO `log_detail` VALUES ('eec0599bb8741a2c9cee190c4602a3f3', '2024-05-24 13:44:52', '2024-05-24 13:44:52', 'zhangmq320', '2024-05-24 13:45:33', '位置导航', '查客户表箱', NULL, NULL, NULL, '10.149.175.40', '00081', '现场综合应用', NULL, 23.00, 0, NULL, 41);
INSERT INTO `log_detail` VALUES ('ef0b45cf9c678732749d966122234852', '2024-05-23 11:00:23', '2024-05-23 11:00:23', 'zhourx3', '2024-05-23 11:01:03', '位置导航', '查客户表箱', NULL, NULL, NULL, '10.11.1.60', '00081', '现场综合应用', NULL, 9.00, 0, NULL, 40);
INSERT INTO `log_detail` VALUES ('ef430404b72085a3753155dcd70cdf19', '2024-05-24 09:27:16', '2024-05-24 09:27:16', 'dingj8', '2024-05-24 09:27:57', '位置导航', '查客户表箱', NULL, NULL, NULL, '192.168.0.100', '00081', '现场综合应用', NULL, 19.00, 0, NULL, 41);
INSERT INTO `log_detail` VALUES ('ef5941d59c712753219836eee885172b', '2024-05-24 08:42:48', '2024-05-24 08:42:48', 'dingj8', '2024-05-24 08:43:29', '位置导航', '查客户表箱', NULL, NULL, NULL, '192.168.0.100', '00081', '现场综合应用', NULL, 18.00, 0, NULL, 41);
INSERT INTO `log_detail` VALUES ('f0d3a417dd2a6b01cb42f22ff09f9177', '2024-05-20 18:40:23', '2024-05-20 18:40:23', 'chenzz12', '2024-05-20 18:41:02', '位置导航', '查客户表箱', NULL, NULL, NULL, '10.3.98.8', '00081', '现场综合应用', NULL, 6.00, 0, NULL, 39);
INSERT INTO `log_detail` VALUES ('f0dd3bb49a427e15c876e199b49a2008', '2024-04-02 16:19:24', '2024-04-02 16:19:24', 'chenzz12', '2024-04-02 16:19:47', '位置导航', '查客户表箱', NULL, NULL, NULL, '192.168.5.109', '00081', '现场综合应用', NULL, 29.00, 0, NULL, 23);
INSERT INTO `log_detail` VALUES ('f18fe861ba68438c1ef33b2b2aec492d', '2024-05-24 11:34:15', '2024-05-24 11:34:15', 'jiangl119', '2024-05-24 11:34:56', '位置导航', '查客户表箱', NULL, NULL, NULL, '10.36.68.101', '00081', '现场综合应用', NULL, 11.00, 0, NULL, 41);
INSERT INTO `log_detail` VALUES ('f1f1e0ab02a46d077444a2e1f48a167e', '2024-04-29 15:51:46', '2024-04-29 15:51:46', 'chenzz12', '2024-04-29 15:52:18', '位置导航', '查客户表箱', NULL, NULL, NULL, '192.168.0.101', '00081', '现场综合应用', NULL, 14.00, 0, NULL, 32);
INSERT INTO `log_detail` VALUES ('f29047b49fbe9c54c7806e54e46b5359', '2024-05-24 16:48:38', '2024-05-24 16:48:38', 'yuangh3', '2024-05-24 16:49:19', '位置导航', '查客户表箱', NULL, NULL, NULL, '10.167.80.98', '00081', '现场综合应用', NULL, 25.00, 0, NULL, 41);
INSERT INTO `log_detail` VALUES ('f37d534a1256d0dbe7387677d2256ab7', '2024-05-23 16:17:45', '2024-05-23 16:17:45', 'shuzx', '2024-05-23 16:18:26', '位置导航', '查客户表箱', NULL, NULL, NULL, '192.168.90.198', '00081', '现场综合应用', NULL, 9.00, 0, NULL, 41);
INSERT INTO `log_detail` VALUES ('f37fa50de538eed27196be25ae4f3dbf', '2024-05-22 12:49:29', '2024-05-22 12:49:29', 'dengjb8', '2024-05-22 12:50:09', '作业登记', '查客户表箱', NULL, NULL, NULL, '192.168.1.211', '00081', '现场综合应用', NULL, 50.00, 0, NULL, 40);
INSERT INTO `log_detail` VALUES ('f42f67e251110b1f41134951522c8e52', '2024-05-20 16:46:21', '2024-05-20 16:46:21', 'xiongj27', '2024-05-20 16:47:00', '位置导航', '查客户表箱', NULL, NULL, NULL, '10.198.97.119', '00081', '现场综合应用', NULL, 63.00, 0, NULL, 39);
INSERT INTO `log_detail` VALUES ('f4d7af55fa9c94688c5589eac4ad4900', '2024-05-23 09:41:47', '2024-05-23 09:41:47', 'chenzz12', '2024-05-23 09:42:28', '位置导航', '查客户表箱', NULL, NULL, NULL, '10.239.187.198', '00081', '现场综合应用', NULL, 7.00, 0, NULL, 41);
INSERT INTO `log_detail` VALUES ('f5b2bf45a811d4b559b22165a743cb78', '2024-04-30 15:58:10', '2024-04-30 15:58:10', 'chenzz12', '2024-04-30 15:58:43', '位置导航', '查客户表箱', NULL, NULL, NULL, '10.151.157.161', '00081', '现场综合应用', NULL, 12.00, 0, NULL, 33);

-- ----------------------------
-- Table structure for log_important_staff
-- ----------------------------
DROP TABLE IF EXISTS `log_important_staff`;
CREATE TABLE `log_important_staff`  (
  `id` int NOT NULL,
  `staff_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `staff_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `staff_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of log_important_staff
-- ----------------------------

-- ----------------------------
-- Table structure for log_staff
-- ----------------------------
DROP TABLE IF EXISTS `log_staff`;
CREATE TABLE `log_staff`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `staff_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '员工编码',
  `staff_nm` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '员工姓名',
  `std_org_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标准单位编码',
  `std_org_nm` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标准单位名称',
  `std_dept_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标准部门编码',
  `std_dept_nm` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标准部门名称',
  `std_team_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标准班组编码',
  `std_team_nm` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标准班组名称',
  `sys_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '系统编号',
  `sys_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '系统名称',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `server_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '推送日志服务器ip',
  `std_dept_nm_new` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_staff_dept`(`staff_no` ASC, `std_dept_nm` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16044 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '日志系统人员基础档案表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of log_staff
-- ----------------------------
INSERT INTO `log_staff` VALUES (15657, 'chenzz12', '彭润海', '43401', '国网长沙供电公司', 'R4300101', '科技数字化部', 'R430010141', '国网长沙供电公司科技数字化部', '00056', '数字员工RPA', '2023-05-14 18:12:58', '2024-02-03 18:28:56', NULL, '科数部');
INSERT INTO `log_staff` VALUES (15996, 'dengjb8', '罗丽', '43401', '国网长沙供电公司', 'R4300101', '信息通信公司', 'R43001010601', '信息运检班', '00058', '桌面小助', '2023-03-16 15:19:12', '2024-02-03 18:28:56', NULL, '信通公司');
INSERT INTO `log_staff` VALUES (16002, 'dingj8', '李俊锋', '43401', '国网长沙供电公司', '4340107', '国网长沙市浏阳市供电公司', '434010712', '大围山乡镇供电所', '00062', '电网小助', '2023-05-14 18:12:58', '2024-02-03 18:28:56', NULL, '浏阳公司');
INSERT INTO `log_staff` VALUES (16003, 'jiangl119', '罗丽', '43401', '国网长沙供电公司', 'R4300101', '信息通信公司', 'R43001010601', '信息运检班', '00059', '全域电网负荷集中监测', '2023-05-14 18:12:58', '2024-02-03 18:28:56', NULL, '信通公司');
INSERT INTO `log_staff` VALUES (16004, 'liuzy395', '罗建', '43401', '国网长沙供电公司', '4340107', '国网长沙市浏阳市供电公司', '434010715', '淮川供电服务站', '00062', '电网小助', '2023-05-14 18:12:58', '2024-02-03 18:28:56', NULL, '浏阳公司');
INSERT INTO `log_staff` VALUES (16005, 'lizm20', '周仲', '43401', '国网长沙供电公司', '4340108', '国网长沙市宁乡市供电公司', '434010814', '煤炭坝乡镇供电所', '00059', '全域电网负荷集中监测', '2023-05-14 18:12:58', '2024-02-03 18:28:56', NULL, '宁乡公司');
INSERT INTO `log_staff` VALUES (16009, 'longs8', '邱灿林', '43401', '国网长沙供电公司', '4340107', '国网长沙市浏阳市供电公司', '434010713', '张坊乡镇供电所', '00062', '电网小助', '2023-05-14 18:12:58', '2024-02-03 18:28:56', NULL, '浏阳公司');
INSERT INTO `log_staff` VALUES (16013, 'luoj76', '李尹', '43401', '国网长沙供电公司', '4340108', '国网长沙市宁乡市供电公司', '434010801', '巷子口乡镇供电所', '00062', '电网小助', '2023-05-14 18:12:58', '2024-02-03 18:28:56', NULL, '宁乡公司');
INSERT INTO `log_staff` VALUES (16014, 'shaox2', '郑周颖', 'R4300104101602', '国网湖南省电力有限公司长沙供电分公司本部', 'R43001', '国网湖南省电力有限公司长沙供电分公司本部', 'R43001', '领导人员', '00062', '电网小助', '2023-05-14 18:12:58', '2024-02-03 18:28:56', NULL, '国网长沙供电公司');
INSERT INTO `log_staff` VALUES (16015, 'shengzm1', '陈潇伦', '43401', '国网长沙供电公司', '4340107', '国网长沙市浏阳市供电公司', 'R430010411', '国网浏阳市供电公司客户服务中心', '00062', '电网小助', '2023-05-14 18:12:58', '2024-02-03 18:28:56', NULL, '浏阳公司');
INSERT INTO `log_staff` VALUES (16017, 'shuzx', '曾祥峰', '43401', '国网长沙供电公司', '4340106', '国网长沙市望城区供电公司', 'R430010307', '国网望城区供电公司配网管理部', '00062', '电网小助', '2023-05-14 18:12:58', '2024-02-03 18:28:56', NULL, '望城公司');
INSERT INTO `log_staff` VALUES (16019, 'xiaozy8', '肖友亿', '43401', '国网长沙供电公司', '4340107', '国网长沙市浏阳市供电公司', '434010705', '大瑶乡镇供电所', '00062', '电网小助', '2023-05-14 18:12:58', '2024-02-03 18:28:56', NULL, '浏阳公司');
INSERT INTO `log_staff` VALUES (16022, 'xiongj27', '张存福', '43401', '国网长沙供电公司', '4340107', '国网长沙市浏阳市供电公司', '434010710', '官渡乡镇供电所', '00062', '电网小助', '2023-05-14 18:12:58', '2024-02-03 18:28:56', NULL, '浏阳公司');
INSERT INTO `log_staff` VALUES (16023, 'yuangh3', '李凌芳', '43401', '国网长沙供电公司', '4340108', '国网长沙市宁乡市供电公司', 'R43001050911', '配电运检（抢修）一班', '00062', '电网小助', '2023-05-14 18:12:58', '2024-02-03 18:28:56', NULL, '宁乡公司');
INSERT INTO `log_staff` VALUES (16024, 'zhangl142', '黄维', '43401', '国网长沙供电公司', '4340110', '国网长沙市雨花区支公司', '434011008', '跳马乡镇供电所', '00062', '电网小助', '2023-05-14 18:12:58', '2024-02-03 18:28:56', NULL, '雨花支公司');
INSERT INTO `log_staff` VALUES (16025, 'zhangmq320', '杨杰', '43401', '国网长沙供电公司', '4340109', '国网长沙市湘江新区供电公司', '434010908', '望城坡供电服务站', '00062', '电网小助', '2023-05-14 18:12:58', '2024-02-03 18:28:56', NULL, '湘江新区公司');

-- ----------------------------
-- Table structure for log_staff_bak
-- ----------------------------
DROP TABLE IF EXISTS `log_staff_bak`;
CREATE TABLE `log_staff_bak`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `staff_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '员工编码',
  `staff_nm` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '员工姓名',
  `std_org_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标准单位编码',
  `std_org_nm` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标准单位名称',
  `std_dept_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标准部门编码',
  `std_dept_nm` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标准部门名称',
  `std_team_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标准班组编码',
  `std_team_nm` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标准班组名称',
  `sys_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '系统编号',
  `sys_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '系统名称',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_staff_dept`(`staff_no` ASC, `std_dept_nm` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '日志系统人员基础档案表（接入轻点人员组织前）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of log_staff_bak
-- ----------------------------

-- ----------------------------
-- Table structure for log_sys_info
-- ----------------------------
DROP TABLE IF EXISTS `log_sys_info`;
CREATE TABLE `log_sys_info`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `sys_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '系统名称',
  `salt` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '盐',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `creator_id` int NULL DEFAULT NULL COMMENT '创建者',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `updator_id` int NULL DEFAULT NULL COMMENT '更新人',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '是否开启',
  `app_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用唯一编号',
  `app_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用密钥',
  `sys_id` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '系统编码',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_idx`(`app_key` ASC) USING BTREE,
  UNIQUE INDEX `sys_id_unq_idx`(`sys_id` ASC) USING BTREE,
  INDEX `sys_name_idx`(`sys_name` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '应用系统档案' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of log_sys_info
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
