package com.operation.electric.yykb.config;

import com.operation.electric.yykb.jobhandler.CleanUnlinkImageJob;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import reactor.core.scheduler.Scheduler;

import java.util.concurrent.Executors;

/**
 * @Author: zhuoxaing.peng
 * @Date: 2025-06-24 17:31
 * @description:
 * @Version: 1.0
 */
@Configuration
@EnableScheduling
public class SchedulingConfig implements SchedulingConfigurer {


	@Override
	public void configureTasks(ScheduledTaskRegistrar scheduledTask) {
		scheduledTask.setScheduler(Executors.newScheduledThreadPool(30));
	}

	@Bean
	public CleanUnlinkImageJob cleanUnlinkImageJob (){
		return new CleanUnlinkImageJob();
	}



}
