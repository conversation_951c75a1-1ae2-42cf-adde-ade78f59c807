<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.operation.electric.yykb.mapper.DwIgwSysDetailMapper">
    <resultMap id="dwIgwSysDetailMap" type="com.operation.electric.yykb.api.entity.DwIgwSysDetailEntity">
        <id property="xh" column="xh"/>
        <result property="appId" column="app_id"/>
        <result property="sysName" column="sys_name"/>
        <result property="sysImgUrl" column="sys_img_url"/>
        <result property="sysLink" column="sys_link"/>
        <result property="sysType" column="sys_type"/>
		<result property="sysClass" column="sys_class"/>
		<result property="sysDirect" column="sys_direct"/>
        <result property="sortNm" column="sort_nm"/>
		<result property="sysFac" column="sys_fac"/>
		<result property="sysFacPeo" column="sys_fac_peo"/>
		<result property="sysFacPeoMail" column="sys_fac_peo_mail"/>
        <result property="openType" column="open_type"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
		<result property="updateTime" column="update_time"/>
		<result property="imgUrl" column="img_url"/>
		<result property="configurationText" column="configuration_text"/>
		<result property="clicksNumber" column="clicks_number"/>
		<result property="applicationState" column="application_state"/>
		<result property="stateUpdateTime" column="state_update_time"/>
		<result property="applicationClass" column="application_class"/>
		<result property="applicationDirect" column="application_direct"/>
    </resultMap>

    <update id="monitor">
        update dw_igw_sys_detail
        set state_update_time = now() ,
			application_state = 1
        where app_id = #{appId}
    </update>

    <select id="monitoringAllData" resultMap="dwIgwSysDetailMap">
        select *
        from dw_igw_sys_detail
        where state_update_time <![CDATA[<=]]> NOW() - INTERVAL 10 MINUTE
			AND sys_type = 'prod'
            AND online = "TRUE"
            AND application_state = "1";
    </select>

    <update id="monitorError">
        update dw_igw_sys_detail
        set application_state = 0 , online = "FALSE"
        where app_id = #{appId}
    </update>

    <select id="quantitativeRestriction" resultType="java.lang.Integer">
        select count(1) from dw_igw_sys_detail where sys_type = 'prod' and sys_class = '常用'
    </select>

    <select id="findAll" resultType="java.lang.String">
        select sys_name from dw_igw_sys_detail where sys_type = 'prod'
    </select>

	<select id="findDetailByAppID" resultType="com.operation.electric.yykb.api.entity.DwIgwSysDetailEntity">
		select * from dw_igw_sys_detail where app_id = #{appId} and sys_type = 'prod'
	</select>
</mapper>
