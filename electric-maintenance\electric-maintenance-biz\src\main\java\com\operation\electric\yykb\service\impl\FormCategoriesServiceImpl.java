package com.operation.electric.yykb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.operation.electric.common.core.util.R;
import com.operation.electric.yykb.entity.FormCategoriesEntity;
import com.operation.electric.yykb.entity.FormCategoryNode;
import com.operation.electric.yykb.mapper.FormCategoriesMapper;
import com.operation.electric.yykb.service.FormCategoriesService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 应用分类
 * <AUTHOR>
 * @date 2024-12-13 10:51:45
 */
@Service
public class FormCategoriesServiceImpl extends ServiceImpl<FormCategoriesMapper, FormCategoriesEntity> implements FormCategoriesService {

	@Override
	public List<FormCategoryNode> buidTreeList() {
		List<FormCategoryNode> categoriesEntities = this.getBaseMapper().selectCategoryList();
		return this.buildCategoryTree(categoriesEntities);
	}

	@Override
	public R diySortList(FormCategoryNode formCategoryNode) {
		if (ObjectUtils.isEmpty(formCategoryNode)) {
			return R.failed();
		}
		if (CollectionUtils.isEmpty(formCategoryNode.getChildren())) {
			return R.failed("请传入正确参数");
		}
		List<FormCategoriesEntity> entityList = new ArrayList<>();
		FormCategoriesEntity entity = null;
		//for循环更改调整后的索引个数
		for (int index = 0; index < formCategoryNode.getChildren().size(); index++) {
			FormCategoryNode categoryNode = formCategoryNode.getChildren().get(index);
			categoryNode.setSortOrder(index);
			entity = new FormCategoriesEntity();
			entity.setId(categoryNode.getId());
			entity.setSortOrder(categoryNode.getSortOrder());
			entityList.add(entity);
		}
		//根据ID批量更新数据
		this.saveOrUpdateBatch(entityList);
		return R.ok("成功");
	}

	@Override
	public R deleteNode(Integer id) {
		//1.根据ID查询登记是哪一级
		FormCategoriesEntity categoriesForm = this.getBaseMapper().selectById(id);
		//2.如果等级为一级 需要判断是否有
		if(ObjectUtils.isEmpty(categoriesForm)){
			return R.failed("请传入正确参数");
		}
		if ( 0 == categoriesForm.getParentId() || null == categoriesForm.getParentId()) {
			//这里应该判断是否还有子集ID
			List<FormCategoryNode> categoryNodes = this.getBaseMapper().selectCategoryListByParentID(categoriesForm.getId());
			if (CollectionUtils.isEmpty(categoryNodes)) {
					this.getBaseMapper().deleteById(categoriesForm.getId());
					return R.ok("删除成功！");
			}

			return R.failed("请先清空二级菜单再进行操作！");
		}
		this.getBaseMapper().deleteById(categoriesForm.getId());
		return R.ok("删除成功！");
	}

	// 假设从数据库查询到的所有分类数据
	public List<FormCategoryNode> buildCategoryTree(List<FormCategoryNode> categories) {
		Map<Integer, FormCategoryNode> categoryMap = new HashMap<>();
		List<FormCategoryNode> rootCategories = new ArrayList<>();
		// 将所有分类存入Map
		for (FormCategoryNode categoryNode : categories) {
			categoryMap.put(categoryNode.getId(), categoryNode);
		}
		// 构建树形结构
		for (FormCategoryNode formCategoryNode : categories) {
			if (formCategoryNode.getParentId() == 0) {  // 根分类
				rootCategories.add(formCategoryNode);
			} else {
				FormCategoryNode parentCategory = categoryMap.get(formCategoryNode.getParentId());
				if (parentCategory != null) {
					parentCategory.getChildren().add(formCategoryNode);  // 添加到父分类的子分类列表中
				}
			}
		}
		// 排序子分类
		for (FormCategoryNode formCategoryNode : rootCategories) {
			formCategoryNode.getChildren().sort((c1, c2) -> Integer.compare(c1.getSortOrder(), c2.getSortOrder()));
		}
		return rootCategories; // 返回根分类列表
	}

	@Override
	public List<FormCategoriesEntity> getChildInfoById(Integer id) {
		return this.getBaseMapper().getChildById(id);
	}
}