package com.operation.electric.yykb.mapper;

import com.operation.electric.common.data.datascope.ElectricBaseMapper;
import com.operation.electric.yykb.entity.FormCategoriesEntity;
import com.operation.electric.yykb.entity.FormCategoryNode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface FormCategoriesMapper extends ElectricBaseMapper<FormCategoriesEntity> {

	List<FormCategoryNode> selectCategoryList();

	//根据parentId查询所有的子集列表
	List<FormCategoryNode> selectCategoryListByParentID(@Param("pid") Integer pid);

	List<FormCategoriesEntity> getChildById(@Param("parentId") Integer id);
}