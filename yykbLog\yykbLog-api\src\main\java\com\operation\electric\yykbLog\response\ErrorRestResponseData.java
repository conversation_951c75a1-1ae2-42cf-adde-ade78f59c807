package com.operation.electric.yykbLog.response;



public class ErrorRestResponseData<T> extends RestResponseData<T>{
    private String exceptionClazz;

    public ErrorRestResponseData(String message) {
        super(false, 500, message, (T)null);
    }

    public ErrorRestResponseData(Integer code, String message) {
        super(false, code, message, (T)null);
    }

    public ErrorRestResponseData(Integer code, String message, T object) {
        super(false, code, message, object);
    }

    public String getExceptionClazz() {
        return this.exceptionClazz;
    }

    public void setExceptionClazz(final String exceptionClazz) {
        this.exceptionClazz = exceptionClazz;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof ErrorRestResponseData)) {
            return false;
        } else {
            ErrorRestResponseData other = (ErrorRestResponseData)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                Object this$exceptionClazz = this.getExceptionClazz();
                Object other$exceptionClazz = other.getExceptionClazz();
                if (this$exceptionClazz == null) {
                    if (other$exceptionClazz != null) {
                        return false;
                    }
                } else if (!this$exceptionClazz.equals(other$exceptionClazz)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof ErrorRestResponseData;
    }

    public int hashCode() {
        boolean PRIME = true;
        int result = 1;
        Object $exceptionClazz = this.getExceptionClazz();
        result = result * 59 + ($exceptionClazz == null ? 43 : $exceptionClazz.hashCode());
        return result;
    }

    public String toString() {
        return "ErrorResponseData(exceptionClazz=" + this.getExceptionClazz() + ")";
    }
}
