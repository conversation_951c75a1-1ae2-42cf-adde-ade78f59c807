package com.operation.electric.yykbLog.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 日志系统人员基础档案表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-25
 */
@Data
public class StaffResult implements Serializable {

    private static final long serialVersionUID = 1L;


    private Integer id;

    /**
     * 员工编码
     */
    private String staffNo;

    /**
     * 员工姓名
     */
    private String staffNm;

    /**
     * 标准单位编码
     */
    private String stdOrgNo;

    /**
     * 标准单位名称
     */
    private String stdOrgNm;

    /**
     * 标准部门编码
     */
    private String stdDeptNo;

    /**
     * 标准部门名称
     */
    private String stdDeptNm;

    /**
     * 标准班组编码
     */
    private String stdTeamNo;

    /**
     * 标准班组名称
     */
    private String stdTeamNm;

    /**
     * 系统编号
     */
    private String sysId;

    /**
     * 系统名称
     */
    private String sysName;

    /**
     * IP地址
     */
    private String ip;

    private Date timestamp;

}
