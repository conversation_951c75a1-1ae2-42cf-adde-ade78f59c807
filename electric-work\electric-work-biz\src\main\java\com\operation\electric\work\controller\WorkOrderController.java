package com.operation.electric.work.controller;


import com.operation.electric.common.core.util.R;
import com.operation.electric.flow.task.api.feign.RemoteFlowTaskService;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/order")
@Tag(description = "dict", name = "工单")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class WorkOrderController {

    @Value("${work.flowId}")
    private String workFolwId;

    /**
     * 默认工单表单的名称
     */
    @Value("${work.name}")
    private String workFolwName;

    @Autowired
    private RemoteFlowTaskService remoteFlowTaskService;
    /**
     * 获取工单流程id
     * @return
     */
    @GetMapping("/getWorkFolwId")
    public R getWorkFolwId() {
        return remoteFlowTaskService.getWorkFolwId(workFolwName);
    }
}
