package com.operation.electric.common.datasource.support;

/**
 * <AUTHOR>
 * @date 2019-04-01
 * <p>
 * 数据源相关常量
 */
public interface DataSourceConstants {

	/**
	 * 数据源名称
	 */
	String NAME = "name";

	/**
	 * 默认数据源（master）
	 */
	String DS_MASTER = "master";

	/**
	 * jdbcurl
	 */
	String DS_JDBC_URL = "url";

	/**
	 * 配置类型
	 */
	String DS_CONFIG_TYPE = "conf_type";

	/**
	 * 用户名
	 */
	String DS_USER_NAME = "username";

	/**
	 * 密码
	 */
	String DS_USER_PWD = "password";

	/**
	 * 数据库类型
	 */
	String DS_TYPE = "ds_type";

	/**
	 * 数据库名称
	 */
	String DS_NAME = "ds_name";

	/**
	 * 主机类型
	 */
	String DS_HOST = "host";

	/**
	 * 端口
	 */
	String DS_PORT = "port";

	/**
	 * 实例名称
	 */
	String DS_INSTANCE = "instance";

}
