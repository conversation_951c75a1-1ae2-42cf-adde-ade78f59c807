package com.operation.electric.flow.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.operation.electric.common.core.util.R;
import com.operation.electric.flow.task.entity.Process;
import com.operation.electric.flow.task.vo.ProcessVO;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-25
 */
public interface IProcessService extends IService<Process> {

	/**
	 * 获取详细数据
	 * @param flowId
	 * @return
	 */
	R<ProcessVO> getDetail(String flowId);

	Process getByFlowId(String flowId);

	void updateByFlowId(Process process);

	void hide(String flowId);

	/**
	 * 创建流程
	 * @param process
	 * @return
	 */
	R create(Process process);

	/**
	 * 编辑表单
	 * @param flowId 摸板ID
	 * @param type 类型 stop using delete
	 * @return 操作结果
	 */
	R update(String flowId, String type, Long groupId);

	/**
	 *通过分组名称查询流程信息
	 * @param groupName
	 * @return
	 */
	R<List<Process>> getProcessListByGroupName(String groupName);

	/**
	 * 通过分组名称查询流程信息(可用和历史版本)
	 * @param groupName
	 * @return
	 */
	R<List<Process>> getProcessListByGroupNameALL(String groupName);
}
