<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.operation</groupId>
		<artifactId>electric-common</artifactId>
		<version>5.3.0</version>
	</parent>

	<artifactId>electric-common-sequence</artifactId>
	<packaging>jar</packaging>

	<description>electric 分布式发号器</description>

	<dependencies>
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
		</dependency>
		<dependency>
			<groupId>com.operation</groupId>
			<artifactId>electric-common-core</artifactId>
		</dependency>
		<!--mybatis plus-->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-extension</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-3-starter</artifactId>
			<optional>true</optional>
		</dependency>
	</dependencies>
</project>
