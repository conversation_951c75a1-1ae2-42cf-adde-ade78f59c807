-- 该脚本不要直接执行， 注意维护菜单的父节点ID 默认 父节点-1 , 默认租户 1

-- 菜单SQL
insert into sys_menu ( menu_id,parent_id, path, permission, menu_type, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50010, '-1', '/yykbLog/logImportantStaff/index', '', '0', 'icon-bangzhushouji', '0', null , '8', null , '重要用户管理', 1);

-- 菜单对应按钮SQL
insert into sys_menu ( menu_id,parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50011,50010, 'yykbLog_logImportantStaff_view', '1', null, '1',  '0', null, '0', null, '重要用户查看', 1);

insert into sys_menu ( menu_id,parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50012,50010, 'yykbLog_logImportantStaff_add', '1', null, '1',  '0', null, '1', null, '重要用户新增', 1);

insert into sys_menu (menu_id, parent_id, permission, menu_type, path, icon,  del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50013,50010, 'yykbLog_logImportantStaff_edit', '1', null, '1',  '0', null, '2', null, '重要用户修改', 1);

insert into sys_menu (menu_id, parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50014,50010, 'yykbLog_logImportantStaff_del', '1', null, '1',  '0', null, '3', null, '重要用户删除', 1);

insert into sys_menu ( menu_id,parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50015,50010, 'yykbLog_logImportantStaff_export', '1', null, '1',  '0', null, '3', null, '导入导出', 1);