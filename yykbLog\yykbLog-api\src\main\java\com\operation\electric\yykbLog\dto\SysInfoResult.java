package com.operation.electric.yykbLog.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 应用系统档案
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
@Data
public class SysInfoResult implements Serializable {

    private static final long serialVersionUID = 1L;

    private String sysId;

    private Integer id;

    /**
     * 系统名称
     */
    private String sysName;

    /**
     * 盐
     */
    private String salt;

    /**
     * 创建时间
     */
    private String createdAt;

    /**
     * 创建者
     */
    private Integer creatorId;

    /**
     * 更新时间
     */
    private String updatedAt;

    /**
     * 更新人
     */
    private Integer updatorId;

    /**
     * 是否开启
     */
    private Integer status;

    /**
     * 应用唯一编号
     */
    private String appKey;

    /**
     * 应用密钥
     */
    private String appSecret;

}
