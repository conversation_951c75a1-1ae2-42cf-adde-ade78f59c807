package com.operation.electric.yykb.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.operation.electric.yykb.entity.DwIgwPhotoEntity;
import com.operation.electric.yykb.mapper.DwIgwPhotoMapper;
import com.operation.electric.yykb.service.DwIgwPhotoService;
import org.springframework.stereotype.Service;

import java.nio.file.Files;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 问题反馈图片表
 *
 * <AUTHOR>
 * @date 2025-06-20 12:01:49
 */
@Service
public class DwIgwPhotoServiceImpl extends ServiceImpl<DwIgwPhotoMapper, DwIgwPhotoEntity> implements DwIgwPhotoService {



	@Override
	public List<DwIgwPhotoEntity> findByIsLinkedFalseAndUploadTimeBefore(LocalDateTime threshold) {

		LambdaQueryWrapper<DwIgwPhotoEntity> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(DwIgwPhotoEntity::getIsLinked,false);
		queryWrapper.between(DwIgwPhotoEntity::getCreateTime,threshold,LocalDateTime.now());
		return this.baseMapper.selectList(queryWrapper);
	}
}