CREATE DATABASE electricx_job;
USE electricx_job;
/*
 Navicat Premium Data Transfer

 Source Server         : mysql8.0
 Source Server Type    : MySQL
 Source Server Version : 80027
 Source Host           : localhost:13309
 Source Schema         : electricx_job

 Target Server Type    : MySQL
 Target Server Version : 80027
 File Encoding         : 65001

 Date: 23/05/2025 16:57:27
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for qrtz_blob_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_blob_triggers`;
CREATE TABLE `qrtz_blob_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `trigger_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `trigger_group` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `blob_data` blob NULL,
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_blob_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_blob_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_calendars
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_calendars`;
CREATE TABLE `qrtz_calendars`  (
  `sched_name` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `calendar_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `calendar` blob NOT NULL,
  PRIMARY KEY (`sched_name`, `calendar_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_calendars
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_cron_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_cron_triggers`;
CREATE TABLE `qrtz_cron_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `trigger_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `trigger_group` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `cron_expression` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `time_zone_id` varchar(80) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_cron_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_cron_triggers
-- ----------------------------
INSERT INTO `qrtz_cron_triggers` VALUES ('clusteredScheduler', '测试', '111', '0 0/1 * * * ? ', 'Asia/Shanghai');

-- ----------------------------
-- Table structure for qrtz_fired_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_fired_triggers`;
CREATE TABLE `qrtz_fired_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `entry_id` varchar(95) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `trigger_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `trigger_group` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `instance_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `fired_time` bigint NOT NULL,
  `sched_time` bigint NOT NULL,
  `priority` int NOT NULL,
  `state` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `job_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `job_group` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `is_nonconcurrent` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `requests_recovery` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`sched_name`, `entry_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_fired_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_job_details
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_job_details`;
CREATE TABLE `qrtz_job_details`  (
  `sched_name` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `job_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `job_group` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `description` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `job_class_name` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `is_durable` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `is_nonconcurrent` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `is_update_data` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `requests_recovery` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `job_data` blob NULL,
  PRIMARY KEY (`sched_name`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_job_details
-- ----------------------------
INSERT INTO `qrtz_job_details` VALUES ('clusteredScheduler', '测试', '111', NULL, 'com.operation.electric.daemon.quartz.config.ElectricQuartzFactory', '0', '1', '0', '0', 0xACED0005737200156F72672E71756172747A2E4A6F62446174614D61709FB083E8BFA9B0CB020000787200266F72672E71756172747A2E7574696C732E537472696E674B65794469727479466C61674D61708208E8C3FBC55D280200015A0013616C6C6F77735472616E7369656E74446174617872001D6F72672E71756172747A2E7574696C732E4469727479466C61674D617013E62EAD28760ACE0200025A000564697274794C00036D617074000F4C6A6176612F7574696C2F4D61703B787001737200116A6176612E7574696C2E486173684D61700507DAC1C31660D103000246000A6C6F6164466163746F724900097468726573686F6C6478703F4000000000000C7708000000100000000174000B7363686564756C654A6F6273720032636F6D2E6F7065726174696F6E2E656C6563747269632E6461656D6F6E2E71756172747A2E656E746974792E5379734A6F6200000000000000010200174C0009636C6173734E616D657400124C6A6176612F6C616E672F537472696E673B4C0008637265617465427971007E00094C000A63726561746554696D657400194C6A6176612F74696D652F4C6F63616C4461746554696D653B4C000E63726F6E45787072657373696F6E71007E00094C000B657865637574655061746871007E00094C00106A6F624578656375746553746174757371007E00094C00086A6F6247726F757071007E00094C00056A6F6249647400104C6A6176612F6C616E672F4C6F6E673B4C00076A6F624E616D6571007E00094C00086A6F624F7264657271007E00094C00096A6F6253746174757371007E00094C000D6A6F6254656E616E745479706571007E00094C00076A6F625479706571007E00094C000A6D6574686F644E616D6571007E00094C00116D6574686F64506172616D7356616C756571007E00094C000D6D697366697265506F6C69637971007E00094C00086E65787454696D6571007E000A4C000C70726576696F757354696D6571007E000A4C000672656D61726B71007E00094C0009737461727454696D6571007E000A4C000874656E616E74496471007E000B4C0008757064617465427971007E00094C000A75706461746554696D6571007E000A78720035636F6D2E62616F6D69646F752E6D796261746973706C75732E657874656E73696F6E2E6163746976657265636F72642E4D6F64656C00000000000000010200007870740000707074000E3020302F31202A202A202A203F207400122F696E6E65722D6A6F622F7B706172616D7D71007E000E74000331313170740006E6B58BE8AF957071007E000E707400013371007E000E740002313174000133707071007E000E707070707800);

-- ----------------------------
-- Table structure for qrtz_locks
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_locks`;
CREATE TABLE `qrtz_locks`  (
  `sched_name` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `lock_name` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`sched_name`, `lock_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_locks
-- ----------------------------
INSERT INTO `qrtz_locks` VALUES ('clusteredScheduler', 'STATE_ACCESS');
INSERT INTO `qrtz_locks` VALUES ('clusteredScheduler', 'TRIGGER_ACCESS');

-- ----------------------------
-- Table structure for qrtz_paused_trigger_grps
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_paused_trigger_grps`;
CREATE TABLE `qrtz_paused_trigger_grps`  (
  `sched_name` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `trigger_group` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`sched_name`, `trigger_group`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_paused_trigger_grps
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_scheduler_state
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_scheduler_state`;
CREATE TABLE `qrtz_scheduler_state`  (
  `sched_name` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `instance_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `last_checkin_time` bigint NOT NULL,
  `checkin_interval` bigint NOT NULL,
  PRIMARY KEY (`sched_name`, `instance_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_scheduler_state
-- ----------------------------
INSERT INTO `qrtz_scheduler_state` VALUES ('clusteredScheduler', 'chenweigang1735631972733', 1735638719480, 10000);

-- ----------------------------
-- Table structure for qrtz_simple_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simple_triggers`;
CREATE TABLE `qrtz_simple_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `trigger_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `trigger_group` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `repeat_count` bigint NOT NULL,
  `repeat_interval` bigint NOT NULL,
  `times_triggered` bigint NOT NULL,
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `QRTZ_SIMPLE_TRIGGERS_IBFK_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_simple_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_simprop_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simprop_triggers`;
CREATE TABLE `qrtz_simprop_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `trigger_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `trigger_group` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `str_prop_1` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `str_prop_2` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `str_prop_3` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `int_prop_1` int NULL DEFAULT NULL,
  `int_prop_2` int NULL DEFAULT NULL,
  `long_prop_1` bigint NULL DEFAULT NULL,
  `long_prop_2` bigint NULL DEFAULT NULL,
  `dec_prop_1` decimal(13, 4) NULL DEFAULT NULL,
  `dec_prop_2` decimal(13, 4) NULL DEFAULT NULL,
  `bool_prop_1` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `bool_prop_2` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `QRTZ_SIMPROP_TRIGGERS_IBFK_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_simprop_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_triggers`;
CREATE TABLE `qrtz_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `trigger_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `trigger_group` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `job_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `job_group` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `description` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `next_fire_time` bigint NULL DEFAULT NULL,
  `prev_fire_time` bigint NULL DEFAULT NULL,
  `priority` int NULL DEFAULT NULL,
  `trigger_state` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `trigger_type` varchar(8) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `start_time` bigint NOT NULL,
  `end_time` bigint NULL DEFAULT NULL,
  `calendar_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `misfire_instr` smallint NULL DEFAULT NULL,
  `job_data` blob NULL,
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  INDEX `sched_name`(`sched_name`, `job_name`, `job_group`) USING BTREE,
  CONSTRAINT `qrtz_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `job_name`, `job_group`) REFERENCES `qrtz_job_details` (`sched_name`, `job_name`, `job_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_triggers
-- ----------------------------
INSERT INTO `qrtz_triggers` VALUES ('clusteredScheduler', '测试', '111', '测试', '111', NULL, 1735638780000, 1735638720000, 5, 'WAITING', 'CRON', 1735632261000, 0, NULL, 2, '');

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job`  (
  `job_id` bigint NOT NULL COMMENT '任务id',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务组名',
  `job_order` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '组内执行顺利，值越大执行优先级越高，最大值9，最小值1',
  `job_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '1、java类;2、spring bean名称;3、rest调用;4、jar调用;9其他',
  `execute_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'job_type=3时，rest调用地址，仅支持rest get协议,需要增加String返回值，0成功，1失败;job_type=4时，jar路径;其它值为空',
  `class_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'job_type=1时，类完整路径;job_type=2时，spring bean名称;其它值为空',
  `method_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '任务方法',
  `method_params_value` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '参数值',
  `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '3' COMMENT '错失执行策略（1错失周期立即执行 2错失周期执行一次 3下周期执行）',
  `job_tenant_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '1、多租户任务;2、非多租户任务',
  `job_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（1、未发布;2、运行中;3、暂停;4、删除;）',
  `job_execute_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1异常）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '初次执行时间',
  `previous_time` timestamp NULL DEFAULT NULL COMMENT '上次执行时间',
  `next_time` timestamp NULL DEFAULT NULL COMMENT '下次执行时间',
  `tenant_id` bigint NULL DEFAULT 1 COMMENT '租户',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '定时任务调度表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_job
-- ----------------------------
INSERT INTO `sys_job` VALUES (1874003647922233345, '测试', '111', '1', '3', 'http://localhost:8080/api/job/inner-job/11', NULL, NULL, '11', '0 0/1 * * * ? ', '3', '1', '1', '0', 'admin', '2024-12-31 16:04:22', NULL, '2024-12-31 16:43:33', NULL, NULL, NULL, 1, '');

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log`  (
  `job_log_id` bigint NOT NULL COMMENT '任务日志ID',
  `job_id` bigint NOT NULL COMMENT '任务id',
  `job_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务组名',
  `job_order` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '组内执行顺利，值越大执行优先级越高，最大值9，最小值1',
  `job_type` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '1' COMMENT '1、java类;2、spring bean名称;3、rest调用;4、jar调用;9其他',
  `execute_path` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'job_type=3时，rest调用地址，仅支持post协议;job_type=4时，jar路径;其它值为空',
  `class_name` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'job_type=1时，类完整路径;job_type=2时，spring bean名称;其它值为空',
  `method_name` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务方法',
  `method_params_value` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数值',
  `cron_expression` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'cron执行表达式',
  `job_message` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '日志信息',
  `job_log_status` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `execute_time` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '执行时间',
  `exception_info` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '异常信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `tenant_id` bigint NOT NULL DEFAULT 1 COMMENT '租户id',
  PRIMARY KEY (`job_log_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '定时任务执行日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_job_log
-- ----------------------------
INSERT INTO `sys_job_log` VALUES (1874003680155459586, 1874003647922233345, '测试', '111', '1', '3', '/inner-job/{param}', NULL, NULL, '11', '0 0/1 * * * ? ', '执行失败', '1', '33', '定时任务restTaskInvok业务执行失败,任务：/inner-job/{param}', '2024-12-31 16:04:29', 1);
INSERT INTO `sys_job_log` VALUES (1874003831934738433, 1874003647922233345, '测试', '111', '1', '3', '/inner-job/{param}', NULL, NULL, '11', '0 0/1 * * * ? ', '执行失败', '1', '6', '定时任务restTaskInvok业务执行失败,任务：/inner-job/{param}', '2024-12-31 16:05:05', 1);
INSERT INTO `sys_job_log` VALUES (1874003858774089729, 1874003647922233345, '测试', '111', '1', '3', '/inner-job/{param}', NULL, NULL, '11', '0 0/1 * * * ? ', '执行失败', '1', '4', '定时任务restTaskInvok业务执行失败,任务：/inner-job/{param}', '2024-12-31 16:05:12', 1);
INSERT INTO `sys_job_log` VALUES (1874013434282041346, 1874003647922233345, '测试', '111', '1', '3', 'http://localhost:8080/api/job/inner-job/11', NULL, NULL, '11', '0 0/1 * * * ? ', '执行成功', '0', '42', '', '2024-12-31 16:43:15', 1);
INSERT INTO `sys_job_log` VALUES (1874013510832283650, 1874003647922233345, '测试', '111', '1', '3', 'http://localhost:8080/api/job/inner-job/11', NULL, NULL, '11', '0 0/1 * * * ? ', '执行成功', '0', '16', '', '2024-12-31 16:43:33', 1);

-- ----------------------------
-- Table structure for xxl_job_group
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_group`;
CREATE TABLE `xxl_job_group`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `app_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '执行器AppName',
  `title` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '执行器名称',
  `address_type` tinyint NOT NULL DEFAULT 0 COMMENT '执行器地址类型：0=自动注册、1=手动录入',
  `address_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '执行器地址列表，多地址逗号分隔',
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of xxl_job_group
-- ----------------------------
INSERT INTO `xxl_job_group` VALUES (1, 'maintenance-server', 'IGW应用管理执行器', 1, 'http://localhost:8889/', '2025-01-15 11:22:27');

-- ----------------------------
-- Table structure for xxl_job_info
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_info`;
CREATE TABLE `xxl_job_info`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `job_group` int NOT NULL COMMENT '执行器主键ID',
  `job_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `add_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `author` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '作者',
  `alarm_email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报警邮件',
  `schedule_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'NONE' COMMENT '调度类型',
  `schedule_conf` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '调度配置，值含义取决于调度类型',
  `misfire_strategy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'DO_NOTHING' COMMENT '调度过期策略',
  `executor_route_strategy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '执行器路由策略',
  `executor_handler` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '执行器任务handler',
  `executor_param` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '执行器任务参数',
  `executor_block_strategy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '阻塞处理策略',
  `executor_timeout` int NOT NULL DEFAULT 0 COMMENT '任务执行超时时间，单位秒',
  `executor_fail_retry_count` int NOT NULL DEFAULT 0 COMMENT '失败重试次数',
  `glue_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'GLUE类型',
  `glue_source` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT 'GLUE源代码',
  `glue_remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'GLUE备注',
  `glue_updatetime` datetime NULL DEFAULT NULL COMMENT 'GLUE更新时间',
  `child_jobid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '子任务ID，多个逗号分隔',
  `trigger_status` tinyint NOT NULL DEFAULT 0 COMMENT '调度状态：0-停止，1-运行',
  `trigger_last_time` bigint NOT NULL DEFAULT 0 COMMENT '上次调度时间',
  `trigger_next_time` bigint NOT NULL DEFAULT 0 COMMENT '下次调度时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of xxl_job_info
-- ----------------------------
INSERT INTO `xxl_job_info` VALUES (3, 1, '监测任务列表心跳是否正常', '2025-01-15 11:23:04', '2025-01-16 16:45:35', 'admin', '<EMAIL>', 'CRON', '0 0/5 * * * ?', 'DO_NOTHING', 'FIRST', 'dwIgwApplicationOnline', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2025-01-15 11:23:04', '', 1, 1747918200000, 1747918500000);
INSERT INTO `xxl_job_info` VALUES (4, 1, '模拟第三方厂家发送心率', '2025-01-15 15:45:07', '2025-01-16 15:58:42', 'yjh', '<EMAIL>', 'CRON', '0 0/5 * * * ?', 'DO_NOTHING', 'FIRST', 'heartSendBatch', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2025-01-15 15:45:07', '', 1, 1747918200000, 1747918500000);

-- ----------------------------
-- Table structure for xxl_job_lock
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_lock`;
CREATE TABLE `xxl_job_lock`  (
  `lock_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '锁名称',
  PRIMARY KEY (`lock_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of xxl_job_lock
-- ----------------------------
INSERT INTO `xxl_job_lock` VALUES ('schedule_lock');

-- ----------------------------
-- Table structure for xxl_job_log
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_log`;
CREATE TABLE `xxl_job_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `job_group` int NOT NULL COMMENT '执行器主键ID',
  `job_id` int NOT NULL COMMENT '任务，主键ID',
  `executor_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '执行器地址，本次执行的地址',
  `executor_handler` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '执行器任务handler',
  `executor_param` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '执行器任务参数',
  `executor_sharding_param` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '执行器任务分片参数，格式如 1/2',
  `executor_fail_retry_count` int NOT NULL DEFAULT 0 COMMENT '失败重试次数',
  `trigger_time` datetime NULL DEFAULT NULL COMMENT '调度-时间',
  `trigger_code` int NOT NULL COMMENT '调度-结果',
  `trigger_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '调度-日志',
  `handle_time` datetime NULL DEFAULT NULL COMMENT '执行-时间',
  `handle_code` int NOT NULL COMMENT '执行-状态',
  `handle_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '执行-日志',
  `alarm_status` tinyint NOT NULL DEFAULT 0 COMMENT '告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `I_trigger_time`(`trigger_time`) USING BTREE,
  INDEX `I_handle_code`(`handle_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2242 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of xxl_job_log
-- ----------------------------
INSERT INTO `xxl_job_log` VALUES (2076, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 09:35:00', 500, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：500<br>msg：xxl-job remoting error(Connection refused: no further information), for url : http://localhost:8889/run', NULL, 0, NULL, 3);
INSERT INTO `xxl_job_log` VALUES (2077, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 09:35:00', 500, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：500<br>msg：xxl-job remoting error(Connection refused: no further information), for url : http://localhost:8889/run', NULL, 0, NULL, 3);
INSERT INTO `xxl_job_log` VALUES (2078, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 09:40:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 09:40:01', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2079, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 09:40:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 09:40:01', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2080, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 09:45:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 09:45:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2081, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 09:45:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 09:45:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2082, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 09:50:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 09:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2083, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 09:50:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 09:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2084, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 09:55:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 09:55:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2085, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 09:55:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 09:55:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2086, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 10:00:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:00:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2087, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 10:00:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:00:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2088, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 10:05:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:05:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2089, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 10:05:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:05:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2090, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 10:10:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:10:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2091, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 10:10:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:10:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2092, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 10:15:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:15:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2093, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 10:15:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:15:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2094, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 10:20:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:20:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2095, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 10:20:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:20:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2096, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 10:25:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:25:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2097, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 10:25:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:25:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2098, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 10:30:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:30:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2099, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 10:30:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:30:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2100, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 10:35:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:35:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2101, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 10:35:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:35:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2102, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 10:40:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:40:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2103, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 10:40:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:40:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2104, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 10:45:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:45:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2105, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 10:45:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:45:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2106, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 10:50:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2107, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 10:50:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2108, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 10:55:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:55:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2109, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 10:55:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 10:55:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2110, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 11:00:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 11:00:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2111, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 11:00:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 11:00:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2112, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 11:05:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 11:05:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2113, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 11:05:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 11:05:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2114, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 11:10:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 11:10:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2115, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 11:10:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 11:10:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2116, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 11:15:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 11:15:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2117, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 11:15:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 11:15:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2118, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 11:20:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 11:20:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2119, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 11:20:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 11:20:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2120, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 11:25:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 11:25:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2121, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 11:25:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 11:25:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2122, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 15:00:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:00:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2123, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 15:00:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:00:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2124, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 15:05:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:05:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2125, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 15:05:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:05:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2126, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 15:10:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:10:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2127, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 15:10:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:10:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2128, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 15:15:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:15:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2129, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 15:15:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:15:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2130, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 15:20:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:20:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2131, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 15:20:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:20:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2132, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 15:25:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:25:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2133, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 15:25:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:25:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2134, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 15:30:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:30:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2135, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 15:30:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:30:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2136, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 15:35:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:35:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2137, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 15:35:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:35:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2138, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 15:40:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:40:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2139, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 15:40:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:40:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2140, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 15:45:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:45:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2141, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 15:45:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:45:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2142, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 15:50:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2143, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 15:50:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2144, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 15:55:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:55:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2145, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 15:55:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 15:55:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2146, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 16:00:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 16:00:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2147, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 16:00:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 16:00:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2148, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 16:05:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 16:05:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2149, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 16:05:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 16:05:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2150, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 16:10:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 16:10:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2151, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 16:10:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 16:10:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2152, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 16:15:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 16:15:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2153, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 16:15:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 16:15:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2154, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 16:50:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 16:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2155, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 16:50:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 16:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2156, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 16:55:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 16:55:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2157, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 16:55:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 16:55:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2158, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 17:00:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:00:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2159, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 17:00:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:00:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2160, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 17:05:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:05:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2161, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 17:05:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:05:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2162, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 17:10:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:10:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2163, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 17:10:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:10:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2164, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 17:15:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:15:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2165, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 17:15:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:15:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2166, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 17:20:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:20:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2167, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 17:20:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:20:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2168, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 17:25:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:25:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2169, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 17:25:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:25:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2170, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 17:30:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:30:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2171, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 17:30:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:30:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2172, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 17:35:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:35:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2173, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 17:35:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:35:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2174, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 17:40:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:40:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2175, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 17:40:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:40:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2176, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 17:45:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:45:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2177, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 17:45:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:45:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2178, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 17:50:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2179, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 17:50:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2180, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 17:55:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:55:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2181, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 17:55:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 17:55:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2182, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 18:00:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 18:00:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2183, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 18:00:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 18:00:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2184, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 18:05:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 18:05:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2185, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 18:05:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 18:05:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2186, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 18:10:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 18:10:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2187, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 18:10:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 18:10:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2188, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 18:15:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 18:15:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2189, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 18:15:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 18:15:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2190, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 18:20:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 18:20:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2191, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 18:20:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 18:20:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2192, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 18:25:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 18:25:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2193, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 18:25:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 18:25:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2194, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 18:30:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 18:30:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2195, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 18:30:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 18:30:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2196, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 18:35:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 18:35:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2197, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 18:35:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 18:35:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2198, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 18:40:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 18:40:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2199, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 18:40:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 18:40:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2200, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 18:45:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 18:45:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2201, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 18:45:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 18:45:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2202, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 18:50:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 18:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2203, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 18:50:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 18:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2204, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 19:20:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 19:20:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2205, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 19:20:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 19:20:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2206, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 19:25:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 19:25:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2207, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 19:25:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 19:25:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2208, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 19:30:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 19:30:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2209, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 19:30:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 19:30:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2210, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 19:35:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 19:35:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2211, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 19:35:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 19:35:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2212, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 19:40:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 19:40:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2213, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 19:40:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 19:40:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2214, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 19:45:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 19:45:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2215, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 19:45:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 19:45:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2216, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 19:50:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 19:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2217, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 19:50:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 19:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2218, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 19:55:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 19:55:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2219, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 19:55:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 19:55:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2220, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 20:00:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 20:00:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2221, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 20:00:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 20:00:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2222, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 20:05:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 20:05:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2223, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 20:05:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 20:05:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2224, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 20:10:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 20:10:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2225, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 20:10:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 20:10:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2226, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 20:15:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 20:15:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2227, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 20:15:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 20:15:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2228, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 20:20:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 20:20:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2229, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 20:20:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 20:20:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2230, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 20:25:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 20:25:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2231, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 20:25:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 20:25:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2232, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 20:30:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 20:30:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2233, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 20:30:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 20:30:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2234, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 20:35:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 20:35:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2235, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 20:35:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 20:35:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2236, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 20:40:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 20:40:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2237, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 20:40:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 20:40:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2238, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 20:45:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 20:45:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2239, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 20:45:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 20:45:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2240, 1, 3, 'http://localhost:8889/', 'dwIgwApplicationOnline', '', NULL, 0, '2025-05-22 20:50:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 20:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (2241, 1, 4, 'http://localhost:8889/', 'heartSendBatch', '', NULL, 0, '2025-05-22 20:50:00', 200, '任务触发类型：Cron触发<br>调度机器：*************<br>执行器-注册方式：手动录入<br>执行器-地址列表：[http://localhost:8889/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://localhost:8889/<br>code：200<br>msg：null', '2025-05-22 20:50:00', 200, '', 0);

-- ----------------------------
-- Table structure for xxl_job_log_report
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_log_report`;
CREATE TABLE `xxl_job_log_report`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `trigger_day` datetime NULL DEFAULT NULL COMMENT '调度-时间',
  `running_count` int NOT NULL DEFAULT 0 COMMENT '运行中-日志数量',
  `suc_count` int NOT NULL DEFAULT 0 COMMENT '执行成功-日志数量',
  `fail_count` int NOT NULL DEFAULT 0 COMMENT '执行失败-日志数量',
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `i_trigger_day`(`trigger_day`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of xxl_job_log_report
-- ----------------------------
INSERT INTO `xxl_job_log_report` VALUES (1, '2024-11-05 00:00:00', 0, 0, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (2, '2024-11-04 00:00:00', 0, 0, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (3, '2024-11-03 00:00:00', 0, 0, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (4, '2025-01-14 00:00:00', 0, 0, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (5, '2025-01-13 00:00:00', 0, 0, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (6, '2025-01-12 00:00:00', 0, 0, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (7, '2025-01-15 00:00:00', 0, 0, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (8, '2025-01-16 00:00:00', 0, 0, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (9, '2025-01-17 00:00:00', 0, 90, 47, NULL);
INSERT INTO `xxl_job_log_report` VALUES (10, '2025-02-13 00:00:00', 0, 40, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (11, '2025-02-12 00:00:00', 0, 0, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (12, '2025-02-11 00:00:00', 0, 0, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (13, '2025-02-14 00:00:00', 0, 0, 36, NULL);
INSERT INTO `xxl_job_log_report` VALUES (14, '2025-03-05 00:00:00', 0, 76, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (15, '2025-03-04 00:00:00', 0, 0, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (16, '2025-03-03 00:00:00', 0, 0, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (17, '2025-03-07 00:00:00', 0, 152, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (18, '2025-03-06 00:00:00', 0, 0, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (19, '2025-03-13 00:00:00', 0, 144, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (20, '2025-03-12 00:00:00', 0, 0, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (21, '2025-03-11 00:00:00', 0, 0, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (22, '2025-05-22 00:00:00', 0, 164, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (23, '2025-05-21 00:00:00', 0, 0, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (24, '2025-05-20 00:00:00', 0, 0, 0, NULL);

-- ----------------------------
-- Table structure for xxl_job_logglue
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_logglue`;
CREATE TABLE `xxl_job_logglue`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `job_id` int NOT NULL COMMENT '任务，主键ID',
  `glue_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'GLUE类型',
  `glue_source` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT 'GLUE源代码',
  `glue_remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'GLUE备注',
  `add_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of xxl_job_logglue
-- ----------------------------

-- ----------------------------
-- Table structure for xxl_job_registry
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_registry`;
CREATE TABLE `xxl_job_registry`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `registry_group` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `registry_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `registry_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `i_g_k_v`(`registry_group`, `registry_key`, `registry_value`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of xxl_job_registry
-- ----------------------------
INSERT INTO `xxl_job_registry` VALUES (1, 'EXECUTOR', 'xxl-job-executor-sample', 'http://*************:8889/', '2025-01-14 18:20:44');
INSERT INTO `xxl_job_registry` VALUES (7, 'EXECUTOR', 'electric-xxl-job-executor-handler', 'http://*************:8889/', '2025-05-22 20:52:44');

-- ----------------------------
-- Table structure for xxl_job_user
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_user`;
CREATE TABLE `xxl_job_user`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '账号',
  `password` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码',
  `role` tinyint NOT NULL COMMENT '角色：0-普通用户、1-管理员',
  `permission` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '权限：执行器ID列表，多个逗号分割',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `i_username`(`username`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of xxl_job_user
-- ----------------------------
INSERT INTO `xxl_job_user` VALUES (1, 'admin', 'e10adc3949ba59abbe56e057f20f883e', 1, NULL);

SET FOREIGN_KEY_CHECKS = 1;
