package com.operation.electric.work.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.operation.electric.admin.api.entity.SysFile;
import com.operation.electric.admin.api.feign.RemoteFileService;
import com.operation.electric.common.core.util.R;
import com.operation.electric.work.entity.KnowledgeBaseEntity;
import com.operation.electric.work.mapper.KnowledgeBaseMapper;
import com.operation.electric.work.service.KnowledgeBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 知识库表(未入库的知识库文档)
 *
 * <AUTHOR>
 * @date 2024-03-12 16:02:18
 */

@Service
public class KnowledgeBaseServiceImpl extends ServiceImpl<KnowledgeBaseMapper, KnowledgeBaseEntity> implements KnowledgeBaseService {

    @Autowired
    private RemoteFileService remoteFileService;

    @Override
    public R storageByIds(Long id, String chatServerPath, String knowledgeBaseName) {
        List<String> fileNames = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        KnowledgeBaseEntity knowledgeBaseEntity = baseMapper.selectById(id);
        fileNames.add(knowledgeBaseEntity.getFileName());
        map.put("fileName", knowledgeBaseEntity.getFileName());
        map.put("maxLength", knowledgeBaseEntity.getMaxLength());
        map.put("coincidentLength", knowledgeBaseEntity.getCoincidentLength());
        map.put("zhTitleEnhance", knowledgeBaseEntity.getZhTitleEnhance());

        JSONObject entries = remoteFileService.bePutInStorage(map, chatServerPath, knowledgeBaseName);
        //入库成功之后改变未入库的知识库数据状态
        System.out.println(entries.get("code"));
        if (entries != null && "200".equals(entries.get("code").toString())) {
            LambdaUpdateWrapper<KnowledgeBaseEntity> objectLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            objectLambdaUpdateWrapper.eq(KnowledgeBaseEntity::getId, id);
            objectLambdaUpdateWrapper.set(KnowledgeBaseEntity::getStatus, 1);
            baseMapper.update(objectLambdaUpdateWrapper);
            return R.ok();
        } else {
            return R.failed();
        }
    }

    @Override
    public boolean delFile(ArrayList<Long> list, String chatServerPath, String knowledgeBaseName) {
        List<String> fileNames = new ArrayList<>();
        list.forEach((id) -> {
            KnowledgeBaseEntity knowledgeBaseEntity = baseMapper.selectById(id);
            fileNames.add(knowledgeBaseEntity.getFileName());

        });
        //调用远程知识库的删除接口
        String url = chatServerPath + "/knowledge_base/delete_docs";
        Map<String, Object> paramMap = new HashMap();
        paramMap.put("knowledge_base_name", knowledgeBaseName);
        paramMap.put("file_names", fileNames);
        paramMap.put("delete_content", false);
        paramMap.put("not_refresh_vs_cache", false);
        System.out.println("paramMap"+paramMap);
//        String result = HttpUtil.post(url, paramMap);
        //链式构建请求
        String result = HttpRequest.post(url)
                .body(JSON.toJSON(paramMap).toString())//表单内容
                .timeout(20000)//超时，毫秒
                .execute().body();
        System.out.println("result" + result);
        JSONObject entries = JSONUtil.parseObj(result);
        if (entries != null && "200".equals(entries.get("code").toString())) {
            return this.removeBatchByIds(list);
        } else {
            return false;
        }


    }
}
