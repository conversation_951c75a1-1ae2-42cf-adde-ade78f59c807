package com.operation.electric.yykbLog.logRest.v2.vm;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@Schema(title = "应用系统员工信息")
public class StaffVm implements Serializable {


    private Integer id;

    /**
     * 员工编码
     */

    @NotBlank(message = "OA账号不能为空")
    @Length(min = 1, max = 50, message = "OA账号不能超过50个字符")
    @Schema(name = "OA账号", title = "OA账号编码", description = "长度为1-50个字符", required = true, example = "zhangxs")
    private String staffNo;

    /**
     * 员工姓名
     */
    @Schema(name = "OA账号昵称", title = "OA账号昵称", description = "OA账号昵称", required = true, example = "张三")
    private String staffNm;

    /**
     * 标准单位编码
     */
    @NotBlank(message = "标准单位编码不能为空")
    @Schema(name = "标准单位编码", title = "标准单位编码", description = "标准单位编码", required = true, example = "430101")
    private String stdOrgNo;

    /**
     * 标准单位名称
     */
    @Schema(name = "标准单位名称", title = "标准单位名称", description = "标准单位名称", required = true, example = "湖南省")
    @NotBlank(message = "标准单位名称不能为空")
    private String stdOrgNm;

    /**
     * 标准部门编码
     */
    @NotBlank(message = "标准部门编码不能为空")
    @Schema(name = "标准部门编码", title = "标准部门编码", description = "标准部门编码", required = true, example = "01000")
    private String stdDeptNo;

    /**
     * 标准部门名称
     */
    @Schema(name = "标准部门名称", title = "标准部门名称", description = "标准部门名称", required = true, example = "发展部")
    @NotBlank(message = "标准部门名称不能为空")
    private String stdDeptNm;

    /**
     * 标准班组编码
     */
    @Schema(name = "标准班组编码", title = "标准班组编码", description = "标准班组编码", required = true, example = "12002")
    @NotBlank(message = "标准班组编码不能为空")
    private String stdTeamNo;

    /**
     * 标准班组名称
     */
    @Schema(name = "标准班组名称", title = "标准班组名称", description = "标准班组名称", required = true, example = "营销一班")
    @NotBlank(message = "标准班组名称不能为空")
    private String stdTeamNm;

    /**
     * 系统编号
     */

    /**
     * 系统名称
     */
    @Schema(name = "系统名称", title = "系统名称", description = "系统名称", required = false, example = "长沙电网线变户停电全感知")
    private String sysName;

    /**
     * 系统编号
     */
    @Schema(name = "系统编号", title = "系统编号", description = "系统编号长度必须为5位", required = false, example = "10001")
    private String sysId;

}
