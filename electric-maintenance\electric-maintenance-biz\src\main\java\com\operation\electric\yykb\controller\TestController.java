package com.operation.electric.yykb.controller;

import com.operation.electric.yykb.jobhandler.CleanUnlinkImageJob;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: zhuoxaing.peng
 * @Date: 2025-06-24 17:26
 * @description:
 * @Version: 1.0
 */
@RestController
@RequestMapping("/test" )
public class TestController {


	@Autowired
	private CleanUnlinkImageJob cleanUnlinkImageJob;


	@GetMapping("/cleanUnlinkedImages")
	public void cleanUnlinkedImages() {
		cleanUnlinkImageJob.cleanUnlinkedImages();
	}




}
