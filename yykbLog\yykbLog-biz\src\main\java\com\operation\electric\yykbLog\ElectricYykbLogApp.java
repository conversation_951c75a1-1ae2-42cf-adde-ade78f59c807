package com.operation.electric.yykbLog;

import com.operation.electric.common.feign.annotation.EnableElectricFeignClients;
import com.operation.electric.common.security.annotation.EnableElectricResourceServer;
import com.operation.electric.common.swagger.annotation.EnableOpenApi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Primary;

/**
 * <AUTHOR> archetype
 * <p>
 * 项目启动类
 */
@EnableOpenApi("yykbLog")
@EnableElectricFeignClients
@EnableDiscoveryClient
@EnableElectricResourceServer
@SpringBootApplication
public class ElectricYykbLogApp {
    public static void main(String[] args) {
        SpringApplication.run(ElectricYykbLogApp.class, args);
    }
}
