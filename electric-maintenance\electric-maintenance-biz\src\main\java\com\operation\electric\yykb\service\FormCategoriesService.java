package com.operation.electric.yykb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.operation.electric.common.core.util.R;
import com.operation.electric.yykb.entity.FormCategoriesEntity;
import com.operation.electric.yykb.entity.FormCategoryNode;

import java.util.List;

public interface FormCategoriesService extends IService<FormCategoriesEntity> {


	List<FormCategoryNode> buidTreeList();

	R diySortList(FormCategoryNode formCategoryNode);

	R deleteNode(Integer id);

    List<FormCategoriesEntity> getChildInfoById(Integer id);
}