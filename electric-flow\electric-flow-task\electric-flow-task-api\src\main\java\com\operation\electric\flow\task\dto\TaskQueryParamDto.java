package com.operation.electric.flow.task.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户任务查询参数
 */
@Data
public class TaskQueryParamDto {

	/**
	 * 任务执行人
	 */
	private String assign;

	/**
	 * 页码
	 */
	private Integer pageNum;

	/**
	 * 每页的数量
	 */
	private Integer pageSize;

	/**
	 * 流程名称
	 */
	private String processName;

	/**
	 * 任务时间
	 */
	private LocalDateTime[] taskTime;

	/**
	 * 状态
	 */
	private Integer status;

	/**
	 * 流程id
	 */
	private String flowId;

	/**
	 * 组名称
	 */
	private String groupName;

	/**
	 * 表单信息
	 */
	private String formData;

}
