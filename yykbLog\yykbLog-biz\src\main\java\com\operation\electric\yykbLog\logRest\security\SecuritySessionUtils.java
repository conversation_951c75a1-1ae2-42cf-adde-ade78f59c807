package com.operation.electric.yykbLog.logRest.security;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


public class SecuritySessionUtils {
    public static String getSysId() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        HttpServletRequest request = requestAttributes
                .getRequest();
        if(request==null){
            return null;
        }
        return request.getHeader("SYS-ID");
    }

    public static String getIp(){
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        HttpServletRequest request = requestAttributes
                .getRequest();
        if(request==null){
            return "";
        }
        return request.getRemoteAddr();
    }
}
