package com.operation.electric.yykbLog.logRest.v1;

import com.operation.electric.common.security.annotation.Inner;
import com.operation.electric.yykbLog.logRest.security.ApiOptionAuth;
import com.operation.electric.yykbLog.logRest.v1.vm.StaffVm;
import com.operation.electric.yykbLog.response.RestResponseData;
import com.operation.electric.yykbLog.service.LogStaffService;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Inner(false)
@RestController
@RequestMapping("/api/log/staffs")
@AllArgsConstructor
@Tag(name = "应用系统员工信息", description = "用来管理应用系统的员工信息")
public class StaffResource {

    private final LogStaffService staffService;

    @PostMapping("")
    @ApiOptionAuth(required = false)
    @Schema(name = "保存应用系统员工信息", description = "保存应用系统员工信息")
    @Tag(name = "v1")
    public RestResponseData<StaffVm> create(@RequestBody @Valid StaffVm vm) {
        StaffVm staffVm = staffService.addStaff(vm);
        return RestResponseData.success(staffVm);
    }

}
