package com.operation.electric.yykbLog.exception;

import com.operation.electric.yykbLog.response.RestResponseData;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestControllerAdvice
@Order(-101)
public class YykbLogExceptionHandler {
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public RestResponseData methodArgumentNotValidException(MethodArgumentNotValidException e) {
        List<String> collect = e.getBindingResult().getAllErrors().stream().map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.toList());
        return RestResponseData.error(HttpStatus.BAD_REQUEST.value(), String.join("。\r\n", collect));
    }

    @ExceptionHandler(value = StaffNotFoundException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public RestResponseData staffNotFoundException(StaffNotFoundException e) {
        return RestResponseData.error(HttpStatus.BAD_REQUEST.value(), e.getMessage());
    }
    @ExceptionHandler(value = StaffExistException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public RestResponseData staffExistException(StaffExistException e) {
        return RestResponseData.error(HttpStatus.BAD_REQUEST.value(), e.getMessage());
    }

    @ExceptionHandler(value = SysInfoAuthError.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public RestResponseData sysInfoAuthError(SysInfoAuthError e) {
        return RestResponseData.error(HttpStatus.UNAUTHORIZED.value(), e.getMessage());
    }
}
