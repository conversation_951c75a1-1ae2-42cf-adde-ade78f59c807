package com.operation.electric.yykb.entity;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Name:ticket-center-api
 * User: yjh
 * Date: 2024/12/13
 * Time: 14:30
 * Description:
 */
@Data
public class FormCategoryNode {
	private int id;           // 分类ID
	private String name;      // 分类名称
	private String description; // 分类描述
	private int parentId;     // 父分类ID
	private int sortOrder;    // 排序
	private List<FormCategoryNode> children = new ArrayList<>(); // 子分类列表
}
