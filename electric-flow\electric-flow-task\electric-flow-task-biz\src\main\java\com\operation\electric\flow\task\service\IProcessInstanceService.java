package com.operation.electric.flow.task.service;

import com.operation.electric.common.core.util.R;
import com.operation.electric.flow.task.dto.ProcessInstanceParamDto;
import com.operation.electric.flow.task.dto.TaskQueryParamDto;
import com.operation.electric.flow.task.vo.NodeFormatParamVo;

/**
 * 流程实例进程
 */
public interface IProcessInstanceService {

	/**
	 * 启动流程
	 * @param processInstanceParamDto
	 * @return
	 */
	R startProcessInstance(ProcessInstanceParamDto processInstanceParamDto);

	/**
	 * 查询当前登录用户的待办任务
	 * @param taskQueryParamDto taskQueryParamDto
	 * @return
	 */
	R queryMineTask(TaskQueryParamDto taskQueryParamDto);

	/**
	 * 查询已办任务
	 * @param taskQueryParamDto
	 * @return
	 */
	R queryMineEndTask(TaskQueryParamDto taskQueryParamDto);

	/**
	 * 流程结束
	 * @param processsInstanceId
	 * @return
	 */
	R end(String processsInstanceId);

	/**
	 * 查询我发起的
	 * @param taskQueryParamDto
	 * @return
	 */
	R queryMineStarted(TaskQueryParamDto taskQueryParamDto);

	/**
	 * 查询抄送给我的
	 * @param taskQueryParamDto
	 * @return
	 */
	R queryMineCC(TaskQueryParamDto taskQueryParamDto);

	/**
	 * 格式化流程显示
	 * @param nodeFormatParamVo
	 * @return
	 */
	R formatStartNodeShow(NodeFormatParamVo nodeFormatParamVo);

	/**
	 * 流程详情
	 * @param processInstanceId
	 * @return
	 */
	R detail(String processInstanceId);

	/**
	 * 删除流程信息
	 * @param ids
	 * @return
	 */
	R deleteProcessInstanceByIds(Long[] ids);

	/**
	 *
	 * @param processInstanceId
	 * @return
	 */
	R editDetail(String processInstanceId);

	/**
	 * 修改流程表单数据
	 * @param processInstanceParamDto
	 * @return
	 */
	R updateFormData(ProcessInstanceParamDto processInstanceParamDto);

	R<String> getWorkFolwId(String workFolwName);
}
