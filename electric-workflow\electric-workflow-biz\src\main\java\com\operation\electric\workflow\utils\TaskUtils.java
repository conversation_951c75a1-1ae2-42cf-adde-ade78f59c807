package com.operation.electric.workflow.utils;

import cn.hutool.core.util.ObjectUtil;
import com.operation.electric.admin.api.entity.SysRole;
import com.operation.electric.admin.api.feign.RemoteRoleService;
import com.operation.electric.common.core.util.SpringContextHolder;
import com.operation.electric.common.security.service.ElectricUser;
import com.operation.electric.common.security.util.SecurityUtils;
import com.operation.electric.workflow.constant.TaskConstants;

import java.util.ArrayList;
import java.util.List;

/**
 * 工作流任务工具类
 *
 * <AUTHOR>
 * @createTime 2022/4/24 12:42
 */
public class TaskUtils {

    public static String getUserId() {
//        return String.valueOf(LoginHelper.getUserId());
        return String.valueOf( SecurityUtils.getUser().getId());
    }

    /**
     * 获取用户组信息
     *
     * @return candidateGroup
     */
    public static List<String> getCandidateGroup() {
        RemoteRoleService remoteRoleService = SpringContextHolder.getBean(RemoteRoleService.class);
        List<String> list = new ArrayList<>();
//        LoginUser user = LoginHelper.getLoginUser();
        ElectricUser user = SecurityUtils.getUser();
        if (ObjectUtil.isNotNull(user)) {
            //查询用户拥有的角色
            List<SysRole> roles = remoteRoleService.selectRoleByUserId(user.getId()).getData();
            if (ObjectUtil.isNotEmpty(roles)) {
                roles.forEach(role -> list.add(TaskConstants.ROLE_GROUP_PREFIX + role.getRoleId()));
            }
//            if (ObjectUtil.isNotEmpty(user.getRoles())) {
//                user.getRoles().forEach(role -> list.add(TaskConstants.ROLE_GROUP_PREFIX + role.getRoleId()));
//            }
            if (ObjectUtil.isNotNull(user.getDeptId())) {
                list.add(TaskConstants.DEPT_GROUP_PREFIX + user.getDeptId());
            }
        }
        return list;
    }
}
