<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.operation.electric.app.mapper.AppArticleMapper">

  <resultMap id="appArticleMap" type="com.operation.electric.app.api.entity.AppArticleEntity">
        <id property="id" column="id"/>
        <result property="cid" column="cid"/>
        <result property="title" column="title"/>
        <result property="intro" column="intro"/>
        <result property="summary" column="summary"/>
        <result property="image" column="image"/>
        <result property="content" column="content"/>
        <result property="author" column="author"/>
        <result property="visit" column="visit"/>
        <result property="sort" column="sort"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
  </resultMap>
</mapper>
