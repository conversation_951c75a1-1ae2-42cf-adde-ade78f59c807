package com.operation.electric.yykb.controller;

import cn.hutool.core.bean.BeanUtil;
import com.operation.electric.common.core.util.R;
import com.operation.electric.common.security.annotation.Inner;
import com.operation.electric.yykb.entity.HeartMessage;
import com.operation.electric.yykb.entity.LogSysInfo;
import com.operation.electric.yykb.service.HeartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 【心跳检测控制类】
 *
 * <AUTHOR>
 * @date 2024/11/07
 */
@Inner(value = false)
@RestController
@RequestMapping("/heart")
public class HeartController {

    @Autowired
    private HeartService heartService;

    @GetMapping("/check")
    public R checkHeart(@RequestParam HeartMessage heartMessage){
        if (BeanUtil.isEmpty(heartMessage)){
            return R.failed("参数为空");
        }
        Boolean flag = heartService.checkHeart(heartMessage);
        return flag?R.ok():R.failed();
    }

}
