/*
 *
 *      Copyright (c) 2018-2025, electric All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: electric
 *
 */

package com.operation.electric.admin.api.feign;

import cn.hutool.json.JSONObject;
import com.operation.electric.admin.api.dto.SysLogDTO;
import com.operation.electric.admin.api.entity.SysFile;
import com.operation.electric.common.core.constant.SecurityConstants;
import com.operation.electric.common.core.constant.ServiceNameConstants;
import com.operation.electric.common.core.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@FeignClient(name = ServiceNameConstants.UPMS_SERVICE)
public interface RemoteFileService {
	@GetMapping("/sys-file/getByFileName/{fileName}")
	 R<SysFile> getByFileName(@PathVariable("fileName") String fileName);

	@PostMapping("/sys-file/bePutInStorage")
	JSONObject bePutInStorage(@RequestBody Map<String,Object> map, @RequestParam("chatServerPath") String chatServerPath,
							  @RequestParam("knowledgeBaseName")String knowledgeBaseName);
}
