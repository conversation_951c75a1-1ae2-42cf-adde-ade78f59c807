-- 该脚本不要直接执行， 注意维护菜单的父节点ID 默认 父节点-1 , 默认租户 1

-- 菜单SQL
insert into sys_menu ( menu_id,parent_id, path, permission, menu_type, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50000, '-1', '/yykbLog/logDetail/index', '', '0', 'icon-bangzhushouji', '0', null , '8', null , '日志系统，系统使用详情日志记录信息管理', 1);

-- 菜单对应按钮SQL
insert into sys_menu ( menu_id,parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50001,50000, 'yykbLog_logDetail_view', '1', null, '1',  '0', null, '0', null, '日志系统，系统使用详情日志记录信息查看', 1);

insert into sys_menu ( menu_id,parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50002,50000, 'yykbLog_logDetail_add', '1', null, '1',  '0', null, '1', null, '日志系统，系统使用详情日志记录信息新增', 1);

insert into sys_menu (menu_id, parent_id, permission, menu_type, path, icon,  del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50003,50000, 'yykbLog_logDetail_edit', '1', null, '1',  '0', null, '2', null, '日志系统，系统使用详情日志记录信息修改', 1);

insert into sys_menu (menu_id, parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50004,50000, 'yykbLog_logDetail_del', '1', null, '1',  '0', null, '3', null, '日志系统，系统使用详情日志记录信息删除', 1);

insert into sys_menu ( menu_id,parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50005,50000, 'yykbLog_logDetail_export', '1', null, '1',  '0', null, '3', null, '导入导出', 1);