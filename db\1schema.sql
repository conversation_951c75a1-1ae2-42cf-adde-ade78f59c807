-- electric 核心表
create database `electricx` default character set utf8mb4 collate utf8mb4_general_ci;

-- electric 工作流相关库
create database `electricx_flow` default character set utf8mb4 collate utf8mb4_general_ci;

-- electric 任务相关库
create database `electricx_job` default character set utf8mb4 collate utf8mb4_general_ci;

-- electric 公众号管理相关库
create database `electricx_mp` default character set utf8mb4 collate utf8mb4_general_ci;

-- electric nacos配置相关库
create database `electricx_config` default character set utf8mb4 collate utf8mb4_general_ci;

-- electric pay配置相关库
create database `electricx_pay` default character set utf8mb4 collate utf8mb4_general_ci;

-- electric codegen相关库
create database `electricx_codegen` default character set utf8mb4 collate utf8mb4_general_ci;

-- electric report相关库
create database `electricx_report` default character set utf8mb4 collate utf8mb4_general_ci;

-- electric bi 报表相关的数据库
create database `electricx_bi` default character set utf8mb4 collate utf8mb4_general_ci;

-- electric app 模块相关的数据库
create database `electricx_app` default character set utf8mb4 collate utf8mb4_general_ci;
