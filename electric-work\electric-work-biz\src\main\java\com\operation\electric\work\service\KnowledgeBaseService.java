package com.operation.electric.work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.operation.electric.common.core.util.R;
import com.operation.electric.work.entity.KnowledgeBaseEntity;

import java.util.ArrayList;

public interface KnowledgeBaseService extends IService<KnowledgeBaseEntity> {

    R storageByIds(Long id, String chatServerPath,String knowledgeBaseName);

    boolean delFile(ArrayList<Long> list, String chatServerPath,String knowledgeBaseName);
}
