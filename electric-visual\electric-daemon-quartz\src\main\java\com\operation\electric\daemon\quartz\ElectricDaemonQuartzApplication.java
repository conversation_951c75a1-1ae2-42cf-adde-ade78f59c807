package com.operation.electric.daemon.quartz;

import com.operation.electric.common.feign.annotation.EnableElectricFeignClients;
import com.operation.electric.common.security.annotation.EnableElectricResourceServer;
import com.operation.electric.common.swagger.annotation.EnableOpenApi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * <AUTHOR>
 * @date 2019/01/23 定时任务模块
 */
@EnableOpenApi("job")
@EnableElectricFeignClients
@EnableElectricResourceServer
@EnableDiscoveryClient
@SpringBootApplication
public class ElectricDaemonQuartzApplication {

	public static void main(String[] args) {
		SpringApplication.run(ElectricDaemonQuartzApplication.class, args);
	}

}
