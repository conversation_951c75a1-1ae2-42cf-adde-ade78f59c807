package com.operation.electric.yykb.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.operation.electric.common.core.util.R;
import com.operation.electric.common.log.annotation.SysLog;
import com.operation.electric.yykb.entity.DwIgwPhotoEntity;
import com.operation.electric.yykb.service.DwIgwPhotoService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.operation.electric.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 问题反馈图片表
 *
 * <AUTHOR>
 * @date 2025-06-20 12:01:49
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/dwIgwPhoto" )
@Tag(description = "dwIgwPhoto" , name = "问题反馈图片表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class DwIgwPhotoController {

    private final DwIgwPhotoService dwIgwPhotoService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param dwIgwPhoto 问题反馈图片表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('electric_dwIgwPhoto_view')" )
    public R getDwIgwPhotoPage(@ParameterObject Page page, @ParameterObject DwIgwPhotoEntity dwIgwPhoto) {
        LambdaQueryWrapper<DwIgwPhotoEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(dwIgwPhotoService.page(page, wrapper));
    }


    /**
     * 通过id查询问题反馈图片表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('electric_dwIgwPhoto_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(dwIgwPhotoService.getById(id));
    }

    /**
     * 新增问题反馈图片表
     * @param dwIgwPhoto 问题反馈图片表
     * @return R
     */
    @Operation(summary = "新增问题反馈图片表" , description = "新增问题反馈图片表" )
    @SysLog("新增问题反馈图片表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('electric_dwIgwPhoto_add')" )
    public R save(@RequestBody DwIgwPhotoEntity dwIgwPhoto) {
        return R.ok(dwIgwPhotoService.save(dwIgwPhoto));
    }

    /**
     * 修改问题反馈图片表
     * @param dwIgwPhoto 问题反馈图片表
     * @return R
     */
    @Operation(summary = "修改问题反馈图片表" , description = "修改问题反馈图片表" )
    @SysLog("修改问题反馈图片表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('electric_dwIgwPhoto_edit')" )
    public R updateById(@RequestBody DwIgwPhotoEntity dwIgwPhoto) {
        return R.ok(dwIgwPhotoService.updateById(dwIgwPhoto));
    }

    /**
     * 通过id删除问题反馈图片表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除问题反馈图片表" , description = "通过id删除问题反馈图片表" )
    @SysLog("通过id删除问题反馈图片表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('electric_dwIgwPhoto_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(dwIgwPhotoService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param dwIgwPhoto 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('electric_dwIgwPhoto_export')" )
    public List<DwIgwPhotoEntity> export(DwIgwPhotoEntity dwIgwPhoto,Long[] ids) {
        return dwIgwPhotoService.list(Wrappers.lambdaQuery(dwIgwPhoto).in(ArrayUtil.isNotEmpty(ids), DwIgwPhotoEntity::getId, ids));
    }
}