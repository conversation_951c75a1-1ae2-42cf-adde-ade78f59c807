package com.operation.electric.yykbLog.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 日志系统，系统使用详情日志记录信息
 *
 * <AUTHOR>
 * @date 2024-03-21 16:57:27
 */
@Data
@TableName("log_detail")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "日志系统，系统使用详情日志记录信息")
public class LogDetailEntity extends Model<LogDetailEntity> {


	/**
	* 日志记录唯一编号UUID
	*/
    @TableId(type = IdType.ASSIGN_UUID)
    @Schema(description="日志记录唯一编号UUID")
    private String id;

	/**
	* 数据记录时间
	*/
    @Schema(description="数据记录时间")
	@TableField(fill = FieldFill.INSERT)
    private Date createdAt;

	/**
	* 数据更新时间
	*/
    @Schema(description="数据更新时间")
	@TableField(fill = FieldFill.UPDATE)
    private Date updatedAt;

	/**
	* 员工编码
	*/
    @Schema(description="员工编码")
    private String staffNo;

	/**
	* 操作时间
	*/
    @Schema(description="操作时间")
    private LocalDateTime timestamp;

	/**
	* 一级操作菜单
	*/
    @Schema(description="一级操作菜单")
    private String operationClass1;

	/**
	* 二级操作标签
	*/
    @Schema(description="二级操作标签")
    private String operationClass2;

	/**
	* 三级操作标签
	*/
    @Schema(description="三级操作标签")
    private String operationClass3;

	/**
	* 四级操作标签
	*/
    @Schema(description="四级操作标签")
    private String operationClass4;

	/**
	* 五级操作标签
	*/
    @Schema(description="五级操作标签")
    private String operationClass5;

	/**
	* IP地址
	*/
    @Schema(description="IP地址")
    private String ip;

	/**
	* 系统编号
	*/
    @Schema(description="系统编号")
    private String sysId;

	/**
	* 系统名称
	*/
    @Schema(description="系统名称")
    private String sysName;

	/**
	* 接入日志系统 的第三方应用/系统的版本
	*/
    @Schema(description="接入日志系统 的第三方应用/系统的版本")
    private String sysVersion;

	/**
	* 在线时间
	*/
    @Schema(description="在线时间")
    private BigDecimal onlineTime;

	/**
	* 重要用户标记
	*/
    @Schema(description="重要用户标记")
    private Integer vipFlag;
}