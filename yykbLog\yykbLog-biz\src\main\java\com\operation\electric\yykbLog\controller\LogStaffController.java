package com.operation.electric.yykbLog.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.operation.electric.common.core.util.R;
import com.operation.electric.common.log.annotation.SysLog;
import com.operation.electric.yykbLog.entity.LogStaffEntity;
import com.operation.electric.yykbLog.service.LogStaffService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.operation.electric.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 日志系统人员基础档案表
 *
 * <AUTHOR>
 * @date 2024-03-21 16:54:37
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/logStaff" )
@Tag(description = "logStaff" , name = "日志系统人员基础档案表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class LogStaffController {

    private final  LogStaffService logStaffService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param logStaff 日志系统人员基础档案表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('yykbLog_logStaff_view')" )
    public R getLogStaffPage(@ParameterObject Page page, @ParameterObject LogStaffEntity logStaff) {
        LambdaQueryWrapper<LogStaffEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.like(StrUtil.isNotBlank(logStaff.getStaffNo()),LogStaffEntity::getStaffNo,logStaff.getStaffNo());
		wrapper.like(StrUtil.isNotBlank(logStaff.getStaffNm()),LogStaffEntity::getStaffNm,logStaff.getStaffNm());
		wrapper.like(StrUtil.isNotBlank(logStaff.getStdDeptNo()),LogStaffEntity::getStdDeptNo,logStaff.getStdDeptNo());
		wrapper.like(StrUtil.isNotBlank(logStaff.getSysId()),LogStaffEntity::getSysId,logStaff.getSysId());
		wrapper.like(StrUtil.isNotBlank(logStaff.getSysName()),LogStaffEntity::getSysName,logStaff.getSysName());
        return R.ok(logStaffService.page(page, wrapper));
    }


    /**
     * 通过id查询日志系统人员基础档案表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('yykbLog_logStaff_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(logStaffService.getById(id));
    }

    /**
     * 新增日志系统人员基础档案表
     * @param logStaff 日志系统人员基础档案表
     * @return R
     */
    @Operation(summary = "新增日志系统人员基础档案表" , description = "新增日志系统人员基础档案表" )
    @SysLog("新增日志系统人员基础档案表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('yykbLog_logStaff_add')" )
    public R save(@RequestBody LogStaffEntity logStaff) {
        return R.ok(logStaffService.save(logStaff));
    }

    /**
     * 修改日志系统人员基础档案表
     * @param logStaff 日志系统人员基础档案表
     * @return R
     */
    @Operation(summary = "修改日志系统人员基础档案表" , description = "修改日志系统人员基础档案表" )
    @SysLog("修改日志系统人员基础档案表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('yykbLog_logStaff_edit')" )
    public R updateById(@RequestBody LogStaffEntity logStaff) {
        return R.ok(logStaffService.updateById(logStaff));
    }

    /**
     * 通过id删除日志系统人员基础档案表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除日志系统人员基础档案表" , description = "通过id删除日志系统人员基础档案表" )
    @SysLog("通过id删除日志系统人员基础档案表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('yykbLog_logStaff_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(logStaffService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param logStaff 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('yykbLog_logStaff_export')" )
    public List<LogStaffEntity> export(LogStaffEntity logStaff,Integer[] ids) {
        return logStaffService.list(Wrappers.lambdaQuery(logStaff).in(ArrayUtil.isNotEmpty(ids), LogStaffEntity::getId, ids));
    }
}