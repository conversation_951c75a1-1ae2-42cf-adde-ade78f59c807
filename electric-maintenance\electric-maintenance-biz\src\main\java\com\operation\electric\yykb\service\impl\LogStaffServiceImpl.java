package com.operation.electric.yykb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.operation.electric.yykb.entity.LogStaff;
import com.operation.electric.yykb.entity.TreeNode;
import com.operation.electric.yykb.mapper.LogStaffMapper;
import com.operation.electric.yykb.service.LogStaffService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 【】
 *
 * <AUTHOR>
 * @date 2024/11/07
 */
@Service
public class LogStaffServiceImpl extends ServiceImpl<LogStaffMapper, LogStaff> implements LogStaffService {
    @Autowired
    private LogStaffMapper logStaffMapper;
    @Override
    public List<TreeNode> selectOrgOne() {
        LambdaQueryWrapper<LogStaff> lqw = new LambdaQueryWrapper<>();
        lqw.groupBy(LogStaff::getStdOrgNo,LogStaff::getStdOrgNm).select(LogStaff::getStdOrgNo,LogStaff::getStdOrgNm);
        List<LogStaff> logStaffs = logStaffMapper.selectList(lqw);
        List<TreeNode> list = new ArrayList<>();
        if (logStaffs.size() != 0){
            logStaffs.forEach(logStaff -> {
                TreeNode treeNode = new TreeNode();
                treeNode.setOrgNo(logStaff.getStdOrgNo());
                treeNode.setOrgNm(logStaff.getStdOrgNm());
                list.add(treeNode);
            });
        }
        return list;
    }

    @Override
    public List<TreeNode> selectOrgTwo(String orgNo) {
        LambdaQueryWrapper<LogStaff> lqw = new LambdaQueryWrapper<>();
        lqw.in(LogStaff::getStdOrgNo,orgNo).groupBy(LogStaff::getStdDeptNo,LogStaff::getStdDeptNm).select(LogStaff::getStdDeptNo,LogStaff::getStdDeptNm);
        List<LogStaff> logStaffs = logStaffMapper.selectList(lqw);
        List<TreeNode> list = new ArrayList<>();
        if (logStaffs.size() != 0){
            logStaffs.forEach(logStaff -> {
                TreeNode treeNode = new TreeNode();
                treeNode.setOrgNo(logStaff.getStdDeptNo());
                treeNode.setOrgNm(logStaff.getStdDeptNm());
                list.add(treeNode);
            });
        }
        return list;
    }

    @Override
    public List<TreeNode> selectOrgThree(String orgNo) {
        LambdaQueryWrapper<LogStaff> lqw = new LambdaQueryWrapper<>();
        lqw.in(LogStaff::getStdDeptNo,orgNo).groupBy(LogStaff::getStdTeamNo,LogStaff::getStdTeamNm).select(LogStaff::getStdTeamNo,LogStaff::getStdTeamNm);
        List<LogStaff> logStaffs = logStaffMapper.selectList(lqw);
        List<TreeNode> list = new ArrayList<>();
        if (logStaffs.size() != 0){
            logStaffs.forEach(logStaff -> {
                TreeNode treeNode = new TreeNode();
                treeNode.setOrgNo(logStaff.getStdTeamNo());
                treeNode.setOrgNm(logStaff.getStdTeamNm());
                list.add(treeNode);
            });
        }
        return list;
    }

    @Override
    public List<TreeNode> selectOrgFour(String orgNo) {
        LambdaQueryWrapper<LogStaff> lqw = new LambdaQueryWrapper<>();
        lqw.in(LogStaff::getStdTeamNo,orgNo).orderBy(true, true, LogStaff::getStaffNo);
        List<LogStaff> logStaffs = logStaffMapper.selectList(lqw);
        List<TreeNode> list = new ArrayList<>();
        if (logStaffs.size() != 0){
            logStaffs.forEach(logStaff -> {
                TreeNode treeNode = new TreeNode();
                treeNode.setOrgNo(logStaff.getStaffNo());
                treeNode.setOrgNm(logStaff.getStaffNm());
                list.add(treeNode);
            });
        }
        return list;
    }
}
