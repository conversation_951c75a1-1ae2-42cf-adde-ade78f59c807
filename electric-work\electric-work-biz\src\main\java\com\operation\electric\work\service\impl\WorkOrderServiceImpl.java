package com.operation.electric.work.service.impl;

import com.operation.electric.work.service.WorkOrderService;
import com.operation.electric.flow.task.api.feign.RemoteFlowTaskService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Slf4j
@AllArgsConstructor
@Service("workOrderService")
public class WorkOrderServiceImpl implements WorkOrderService {

    private final RemoteFlowTaskService remoteFlowTaskService;
}
