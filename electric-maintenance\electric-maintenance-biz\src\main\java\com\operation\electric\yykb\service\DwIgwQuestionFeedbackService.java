package com.operation.electric.yykb.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.operation.electric.yykb.entity.DwIgwQuestionFeedbackEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

public interface DwIgwQuestionFeedbackService extends IService<DwIgwQuestionFeedbackEntity> {
	String saveImage(MultipartFile file, String sessionId) throws IOException;

	/**
	 * 提交反馈并关联图片
	 * @param content
	 * @param contactPhone
	 * @param sessionId
	 * @return
	 */
	DwIgwQuestionFeedbackEntity submitFeedback(String content, String contactPhone, String sessionId);

	Page getDwIgwQuestionFeedbackPage(Page page, DwIgwQuestionFeedbackEntity dwIgwQuestionFeedback);
}