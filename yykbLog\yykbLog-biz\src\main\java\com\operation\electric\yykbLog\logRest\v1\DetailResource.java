package com.operation.electric.yykbLog.logRest.v1;

import com.operation.electric.common.security.annotation.Inner;
import com.operation.electric.yykbLog.logRest.security.ApiOptionAuth;
import com.operation.electric.yykbLog.logRest.v1.vm.DetailVm;
import com.operation.electric.yykbLog.response.RestResponseData;
import com.operation.electric.yykbLog.service.LogDetailService;
import com.operation.electric.yykbLog.service.LogPostVariables;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Inner(false)
@RestController
@RequestMapping("/api/log/details")
@Tag(name = "日志详情", description = "用来管理系统使用的日志信息")
public class DetailResource {

    @Autowired
    private LogDetailService detailService;


    @PostMapping("")
    @ApiOptionAuth(required = false)
    @Schema(name = "保存日志详情", description = "保存日志详情")
    @Tag(name = "v1")
    @Inner(false)
    public RestResponseData<DetailVm> create(@RequestBody @Valid DetailVm vm) {
        boolean exist = LogPostVariables.isVipExist(vm.getStaffNo());
        if (exist) vm.setVipFlag(1);
        DetailVm detailVm = detailService.addDetail(vm);
        return RestResponseData.success(detailVm);
    }
}
