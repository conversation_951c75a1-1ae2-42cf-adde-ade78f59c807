package com.operation.electric.work.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.operation.electric.common.core.util.R;
import com.operation.electric.common.log.annotation.SysLog;
import com.operation.electric.work.entity.KnowledgeBaseDO;
import com.operation.electric.work.entity.KnowledgeBaseEntity;
import com.operation.electric.work.service.KnowledgeBaseService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import com.operation.electric.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 知识库表(未入库的知识库文档)
 *
 * <AUTHOR>
 * @date 2024-03-12 16:02:18
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/knowledgeBase" )
@Tag(description = "knowledgeBase" , name = "知识库表(未入库的知识库文档)管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class KnowledgeBaseController {

    private final  KnowledgeBaseService knowledgeBaseService;

    @Value("${work.ChatServerPath}")
    private String ChatServerPath;


    @Value("${work.knowledgeBaseName}")
    private String knowledgeBaseName;

    /**
     * 分页查询
     * @param page 分页对象
     * @param knowledgeBase 知识库表(未入库的知识库文档)
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
//    @PreAuthorize("@pms.hasPermission('work_knowledgeBase_view')" )
    public R getKnowledgeBasePage(@ParameterObject Page page, @ParameterObject KnowledgeBaseEntity knowledgeBase) {
        LambdaQueryWrapper<KnowledgeBaseEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(KnowledgeBaseEntity::getStatus,knowledgeBase.getStatus());
        return R.ok(knowledgeBaseService.page(page, wrapper));
    }


    /**
     * 通过id查询知识库表(未入库的知识库文档)
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('work_knowledgeBase_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(knowledgeBaseService.getById(id));
    }

    /**
     * 新增知识库表(未入库的知识库文档)
     * @param knowledgeBase 知识库表(未入库的知识库文档)
     * @return R
     */
    @Operation(summary = "新增知识库表(未入库的知识库文档)" , description = "新增知识库表(未入库的知识库文档)" )
    @SysLog("新增知识库表(未入库的知识库文档)" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('work_knowledgeBase_add')" )
    public R save(@RequestBody KnowledgeBaseEntity knowledgeBase) {
        return R.ok(knowledgeBaseService.save(knowledgeBase));
    }

    @SysLog("新增知识库表(未入库的知识库文档)" )
    @PostMapping("/submit")
    public R submit(@RequestBody List<KnowledgeBaseDO> knowledgeBaseDOS) {
        knowledgeBaseDOS.forEach(knowledgeBaseDO->{

            KnowledgeBaseEntity knowledgeBase = new KnowledgeBaseEntity();
            knowledgeBase.setFileName(knowledgeBaseDO.getFileName());
            knowledgeBase.setOriginalName(knowledgeBaseDO.getName());
            knowledgeBase.setUrl(knowledgeBaseDO.getUrl());
            knowledgeBase.setMaxLength(knowledgeBaseDO.getMaxLength());
            knowledgeBase.setCoincidentLength(knowledgeBaseDO.getCoincidentLength());
            knowledgeBase.setZhTitleEnhance(knowledgeBaseDO.getZhTitleEnhance());
            knowledgeBaseService.save(knowledgeBase);
        });

        return R.ok();
    }


    /**
     * 修改知识库表(未入库的知识库文档)
     * @param knowledgeBase 知识库表(未入库的知识库文档)
     * @return R
     */
    @Operation(summary = "修改知识库表(未入库的知识库文档)" , description = "修改知识库表(未入库的知识库文档)" )
    @SysLog("修改知识库表(未入库的知识库文档)" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('work_knowledgeBase_edit')" )
    public R updateById(@RequestBody KnowledgeBaseEntity knowledgeBase) {
        return R.ok(knowledgeBaseService.updateById(knowledgeBase));
    }

    /**
     * 通过id删除知识库表(未入库的知识库文档)
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除知识库表(未入库的知识库文档)" , description = "通过id删除知识库表(未入库的知识库文档)" )
    @SysLog("通过id删除知识库表(未入库的知识库文档)" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('work_knowledgeBase_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(knowledgeBaseService.removeBatchByIds(CollUtil.toList(ids)));
    }

    /**
     * 通过id删除知识库表(未入库的知识库文档)
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除知识库表(已入库的知识库文档)" , description = "通过id删除知识库表(已入库的知识库文档)" )
    @SysLog("通过id删除知识库表(已入库的知识库文档)" )
    @DeleteMapping("/delFile")
    @PreAuthorize("@pms.hasPermission('work_knowledgeBase_del')" )
    public R delFile(@RequestBody Long[] ids) {
        return R.ok(knowledgeBaseService.delFile(CollUtil.toList(ids),ChatServerPath,knowledgeBaseName));
    }



    /**
     * 通过id删除知识库表(未入库的知识库文档)
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id进行入库操作" , description = "通过id进行入库操作" )
    @SysLog("通过id进行入库操作" )
    @PostMapping("/storageById/{id}")
    @PreAuthorize("@pms.hasPermission('work_knowledgeBase_storage')" )
    public R storageById(@PathVariable Long id) {
        return knowledgeBaseService.storageByIds(id,ChatServerPath, knowledgeBaseName);
    }


    /**
     * 导出excel 表格
     * @param knowledgeBase 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('work_knowledgeBase_export')" )
    public List<KnowledgeBaseEntity> export(KnowledgeBaseEntity knowledgeBase,Integer[] ids) {
        return knowledgeBaseService.list(Wrappers.lambdaQuery(knowledgeBase).in(ArrayUtil.isNotEmpty(ids), KnowledgeBaseEntity::getId, ids));
    }

    @GetMapping("/listFiles")
    public JSONObject listFiles(String knowledgeBaseName){
        String s = HttpUtil.get(ChatServerPath+"/knowledge_base/list_files?knowledge_base_name=" + knowledgeBaseName);
        JSONObject entries = JSONUtil.parseObj(s);

        return entries;
    }
}
