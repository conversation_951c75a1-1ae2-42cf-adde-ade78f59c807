package com.operation.electric.yykb.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.operation.electric.yykb.api.dto.LogDetailDTO;
import com.operation.electric.yykb.entity.LogDetailEntity;
import com.operation.electric.yykb.mapper.LogDetailMapper;
import com.operation.electric.yykb.service.LogDetailService;
import org.springframework.stereotype.Service;
/**
 * 日志系统，系统使用详情日志记录信息
 *
 * <AUTHOR>
 * @date 2025-01-02 18:14:48
 */
@Service
public class LogDetailServiceImpl extends ServiceImpl<LogDetailMapper, LogDetailEntity> implements LogDetailService {
    @Override
    public IPage<LogDetailDTO> selectLogDetailAndLogStaffInSysName(Page page, LogDetailDTO logDetailDTO) {
        return this.baseMapper.selectLogDetailAndLogStaffInSysName(page,logDetailDTO);
    }
}