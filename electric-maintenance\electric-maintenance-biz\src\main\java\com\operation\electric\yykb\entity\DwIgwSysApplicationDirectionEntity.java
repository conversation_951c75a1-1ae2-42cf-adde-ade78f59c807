package com.operation.electric.yykb.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 应用方向
 *
 * <AUTHOR>
 * @date 2025-01-13 14:40:21
 */
@Data
@TableName("dw_igw_sys_application_direction")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "应用方向")
public class DwIgwSysApplicationDirectionEntity extends Model<DwIgwSysApplicationDirectionEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 应用方向
	*/
    @Schema(description="应用方向")
    private String name;
}