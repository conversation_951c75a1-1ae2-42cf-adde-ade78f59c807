package com.operation.electric.yykb.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.operation.electric.common.core.util.R;
import com.operation.electric.common.log.annotation.SysLog;
import com.operation.electric.yykb.entity.LogDetailEntity;
import com.operation.electric.yykb.entity.TreeNode;
import com.operation.electric.yykb.service.LogDetailService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.operation.electric.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 第三方系统日志
 *
 * <AUTHOR>
 * @date 2024-11-06 11:15:41
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/logDetail" )
@Tag(description = "logDetail" , name = "第三方系统日志管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class LogDetailController {

    private final  LogDetailService logDetailService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param logDetail 第三方系统日志
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('maintenance_logDetail_view')" )
    public R getLogDetailPage(@ParameterObject Page page, @ParameterObject LogDetailEntity logDetail) {
        LambdaQueryWrapper<LogDetailEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.eq(StrUtil.isNotBlank(logDetail.getStaffNo()),LogDetailEntity::getStaffNo,logDetail.getStaffNo());
		wrapper.eq(StrUtil.isNotBlank(logDetail.getOperationClass1()),LogDetailEntity::getOperationClass1,logDetail.getOperationClass1());
		wrapper.eq(StrUtil.isNotBlank(logDetail.getIp()),LogDetailEntity::getIp,logDetail.getIp());
		wrapper.eq(StrUtil.isNotBlank(logDetail.getSysName()),LogDetailEntity::getSysName,logDetail.getSysName());
		wrapper.eq(Objects.nonNull(logDetail.getVipFlag()),LogDetailEntity::getVipFlag,logDetail.getVipFlag());
		wrapper.eq(StrUtil.isNotBlank(logDetail.getServerIp()),LogDetailEntity::getServerIp,logDetail.getServerIp());
        return R.ok(logDetailService.page(page, wrapper));
    }


    /**
     * 通过id查询第三方系统日志
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('maintenance_logDetail_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(logDetailService.getById(id));
    }

    /**
     * 新增第三方系统日志
     * @param logDetail 第三方系统日志
     * @return R
     */
    @Operation(summary = "新增第三方系统日志" , description = "新增第三方系统日志" )
    @SysLog("新增第三方系统日志" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('maintenance_logDetail_add')" )
    public R save(@RequestBody LogDetailEntity logDetail) {
        return R.ok(logDetailService.save(logDetail));
    }

    /**
     * 修改第三方系统日志
     * @param logDetail 第三方系统日志
     * @return R
     */
    @Operation(summary = "修改第三方系统日志" , description = "修改第三方系统日志" )
    @SysLog("修改第三方系统日志" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('maintenance_logDetail_edit')" )
    public R updateById(@RequestBody LogDetailEntity logDetail) {
        return R.ok(logDetailService.updateById(logDetail));
    }

    /**
     * 通过id删除第三方系统日志
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除第三方系统日志" , description = "通过id删除第三方系统日志" )
    @SysLog("通过id删除第三方系统日志" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('maintenance_logDetail_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(logDetailService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param logDetail 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('maintenance_logDetail_export')" )
    public List<LogDetailEntity> export(LogDetailEntity logDetail,Integer[] ids) {
        return logDetailService.list(Wrappers.lambdaQuery(logDetail).in(ArrayUtil.isNotEmpty(ids), LogDetailEntity::getId, ids));
    }

    /**
     * 获取组织结构树
     * @return
     */
    @Operation(summary = "获取组织结构树" , description = "获取组织结构树" )
    @GetMapping("/getOrgTree" )
    @PreAuthorize("@pms.hasPermission('maintenance_logDetail_view')" )
    public R getOrg() {
        List<TreeNode> nodes = logDetailService.getOrgMessage();
        return R.ok(nodes);
    }

    /**
     * 获取人员集合数据
     */
    @Operation(summary = "获取人员集合" , description = "获取人员集合" )
    @GetMapping("/getStaff" )
    @PreAuthorize("@pms.hasPermission('maintenance_logDetail_view')" )
    public R getStaffListByOrgId(@RequestParam(required = false) String orgId) {
        List<String> staffList = logDetailService.selectStaffByOrgNo(orgId);
        return R.ok(staffList);
    }

    /**
     * 获取系统日志包含的系统列表下线
     */
    @Operation(summary = "获取系统列表" , description = "获取系统列表")
    @GetMapping("getSystem")
    @PreAuthorize("@pms.hasPermission('maintenance_logDetail_view')" )
    public R getSystemInfo(){
        return R.ok(logDetailService.getSystemInfo());
    }
}

