package com.operation.electric.workflow.service;

import com.operation.electric.workflow.entity.ProcessQuery;
import com.operation.electric.workflow.entity.vo.WfDeployVo;
import com.operation.electric.workflow.utils.PageQuery;
import com.operation.electric.workflow.utils.TableDataInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2022/6/30 9:03
 */
public interface IWfDeployService {

    TableDataInfo<WfDeployVo> queryPageList(ProcessQuery processQuery, PageQuery pageQuery);

    TableDataInfo<WfDeployVo> queryPublishList(String processKey, PageQuery pageQuery);

    void updateState(String definitionId, String stateCode);

    String queryBpmnXmlById(String definitionId);

    void deleteByIds(List<String> deployIds);
}
