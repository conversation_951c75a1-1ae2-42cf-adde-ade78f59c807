package com.operation.electric.yykbLog.entity;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.io.Serializable;
import java.util.Date;
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class Journal implements Serializable {

    @Builder.Default
    private String detailId = "";

    /**
     * 电力统一权限登陆平台OA账号
     */
    @Builder.Default
    private String staffNo="";

    /**
     * 动作时间，使用app操作的时间点
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date timestamp;

    /**
     * 一级操作名称。例如：登陆/退出登陆/超时退出
     */
    @Builder.Default
    private String operationClass1="";

    /**
     * 二级操作名称
     */
    @Builder.Default

    private String operationClass2 = "";
    @Builder.Default

    private String operationClass3 = "";
    @Builder.Default

    private String operationClass4 ="";
    @Builder.Default

    private String operationClass5 ="";

    /**
     * 使用系统时客户的电脑IP
     */
    @Builder.Default

    private String ip = "";

    /**
     * 接入日志系统 的第三方应用/系统的编号
     */
    @Builder.Default

    private String sysId = "";

    /**
     * 接入日志系统 的第三方应用/系统的名称
     */
    @Builder.Default
    private String sysName = "";

    @Override
    public String toString() {
        return "{" +
                "detailId:'" + detailId + '\'' +
                ", staffNo:'" + staffNo + '\'' +
                ", timestamp:" + timestamp +
                ", operationClass1:'" + operationClass1 + '\'' +
                ", operationClass2:'" + operationClass2 + '\'' +
                ", operationClass3:'" + operationClass3 + '\'' +
                ", operationClass4:'" + operationClass4 + '\'' +
                ", operationClass5:'" + operationClass5 + '\'' +
                ", ip:'" + ip + '\'' +
                ", sysId:'" + sysId + '\'' +
                ", sysName:'" + sysName + '\'' +
                '}';
    }
}
