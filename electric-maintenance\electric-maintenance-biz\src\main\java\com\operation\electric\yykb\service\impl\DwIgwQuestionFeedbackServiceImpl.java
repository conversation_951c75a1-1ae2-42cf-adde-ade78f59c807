package com.operation.electric.yykb.service.impl;


import cn.hutool.core.lang.func.Func1;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.operation.electric.yykb.dto.DwIgwQuestionFeedbackDto;
import com.operation.electric.yykb.entity.DwIgwPhotoEntity;
import com.operation.electric.yykb.entity.DwIgwQuestionFeedbackEntity;
import com.operation.electric.yykb.entity.DwIgwSysApplicationLogsEntity;
import com.operation.electric.yykb.mapper.DwIgwQuestionFeedbackMapper;
import com.operation.electric.yykb.service.DwIgwPhotoService;
import com.operation.electric.yykb.service.DwIgwQuestionFeedbackService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.lang.invoke.LambdaConversionException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 问题反馈表
 *
 * <AUTHOR>
 * @date 2025-06-20 11:50:53
 */
@Service
public class DwIgwQuestionFeedbackServiceImpl extends ServiceImpl<DwIgwQuestionFeedbackMapper, DwIgwQuestionFeedbackEntity> implements DwIgwQuestionFeedbackService {


	@Autowired
	public DwIgwPhotoService dwPhotoService;

	@Value("${file.upload-dir}")
	private String uploadDir;

	public String saveImage(MultipartFile file, String sessionId) throws IOException {
		String fileName = file.getOriginalFilename();
		String renameFileName = renameFile(fileName);
		Path filePath = Paths.get(uploadDir, "temp", renameFileName);
//		File destFile = new File(folderPath + renameFileName); // 检查文件夹是否存在，如果不存在则创建
/*		if (!destFile.getParentFile().exists()) {
			destFile.getParentFile().mkdirs();
		}*/
		try {
			Files.createDirectories(filePath.getParent());
			// 保存图片记录
			DwIgwPhotoEntity dwIgwPhotoEntity = new DwIgwPhotoEntity();
			dwIgwPhotoEntity.setFilePath(filePath.toString());
			dwIgwPhotoEntity.setSessionId(sessionId);
			dwPhotoService.save(dwIgwPhotoEntity);
			file.transferTo(filePath);
			return filePath.getParent() + "\\" + renameFileName;

		} catch (IOException e) {
			throw new RuntimeException("File upload failed", e);
		}
//		return folderPath + renameFileName;
	}


	// 提交反馈并关联图片
	@Override
	@Transactional
	public DwIgwQuestionFeedbackEntity submitFeedback(String content, String contactPhone, String sessionId) {
		// 创建反馈记录
		DwIgwQuestionFeedbackEntity feedback = new DwIgwQuestionFeedbackEntity();
		feedback.setQuestionContent(content);
		feedback.setTelephone(contactPhone);
		boolean save = this.save(feedback);
		// 关联临时图片
		if (sessionId != null) {
			LambdaQueryWrapper<DwIgwPhotoEntity> photoWrapper = new LambdaQueryWrapper<>();
			photoWrapper.eq(DwIgwPhotoEntity::getSessionId, sessionId);
			List<DwIgwPhotoEntity> photoEntityList = dwPhotoService.list(photoWrapper);
			for (DwIgwPhotoEntity image : photoEntityList) {
				// 移动文件到正式目录
				Path source = Paths.get(image.getFilePath());
				Path target = Paths.get(uploadDir, "final", feedback.getId().toString(),
						source.getFileName().toString());

				try {
					Files.createDirectories(target.getParent());
					Files.move(source, target, StandardCopyOption.REPLACE_EXISTING);

					// 更新图片记录
					image.setFeedbackId(feedback.getId());
					image.setFilePath(target.toString());
					image.setIsLinked(true);
					dwPhotoService.updateById(image);
				} catch (IOException e) {
					throw new RuntimeException("File move failed", e);
				}
			}
		}
		return feedback;
	}

	@Override
	public Page getDwIgwQuestionFeedbackPage(Page page, DwIgwQuestionFeedbackEntity dwIgwQuestionFeedback) {
		LambdaQueryWrapper<DwIgwQuestionFeedbackEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.like(StringUtils.isNotBlank(dwIgwQuestionFeedback.getQuestionContent()), DwIgwQuestionFeedbackEntity::getQuestionContent, dwIgwQuestionFeedback.getQuestionContent())
				.eq(StringUtils.isNotBlank(dwIgwQuestionFeedback.getTelephone()), DwIgwQuestionFeedbackEntity::getTelephone, dwIgwQuestionFeedback.getTelephone());
		Page resultPage = this.page(page, wrapper);
		List<DwIgwQuestionFeedbackEntity> recordList = resultPage.getRecords();
		List<DwIgwQuestionFeedbackDto> dwIgwQuestionFeedbackDtoList = Optional.ofNullable(recordList).orElse(new ArrayList<DwIgwQuestionFeedbackEntity>()).stream().map(record -> {
			LambdaQueryWrapper<DwIgwPhotoEntity> queryWrapper = new LambdaQueryWrapper<>();
			DwIgwQuestionFeedbackDto feedbackDto = new DwIgwQuestionFeedbackDto();
			BeanUtils.copyProperties(record, feedbackDto);
			queryWrapper.eq(DwIgwPhotoEntity::getFeedbackId, record.getId());
			List<DwIgwPhotoEntity> photoList = dwPhotoService.list(queryWrapper);
			feedbackDto.setPhotoEntityList(photoList);
			return feedbackDto;
		}).collect(Collectors.toList());
		resultPage.setRecords(dwIgwQuestionFeedbackDtoList);
		return resultPage;
	}


	private String renameFile(String fileName) {
		String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
		return UUID.randomUUID().toString() + "." + suffix;
	}

}