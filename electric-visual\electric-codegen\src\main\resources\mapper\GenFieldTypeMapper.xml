<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright (c) 2018-2025, electric All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: electric
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.operation.electric.codegen.mapper.GenFieldTypeMapper">

    <resultMap id="fieldTypeMap" type="com.operation.electric.codegen.entity.GenFieldType">
        <id property="id" column="id"/>
        <result property="columnType" column="column_type"/>
        <result property="attrType" column="attr_type"/>
        <result property="packageName" column="package_name"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="getPackageByTableId" resultType="String">
        select t1.package_name
        from gen_field_type t1,
        gen_table_column t2
        where t1.attr_type = t2.attr_type
        and t2.ds_name = #{dsName} and t2.table_name = #{tableName}
    </select>
</mapper>
