<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
            http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.operation</groupId>
		<artifactId>yykbLog</artifactId>
		<version>5.3.0</version>
	</parent>

	<artifactId>yykbLog-biz</artifactId>

	<dependencies>
		<!--必备: undertow容器-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-undertow</artifactId>
		</dependency>
		<!--必备: spring boot web-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<!--必备: 注册中心客户端-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
		</dependency>
		<!--必备: 配置中心客户端-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
		</dependency>
		<!--必备: 操作数据源相关-->
		<dependency>
			<groupId>com.operation</groupId>
			<artifactId>electric-common-data</artifactId>
		</dependency>
		<!--必备：electric安全模块-->
		<dependency>
			<groupId>com.operation</groupId>
			<artifactId>electric-common-security</artifactId>
		</dependency>
		<!--必备：xss 过滤模块-->
		<dependency>
			<groupId>com.operation</groupId>
			<artifactId>electric-common-xss</artifactId>
		</dependency>
		<!--必备: sentinel 依赖-->
		<dependency>
			<groupId>com.operation</groupId>
			<artifactId>electric-common-sentinel</artifactId>
		</dependency>
		<!--必备: feign 依赖-->
		<dependency>
			<groupId>com.operation</groupId>
			<artifactId>electric-common-feign</artifactId>
		</dependency>
		<!--必备: 依赖api模块-->
		<dependency>
			<groupId>com.operation</groupId>
			<artifactId>yykbLog-api</artifactId>
			<version>5.3.0</version>
		</dependency>
		<!--必备: log 依赖-->
		<dependency>
			<groupId>com.operation</groupId>
			<artifactId>electric-common-log</artifactId>
		</dependency>
		<!--选配: mybatis 依赖 -->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
		</dependency>
		<!--选配： druid 连接池 -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-3-starter</artifactId>
		</dependency>
		<!--选配: mysql 数据库驱动 -->
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
		</dependency>
		<!--选配: swagger文档-->
		<dependency>
			<groupId>com.operation</groupId>
			<artifactId>electric-common-swagger</artifactId>
		</dependency>
		<!--测试: spring boot test-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
		</dependency>
		<dependency>
			<groupId>com.operation</groupId>
			<artifactId>electric-common-encrypt-api</artifactId>
		</dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
        </dependency>
		<dependency>
			<groupId>io.swagger</groupId>
			<artifactId>swagger-models</artifactId>
			<version>1.6.2</version>
		</dependency>
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>io.fabric8</groupId>
				<artifactId>docker-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>
</project>
