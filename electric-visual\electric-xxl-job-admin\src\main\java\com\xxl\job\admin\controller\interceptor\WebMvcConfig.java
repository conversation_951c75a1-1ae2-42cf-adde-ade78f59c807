package com.xxl.job.admin.controller.interceptor;

import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * web mvc config
 *
 * <AUTHOR> 2018-04-02 20:48:20
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

	@Resource
	private PermissionInterceptor permissionInterceptor;

	@Resource
	private CookieInterceptor cookieInterceptor;

	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		registry.addInterceptor(permissionInterceptor).addPathPatterns("/**");
		registry.addInterceptor(cookieInterceptor).addPathPatterns("/**");
	}

}
