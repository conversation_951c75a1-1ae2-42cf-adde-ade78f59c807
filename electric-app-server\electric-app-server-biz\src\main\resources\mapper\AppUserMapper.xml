<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright (c) 2018-2025, electric All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: electric
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.operation.electric.app.mapper.AppUserMapper">

  <resultMap id="appUserMap" type="com.operation.electric.app.api.vo.AppUserVO">
        <id property="userId" column="user_id"/>
        <result property="username" column="username"/>
        <result property="password" column="password"/>
        <result property="phone" column="phone"/>
        <result property="avatar" column="avatar"/>
        <result property="nickname" column="nickname"/>
        <result property="name" column="name"/>
        <result property="email" column="email"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="lastModifiedTime" column="last_modified_time"/>
	    <collection property="roleList" ofType="com.operation.electric.app.api.entity.AppRole"
					  select="com.operation.electric.app.mapper.AppRoleMapper.listRolesByUserId" column="user_id">
		</collection>
  </resultMap>
    <select id="getUserVosPage" resultMap="appUserMap">
		select
		u.user_id,
		u.username,
		u.password,
		u.phone,
		u.avatar,
		u.create_time,
		u.del_flag,
		u.lock_flag,
		u.tenant_id,
		u.nickname,
		u.name,
		u.email
		from app_user u
		<where>
			u.del_flag = '0'
			<if test="query.username != null and query.username != ''">
				<bind name="usernameLike" value="'%'+query.username+'%'"/>
				AND u.username LIKE #{usernameLike}
			</if>
		</where>
	</select>

	<sql id="userRoleDeptSql">
		u.user_id,
        u.username,
        u.password,
        u.salt,
        u.phone,
        u.avatar,
        u.wx_openid,
        u.del_flag,
        u.lock_flag,
        u.tenant_id,
        u.nickname,
        u.name,
        u.email,
        u.create_by,
        u.create_time  ucreate_time,
        u.update_time  uupdate_time,
        r.role_id
	</sql>

	<select id="getUserVoById" resultMap="appUserMap">
		SELECT
		<include refid="userRoleDeptSql"/>
		FROM
		app_user u
		LEFT JOIN app_user_role urole ON urole.user_id = u.user_id
		LEFT JOIN app_role r ON r.role_id = urole.role_id
		WHERE
		u.user_id = #{id}
	</select>
</mapper>
