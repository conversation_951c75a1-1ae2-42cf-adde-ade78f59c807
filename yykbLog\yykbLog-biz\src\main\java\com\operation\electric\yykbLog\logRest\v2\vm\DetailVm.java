package com.operation.electric.yykbLog.logRest.v2.vm;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@Schema(title = "应用系统员工信息")
public class DetailVm implements Serializable {
    /**
     * 接入日志系统 的第三方应用/系统的编号
     */
    private String detailId;

    /**
     * 电力统一权限登陆平台OA账号
     */
    @NotBlank(message = "OA账号不能为空")
    @Length(min = 1, max = 50, message = "OA账号不能超过50个字符")
    @Schema(name = "OA账号", title = "OA账号编码", description = "长度为1-50个字符", required = true, example = "zhangxs")
    private String staffNo;

    /**
     * 动作时间，使用app操作的时间点
     */
    @NotNull(message = "操作时间不能为空，TimeZone必须是东8区,时间格式为：yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss", iso = DateTimeFormat.ISO.DATE_TIME)
    @Schema(name = "操作时间", title = "操作时间", description = "时间格式为：yyyy-MM-dd HH:mm:ss", required = true, example = "2023-02-12 12:56:12")
    private Date timestamp;

    /**
     * 一级操作名称。例如：登陆/退出登陆/超时退出
     */
    @NotBlank(message = "一级菜单不能为空")
    @Schema(name = "一级操作菜单", title = "一级操作菜单", description = "一级操作菜单", required = true, example = "用户登陆")
    private String operationClass1;

    /**
     * 二级操作名称
     */
    @Schema(name = "二级操作标签", title = "二级操作标签", description = "二级操作标签", required = false, example = "系统管理/用户管理")
    private String operationClass2;
    @Schema(name = "三级操作标签", title = "三级操作标签", description = "三级操作标签", required = false, example = "系统管理/用户管理/添加用户")
    private String operationClass3;

    @Schema(name = "四级操作标签", title = "四级操作标签", description = "四级操作标签", required = false, example = "系统管理/用户管理/添加用户/添加分组")
    private String operationClass4;

    @Schema(name = "五级操作标签", title = "五级操作标签", description = "五级操作标签", required = false, example = "系统管理/用户管理/添加用户/添加分组/添加分组角色...")
    private String operationClass5;

    /**
     * 使用系统时客户的电脑IP
     */
    @NotBlank(message = "客户端IP不能为空")
    @Pattern(regexp = "^[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}$", message = "客户端IP格式不正确,正确格式为：xxx.xxx.xxx.xxx")
    @Schema(name = "客户端IP", title = "客户端IP", description = "使用应用用户的IP", required = true, example = "**************")
    private String ip;

    /**
     * 接入日志系统 的第三方应用/系统的编号
     */

    /**
     * 接入日志系统 的第三方应用/系统的名称
     */
    @NotBlank(message = "系统名称不能为空")
    @Schema(name = "系统名称", title = "系统名称", description = "系统名称", required = false, example = "长沙电网线变户停电全感知")
    private String sysName;
    @Schema(name = "系统编号", title = "系统编号", description = "系统编号长度必须为5位", required = false, example = "10005")
    private String sysId;

}
