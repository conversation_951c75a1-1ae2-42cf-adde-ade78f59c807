package com.operation.electric.yykb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.operation.electric.yykb.entity.LogStaff;
import com.operation.electric.yykb.entity.TreeNode;

import java.util.List;

/**
 * 【】
 *
 * <AUTHOR>
 * @date 2024/11/07
 */
public interface LogStaffService extends IService<LogStaff> {

    List<TreeNode> selectOrgOne();

    List<TreeNode> selectOrgTwo(String orgNo);

    List<TreeNode> selectOrgThree(String orgNo);

    List<TreeNode> selectOrgFour(String orgNo);
}
