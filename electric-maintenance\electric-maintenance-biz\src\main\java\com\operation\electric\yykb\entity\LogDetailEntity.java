package com.operation.electric.yykb.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 第三方系统日志
 *
 * <AUTHOR>
 * @date 2024-11-06 11:15:41
 */
@Data
@TableName("log_detail")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "第三方系统日志")
public class LogDetailEntity extends Model<LogDetailEntity> {


	/**
	* 日志主键
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="日志主键")
    private Integer id;

	/**
	* 记录时间
	*/
    @Schema(description="记录时间")
    private LocalDateTime createdAt;

	/**
	* 更新时间
	*/
    @Schema(description="更新时间")
    private LocalDateTime updatedAt;

	/**
	* 账号名称
	*/
    @Schema(description="账号名称")
    private String staffNo;

	/**
	* 操作时间
	*/
    @Schema(description="操作时间")
    private LocalDateTime timestamp;

	/**
	* 一级业务名称
	*/
    @Schema(description="一级业务名称")
    private String operationClass1;

	/**
	* 二级业务名称
	*/
    @Schema(description="二级业务名称")
    private String operationClass2;

	/**
	* 三级业务名称
	*/
    @Schema(description="三级业务名称")
    private String operationClass3;

	/**
	* 四级业务名称
	*/
    @Schema(description="四级业务名称")
    private String operationClass4;

	/**
	* 五级业务名称
	*/
    @Schema(description="五级业务名称")
    private String operationClass5;

	/**
	* IP地址
	*/
    @Schema(description="IP地址")
    private String ip;

	/**
	* 接入系统编号
	*/
    @Schema(description="接入系统编号")
    private String sysId;

	/**
	* 接入系统名称
	*/
    @Schema(description="接入系统名称")
    private String sysName;

	/**
	* 接入系统版本
	*/
    @Schema(description="接入系统版本")
    private String sysVersion;

	/**
	* 在线时间
	*/
    @Schema(description="在线时间")
    private BigDecimal onlineTime;

	/**
	* 是否为重要用户
	*/
    @Schema(description="是否为重要用户")
    private Integer vipFlag;

	/**
	* 访问系统IP
	*/
    @Schema(description="访问系统IP")
    private String serverIp;
}
