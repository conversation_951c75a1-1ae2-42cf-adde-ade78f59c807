package com.operation.electric.yykbLog.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.operation.electric.common.core.util.R;
import com.operation.electric.common.log.annotation.SysLog;
import com.operation.electric.yykbLog.entity.LogSysInfoEntity;
import com.operation.electric.yykbLog.service.LogSysInfoService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.operation.electric.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 应用系统档案
 *
 * <AUTHOR>
 * @date 2024-03-21 17:21:46
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/logSysInfo" )
@Tag(description = "logSysInfo" , name = "应用系统档案管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class LogSysInfoController {

    private final LogSysInfoService logSysInfoService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param logSysInfo 应用系统档案
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('yykbLog_logSysInfo_view')" )
    public R getLogSysInfoPage(@ParameterObject Page page, @ParameterObject LogSysInfoEntity logSysInfo) {
        LambdaQueryWrapper<LogSysInfoEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.like(StrUtil.isNotBlank(logSysInfo.getSysName()),LogSysInfoEntity::getSysName,logSysInfo.getSysName());
        return R.ok(logSysInfoService.page(page, wrapper));
    }


    /**
     * 通过id查询应用系统档案
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('yykbLog_logSysInfo_view')" )
    public R getById(@PathVariable("id" ) Integer id) {
        return R.ok(logSysInfoService.getById(id));
    }

    /**
     * 新增应用系统档案
     * @param logSysInfo 应用系统档案
     * @return R
     */
    @Operation(summary = "新增应用系统档案" , description = "新增应用系统档案" )
    @SysLog("新增应用系统档案" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('yykbLog_logSysInfo_add')" )
    public R save(@RequestBody LogSysInfoEntity logSysInfo) {
        return R.ok(logSysInfoService.add(logSysInfo));
    }

    /**
     * 修改应用系统档案
     * @param logSysInfo 应用系统档案
     * @return R
     */
    @Operation(summary = "修改应用系统档案" , description = "修改应用系统档案" )
    @SysLog("修改应用系统档案" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('yykbLog_logSysInfo_edit')" )
    public R updateById(@RequestBody LogSysInfoEntity logSysInfo) {
        return R.ok(logSysInfoService.updateById(logSysInfo));
    }

    /**
     * 通过id删除应用系统档案
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除应用系统档案" , description = "通过id删除应用系统档案" )
    @SysLog("通过id删除应用系统档案" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('yykbLog_logSysInfo_del')" )
    public R removeById(@RequestBody Integer[] ids) {
        return R.ok(logSysInfoService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param logSysInfo 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('yykbLog_logSysInfo_export')" )
    public List<LogSysInfoEntity> export(LogSysInfoEntity logSysInfo,Integer[] ids) {
        return logSysInfoService.list(Wrappers.lambdaQuery(logSysInfo).in(ArrayUtil.isNotEmpty(ids), LogSysInfoEntity::getId, ids));
    }
}