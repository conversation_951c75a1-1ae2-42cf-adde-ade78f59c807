/*
 *    Copyright (c) 2018-2025, electric All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: electric
 */

package com.operation.electric.app.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.operation.electric.app.api.entity.AppSocialDetails;
import com.operation.electric.app.service.AppSocialDetailsService;
import com.operation.electric.common.core.util.R;
import com.operation.electric.common.core.util.ValidGroup;
import com.operation.electric.common.excel.annotation.ResponseExcel;
import com.operation.electric.common.log.annotation.SysLog;
import com.operation.electric.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/appsocial")
@AllArgsConstructor
@Tag(description = "social", name = "三方账号管理模块")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class AppSocialDetailsController {

	private final AppSocialDetailsService appSocialDetailsService;

	/**
	 * 社交登录账户简单分页查询
	 * @param page 分页对象
	 * @param appSocialDetails 社交登录
	 * @return
	 */
	@GetMapping("/page")
	public R getSocialDetailsPage(Page page, AppSocialDetails appSocialDetails) {
		return R.ok(appSocialDetailsService.page(page, Wrappers.query(appSocialDetails)));
	}

	/**
	 * 信息
	 * @param id id
	 * @return R
	 */
	@GetMapping("/{id}")
	public R getinfo(@PathVariable("id") Long id) {
		return R.ok(appSocialDetailsService.getById(id));
	}

	/**
	 * 保存
	 * @param appSocialDetails
	 * @return R
	 */
	@SysLog("保存三方信息")
	@PostMapping
	@PreAuthorize("@pms.hasPermission('app_social_details_add')")
	public R save(@Valid @RequestBody AppSocialDetails appSocialDetails) {
		return R.ok(appSocialDetailsService.save(appSocialDetails));
	}

	/**
	 * 修改
	 * @param appSocialDetails
	 * @return R
	 */
	@SysLog("修改三方信息")
	@PutMapping
	@PreAuthorize("@pms.hasPermission('app_social_details_edit')")
	public R updateById(@Validated({ ValidGroup.Update.class }) @RequestBody AppSocialDetails appSocialDetails) {
		appSocialDetailsService.updateById(appSocialDetails);
		return R.ok(Boolean.TRUE);
	}

	/**
	 * 删除
	 * @param ids
	 * @return R
	 */
	@SysLog("删除三方信息")
	@DeleteMapping
	@PreAuthorize("@pms.hasPermission('app_social_details_del')")
	public R removeById(@RequestBody Long[] ids) {
		return R.ok(appSocialDetailsService.removeBatchByIds(CollUtil.toList(ids)));
	}

	/**
	 * 通过社交账号、手机号查询用户、角色信息
	 * @param inStr appid@code
	 * @return
	 */
	@Inner
	@GetMapping("/info/{inStr}")
	public R getUserInfo(@PathVariable String inStr) {
		return R.ok(appSocialDetailsService.getUserInfo(inStr));
	}

	/**
	 * 绑定社交账号
	 * @param state 类型
	 * @param code code
	 * @return
	 */
	@PostMapping("/bind")
	public R bindSocial(String state, String code) {
		return R.ok(appSocialDetailsService.bindSocial(state, code));
	}

	/**
	 * 导出
	 */
	@GetMapping("/export")
	@ResponseExcel
	public List<AppSocialDetails> export(AppSocialDetails appSocialDetails) {
		return appSocialDetailsService.list(Wrappers.query(appSocialDetails));
	}

}
