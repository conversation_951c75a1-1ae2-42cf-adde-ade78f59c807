<!doctype html>
<html>
	<head>
		<title>Druid Web Session Stat</title>
		<link href='css/bootstrap.min.css' rel="stylesheet" />
		<link href="css/style.css" type="text/css" rel="stylesheet"/>
    	<script type="text/javascript" src="js/jquery.min.js"></script>
		<script src="js/lang.js" type="text/javascript" charset="utf8"></script>
		<script src="js/common.js" type="text/javascript" charset="utf8"></script>
	</head>
	<body>
		
    	<div class="container-fluid">
      		<div class="row-fluid">
        		<div class="span12">
          				<h3>
          					Web Session View
          					<a id="viewJsonApi" target="_blank">View JSON API</a>
          				</h3>
						<table id="dataTable" class="table table-bordered" style="background-color: #fff">
							<tr>
								<td  class="td_lable lang" langKey="SESSIONID" width='130'> SESSIONID </td>
								<td id="SESSIONID"></td>
								<td></td>
							</tr>
							<tr>
								<td  class="td_lable lang" langKey="UserAgent"> UserAgent </td>
								<td id="UserAgent"></td>
								<td></td>
							</tr>
							<tr>
								<td  class="td_lable lang" langKey="Principal"> Principal </td>
								<td id="Principal"></td>
								<td></td>
							</tr>
							<tr>
								<td  class="td_lable lang" langKey="CreateTime"> CreateTime </td>
								<td id="CreateTime"></td>
								<td></td>
							</tr>
							<tr>
								<td  class="td_lable lang" langKey="LastAccessTime"> LastAccessTime </td>
								<td id="LastAccessTime"></td>
								<td></td>
							</tr>
							<tr>
								<td  class="td_lable lang" langKey="RemoteAddress"> RemoteAddress </td>
								<td id="RemoteAddress"></td>
								<td></td>
							</tr>
							<tr>
								<td  class="td_lable lang" langKey="RequestCount"> RequestCount </td>
								<td id="RequestCount"></td>
								<td></td>
							</tr>
							<tr>
								<td  class="td_lable lang" langKey="RequestTimeMillisTotal"> RequestTimeMillisTotal </td>
								<td id="RequestTimeMillisTotal"></td>
								<td></td>
							</tr>
							<tr>
								<td  class="td_lable lang" langKey="RequestInterval"> RequestInterval </td>
								<td id="RequestInterval"></td>
								<td></td>
							</tr>
							<tr>
								<td  class="td_lable lang" langKey="RunningCount"> RunningCount </td>
								<td id="RunningCount"></td>
								<td></td>
							</tr>
							<tr>
								<td  class="td_lable lang" langKey="ConcurrentMax"> ConcurrentMax </td>
								<td id="ConcurrentMax"></td>
								<td></td>
							</tr>
							<tr>
								<td  class="td_lable lang" langKey="JdbcExecuteCount"> JdbcExecuteCount </td>
								<td id="JdbcExecuteCount"></td>
								<td></td>
							</tr>
							<tr>
								<td  class="td_lable lang" langKey="JdbcExecuteTimeMillis"> JdbcExecuteTimeMillis </td>
								<td id="JdbcExecuteTimeMillis"></td>
								<td></td>
							</tr>
							<tr>
								<td  class="td_lable lang" langKey="JdbcCommitCount"> JdbcCommitCount </td>
								<td id="JdbcCommitCount"></td>
								<td></td>
							</tr>
							<tr>
								<td  class="td_lable lang" langKey="JdbcRollbackCount"> JdbcRollbackCount </td>
								<td id="JdbcRollbackCount"></td>
								<td></td>
							</tr>
							<tr>
								<td  class="td_lable lang" langKey="JdbcFetchRowCount"> JdbcFetchRowCount </td>
								<td id="JdbcFetchRowCount"></td>
								<td></td>
							</tr>
							<tr>
								<td  class="td_lable lang" langKey="JdbcUpdateCount"> JdbcUpdateCount </td>
								<td id="JdbcUpdateCount"></td>
								<td></td>
							</tr>
				      	</table>
				      	<div class="container">
				      		<a class="btn btn-primary" href="javascript:window.close();">Close</a>
				      	</div>
        		</div>
      		</div> 
    	</div>
    	<script type="text/javascript">
    	$.namespace("druid.websessiondetail");
    	druid.websessiondetail = function () {  
    		var sessionId = druid.common.getUrlVar("sessionId");
    		return  {
    			init : function() {
    				druid.common.buildHead(6);
    				this.ajaxRequestForBasicInfo();
    				$("#viewJsonApi").attr("href", 'websession-' + sessionId + '.json');
    			},
    			
    			ajaxRequestForBasicInfo : function() {
    				$.ajax({
    					type: 'POST',
    					url: 'websession-' + sessionId + '.json',
    					success: function(data) {
    						if (data.Content == null)
    							return;
    						$("#SESSIONID").text(data.Content.SESSIONID)
    						if (data.Content.Principal) {
    							$("#Principal").text(data.Content.Principal)
    						}
    						
    						$("#CreateTime").text(data.Content.CreateTime)
    						$("#LastAccessTime").text(data.Content.LastAccessTime)
    						$("#UserAgent").text(data.Content.UserAgent)
    						$("#RemoteAddress").text(data.Content.RemoteAddress)
    						$("#RequestCount").text(data.Content.RequestCount)
    						$("#RequestTimeMillisTotal").text(data.Content.RequestTimeMillisTotal)
    						$("#RunningCount").text(data.Content.RunningCount)
    						$("#ConcurrentMax").text(data.Content.ConcurrentMax)
    						$("#JdbcExecuteCount").text(data.Content.JdbcExecuteCount)
    						$("#JdbcExecuteTimeMillis").text(data.Content.JdbcExecuteTimeMillis)
    						$("#JdbcCommitCount").text(data.Content.JdbcCommitCount)
    						$("#JdbcRollbackCount").text(data.Content.JdbcRollbackCount)
    						$("#JdbcFetchRowCount").text(data.Content.JdbcFetchRowCount)
    						$("#JdbcUpdateCount").text(data.Content.JdbcUpdateCount)
    						var html = '[';
    						html += '<a title="count of < 1 ms">' + data.Content.RequestInterval[0] + '</a>';
    						html += ', <a title="count of 1 - 10 ms">' + data.Content.RequestInterval[1] + '</a>';
    						html += ', <a title="count of 10 - 100 ms">' + data.Content.RequestInterval[2] + '</a>';
    						html += ', <a title="count of 100 - 1000 ms">' + data.Content.RequestInterval[3] + '</a>';
    						html += ', <a title="count of 1 - 10 s">' + data.Content.RequestInterval[4] + '</a>';
    						html += ', <a title="count of 10 - 100 s">' + data.Content.RequestInterval[5] + '</a>';
    						html += ', <a title="count of 100 - 1000 s">' + data.Content.RequestInterval[6] + '</a>';
    						html += ', <a title="count of 1000 - 10000 s">' + data.Content.RequestInterval[7] + '</a>';
    						html += ', <a title="count of > 10000 s">' + data.Content.RequestInterval[8] + '</a>';
    						html += ']';
    						$("#RequestInterval").html(html);
    					},
    					dataType: "json"
    				});
    			}
    		}
    	}();

    	$(document).ready(function() {
    		druid.websessiondetail.init();
    	});

    	</script>
	</body>
</html>
