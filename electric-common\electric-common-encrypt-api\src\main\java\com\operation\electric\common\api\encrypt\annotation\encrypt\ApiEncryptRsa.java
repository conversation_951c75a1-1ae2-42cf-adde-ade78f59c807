package com.operation.electric.common.api.encrypt.annotation.encrypt;

import com.operation.electric.common.api.encrypt.enums.EncryptType;

import java.lang.annotation.*;

/**
 * rsa body 加密
 *
 * <AUTHOR>
 * <AUTHOR>
 * @version 2018/9/4
 * @see ApiEncrypt
 */
@Target({ ElementType.METHOD, ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Documented
@ApiEncrypt(EncryptType.RSA)
public @interface ApiEncryptRsa {

}
