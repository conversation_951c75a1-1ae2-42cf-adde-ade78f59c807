<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.operation.electric.workflow.mapper.WfCopyMapper">

    <resultMap type="com.operation.electric.workflow.entity.WfCopy" id="WfCopyResult">
        <result property="copyId" column="copy_id"/>
        <result property="title" column="title"/>
        <result property="processId" column="process_id"/>
        <result property="processName" column="process_name"/>
        <result property="categoryId" column="category_id"/>
        <result property="taskId" column="taskId"/>
        <result property="userId" column="user_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


</mapper>
