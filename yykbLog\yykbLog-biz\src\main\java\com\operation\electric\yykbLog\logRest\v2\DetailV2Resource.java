package com.operation.electric.yykbLog.logRest.v2;

import cn.hutool.core.bean.BeanUtil;
import com.operation.electric.common.security.annotation.Inner;
import com.operation.electric.yykbLog.logRest.security.ApiOptionAuth;
import com.operation.electric.yykbLog.logRest.security.SecuritySessionUtils;
import com.operation.electric.yykbLog.logRest.v2.vm.DetailVm;
import com.operation.electric.yykbLog.response.RestResponseData;
import com.operation.electric.yykbLog.service.LogDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Inner(false)
@RestController
@RequestMapping("/api/v2/log/details")
@Tag(name = "日志详情", description = "用来管理系统使用的日志信息")
public class DetailV2Resource {

    @Autowired
    private LogDetailService detailService;

    @PostMapping("")
    @ApiOptionAuth(required = true)
    @Operation(
            parameters = {
                    @Parameter(name = "APP-KEY", description = "应用系统id", required = true, in = ParameterIn.HEADER),
                    @Parameter(name = "SYS-ID", description = "应用系统编码", required = true, in = ParameterIn.HEADER)
            })
    @Schema(name = "保存日志详情", description = "保存日志详情")
    @Tag(name = "v2")
    public RestResponseData<com.operation.electric.yykbLog.logRest.v1.vm.DetailVm> create(@RequestBody @Valid DetailVm vm) {
        com.operation.electric.yykbLog.logRest.v1.vm.DetailVm detailVm = new com.operation.electric.yykbLog.logRest.v1.vm.DetailVm();
        BeanUtil.copyProperties(vm, detailVm);
        String sysId = SecuritySessionUtils.getSysId();
        detailVm.setSysId(sysId);

        detailVm = detailService.addDetail(detailVm);
        return RestResponseData.success(detailVm);
    }

}
