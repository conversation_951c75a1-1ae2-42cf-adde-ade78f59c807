package com.operation.electric.yykb.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.operation.electric.common.core.util.R;
import com.operation.electric.common.log.annotation.SysLog;
import com.operation.electric.yykb.entity.DwIgwSysApplicationClassEntity;
import com.operation.electric.yykb.service.DwIgwSysApplicationClassService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.operation.electric.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 应用大类表单
 *
 * <AUTHOR>
 * @date 2025-01-13 14:33:45
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/dwIgwSysApplicationClass" )
@Tag(description = "dwIgwSysApplicationClass" , name = "应用大类表单管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class DwIgwSysApplicationClassController {

    private final  DwIgwSysApplicationClassService dwIgwSysApplicationClassService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param dwIgwSysApplicationClass 应用大类表单
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysApplicationClass_view')" )
    public R getDwIgwSysApplicationClassPage(@ParameterObject Page page, @ParameterObject DwIgwSysApplicationClassEntity dwIgwSysApplicationClass) {
        LambdaQueryWrapper<DwIgwSysApplicationClassEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtils.isNotBlank(dwIgwSysApplicationClass.getApplicationName())
				, DwIgwSysApplicationClassEntity::getApplicationName , dwIgwSysApplicationClass.getApplicationName());
        return R.ok(dwIgwSysApplicationClassService.page(page, wrapper));
    }


    /**
     * 通过id查询应用大类表单
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysApplicationClass_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(dwIgwSysApplicationClassService.getById(id));
    }

    /**
     * 新增应用大类表单
     * @param dwIgwSysApplicationClass 应用大类表单
     * @return R
     */
    @Operation(summary = "新增应用大类表单" , description = "新增应用大类表单" )
    @SysLog("新增应用大类表单" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysApplicationClass_add')" )
    public R save(@RequestBody DwIgwSysApplicationClassEntity dwIgwSysApplicationClass) {
        return R.ok(dwIgwSysApplicationClassService.save(dwIgwSysApplicationClass));
    }

    /**
     * 修改应用大类表单
     * @param dwIgwSysApplicationClass 应用大类表单
     * @return R
     */
    @Operation(summary = "修改应用大类表单" , description = "修改应用大类表单" )
    @SysLog("修改应用大类表单" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysApplicationClass_edit')" )
    public R updateById(@RequestBody DwIgwSysApplicationClassEntity dwIgwSysApplicationClass) {
        return R.ok(dwIgwSysApplicationClassService.updateById(dwIgwSysApplicationClass));
    }

    /**
     * 通过id删除应用大类表单
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除应用大类表单" , description = "通过id删除应用大类表单" )
    @SysLog("通过id删除应用大类表单" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysApplicationClass_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(dwIgwSysApplicationClassService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param dwIgwSysApplicationClass 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysApplicationClass_export')" )
    public List<DwIgwSysApplicationClassEntity> export(DwIgwSysApplicationClassEntity dwIgwSysApplicationClass,Long[] ids) {
        return dwIgwSysApplicationClassService.list(Wrappers.lambdaQuery(dwIgwSysApplicationClass).in(ArrayUtil.isNotEmpty(ids), DwIgwSysApplicationClassEntity::getId, ids));
    }
}