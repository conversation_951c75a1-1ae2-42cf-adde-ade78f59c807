package com.operation.electric.yykb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.operation.electric.common.core.util.R;
import com.operation.electric.yykb.api.entity.DwIgwSysDetailEntity;

import java.util.List;

public interface DwIgwSysDetailService extends IService<DwIgwSysDetailEntity> {

    List<DwIgwSysDetailEntity> getSysList(String sysType);

    List<DwIgwSysDetailEntity> getSysListAll(DwIgwSysDetailEntity param);

    void operationConfigurationFile();

    R monitor(String appId);

    String monitoringAllData();

    Integer quantitativeRestriction();

    List<String> findAll();

    String dwIgwSysApplicationLineStatusChange(DwIgwSysDetailEntity detailEntity);

	List<DwIgwSysDetailEntity> findDetailByAppID(String appId);
}
