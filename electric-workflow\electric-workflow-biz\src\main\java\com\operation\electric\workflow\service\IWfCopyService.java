package com.operation.electric.workflow.service;

import com.operation.electric.workflow.entity.bo.WfCopyBo;
import com.operation.electric.workflow.entity.bo.WfTaskBo;
import com.operation.electric.workflow.entity.vo.WfCopyVo;
import com.operation.electric.workflow.utils.PageQuery;
import com.operation.electric.workflow.utils.TableDataInfo;

import java.util.List;

/**
 * 流程抄送Service接口
 *
 * <AUTHOR>
 * @date 2022-05-19
 */
public interface IWfCopyService {

    /**
     * 查询流程抄送
     *
     * @param copyId 流程抄送主键
     * @return 流程抄送
     */
    WfCopyVo queryById(Long copyId);

    /**
     * 查询流程抄送列表
     *
     * @param wfCopy 流程抄送
     * @return 流程抄送集合
     */
    TableDataInfo<WfCopyVo> selectPageList(WfCopyBo wfCopy, PageQuery pageQuery);

    /**
     * 查询流程抄送列表
     *
     * @param wfCopy 流程抄送
     * @return 流程抄送集合
     */
    List<WfCopyVo> selectList(WfCopyBo wfCopy);

    /**
     * 抄送
     * @param taskBo
     * @return
     */
    Boolean makeCopy(WfTaskBo taskBo);
}
