package com.operation.electric.yykbLog.entity;

import lombok.*;

import java.io.Serializable;
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class OperateUser implements Serializable {

    private Integer id;

    /**
     * 员工编码
     */
    @Builder.Default
    private String staffNo="";

    /**
     * 员工姓名
     */
    @Builder.Default

    private String staffNm="";

    /**
     * 标准单位编码
     */
    @Builder.Default

    private String stdOrgNo="";

    /**
     * 标准单位名称
     */
    @Builder.Default

    private String stdOrgNm="";

    /**
     * 标准部门编码
     */
    @Builder.Default
    private String stdDeptNo="";

    /**
     * 标准部门名称
     */
    @Builder.Default

    private String stdDeptNm="";

    /**
     * 标准班组编码
     */
    @Builder.Default

    private String stdTeamNo="";

    /**
     * 标准班组名称
     */
    @Builder.Default

    private String stdTeamNm="";

    /**
     * 系统编号
     */
    @Builder.Default

    private String sysId="";

    /**
     * 系统名称
     */
    @Builder.Default

    private String sysName = "";

    @Override
    public String toString() {
        return "BusinessOperateUser{" +
                "id:" + id +
                ", staffNo:'" + staffNo + '\'' +
                ", staffNm:'" + staffNm + '\'' +
                ", stdOrgNo:'" + stdOrgNo + '\'' +
                ", stdOrgNm:'" + stdOrgNm + '\'' +
                ", stdDeptNo:'" + stdDeptNo + '\'' +
                ", stdDeptNm:'" + stdDeptNm + '\'' +
                ", stdTeamNo:'" + stdTeamNo + '\'' +
                ", stdTeamNm:'" + stdTeamNm + '\'' +
                ", sysId:'" + sysId + '\'' +
                ", sysName:'" + sysName + '\'' +
                '}';
    }

}
