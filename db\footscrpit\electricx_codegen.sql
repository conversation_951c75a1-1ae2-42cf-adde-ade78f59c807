CREATE DATABASE electricx_codegen;
USE electricx_codegen;
/*
 Navicat Premium Data Transfer

 Source Server         : mysql8.0
 Source Server Type    : MySQL
 Source Server Version : 80027
 Source Host           : localhost:13309
 Source Schema         : electricx_codegen

 Target Server Type    : MySQL
 Target Server Version : 80027
 File Encoding         : 65001

 Date: 23/05/2025 16:57:11
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for gen_datasource_conf
-- ----------------------------
DROP TABLE IF EXISTS `gen_datasource_conf`;
CREATE TABLE `gen_datasource_conf`  (
  `id` bigint NOT NULL COMMENT '主键',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '别名',
  `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'jdbcurl',
  `username` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `password` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密码',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标记',
  `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户ID',
  `ds_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据库类型',
  `conf_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置类型',
  `ds_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据库名称',
  `instance` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '实例',
  `port` int NULL DEFAULT NULL COMMENT '端口',
  `host` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主机',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据源表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gen_datasource_conf
-- ----------------------------
INSERT INTO `gen_datasource_conf` VALUES (1767459683380023297, 'electricx_work', '****************************************************************************************************************************************************************************************************************************************************************', 'root', 'KiAcQWaEI1c0XHv+thL4mQ==', '2024-03-12 15:56:41', '2025-01-13 14:23:18', '1', 1, 'mysql', '1', NULL, NULL, 0, NULL);
INSERT INTO `gen_datasource_conf` VALUES (1772154515117641730, 'dw_igw', '********************************************************************************************************************************************************************************************************************************************************', 'root', 'QWW2NALT6N3GW699L+h+VA==', '2024-03-25 14:52:16', '2025-01-13 14:23:16', '1', 1, 'mysql', '1', NULL, NULL, 0, NULL);
INSERT INTO `gen_datasource_conf` VALUES (1853992292407439361, 'test_sys_operation_log', '************************************************************************************************************************************************************************************************************************************', 'root', '8We/2vJmX0jXMtzWUJZy/A==', '2024-11-06 10:46:23', '2025-01-13 14:23:12', '1', 1, 'mysql', '0', 'electricx_test', NULL, 3306, '127.0.0.1');
INSERT INTO `gen_datasource_conf` VALUES (1874760938376171521, 'yykb_test', '***********************************************************************************************************************************************************************************************************************************************************', 'root', 'VuDS3o9cE7CQGOc6EoRbag==', '2025-01-02 18:13:34', '2025-01-13 14:23:10', '1', 1, 'mysql', '1', NULL, NULL, 0, NULL);
INSERT INTO `gen_datasource_conf` VALUES (1878689345330782209, 'dw_igw', '*****************************************************************************************************************************************************************************************************************************', 'root', '2esvMYTra8xZxgsx340ydw==', '2025-01-13 14:23:39', '2025-01-13 14:23:38', '0', 1, 'mysql', '0', 'dw_igw', NULL, 13309, 'localhost');

-- ----------------------------
-- Table structure for gen_field_type
-- ----------------------------
DROP TABLE IF EXISTS `gen_field_type`;
CREATE TABLE `gen_field_type`  (
  `id` bigint NOT NULL COMMENT 'id',
  `column_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字段类型',
  `attr_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性类型',
  `package_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性包名',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标记',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `column_type`(`column_type`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字段类型管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gen_field_type
-- ----------------------------
INSERT INTO `gen_field_type` VALUES (1, 'datetime', 'LocalDateTime', 'java.time.LocalDateTime', '2023-02-06 08:45:10', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (2, 'date', 'LocalDate', 'java.time.LocalDate', '2023-02-06 08:45:10', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (3, 'tinyint', 'Integer', NULL, '2023-02-06 08:45:11', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (4, 'smallint', 'Integer', NULL, '2023-02-06 08:45:11', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (5, 'mediumint', 'Integer', NULL, '2023-02-06 08:45:11', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (6, 'int', 'Integer', NULL, '2023-02-06 08:45:11', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (7, 'integer', 'Integer', NULL, '2023-02-06 08:45:11', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (8, 'bigint', 'Long', NULL, '2023-02-06 08:45:11', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (9, 'float', 'Float', NULL, '2023-02-06 08:45:11', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (10, 'double', 'Double', NULL, '2023-02-06 08:45:11', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (11, 'decimal', 'BigDecimal', 'java.math.BigDecimal', '2023-02-06 08:45:11', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (12, 'bit', 'Boolean', NULL, '2023-02-06 08:45:11', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (13, 'char', 'String', NULL, '2023-02-06 08:45:11', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (14, 'varchar', 'String', NULL, '2023-02-06 08:45:11', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (15, 'tinytext', 'String', NULL, '2023-02-06 08:45:11', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (16, 'text', 'String', NULL, '2023-02-06 08:45:11', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (17, 'mediumtext', 'String', NULL, '2023-02-06 08:45:11', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (18, 'longtext', 'String', NULL, '2023-02-06 08:45:11', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (19, 'timestamp', 'LocalDateTime', 'java.time.LocalDateTime', '2023-02-06 08:45:11', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (20, 'NUMBER', 'Integer', NULL, '2023-02-06 08:45:11', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (21, 'BINARY_INTEGER', 'Integer', NULL, '2023-02-06 08:45:12', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (22, 'BINARY_FLOAT', 'Float', NULL, '2023-02-06 08:45:12', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (23, 'BINARY_DOUBLE', 'Double', NULL, '2023-02-06 08:45:12', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (24, 'VARCHAR2', 'String', NULL, '2023-02-06 08:45:12', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (25, 'NVARCHAR', 'String', NULL, '2023-02-06 08:45:12', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (26, 'NVARCHAR2', 'String', NULL, '2023-02-06 08:45:12', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (27, 'CLOB', 'String', NULL, '2023-02-06 08:45:12', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (28, 'int8', 'Long', NULL, '2023-02-06 08:45:12', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (29, 'int4', 'Integer', NULL, '2023-02-06 08:45:12', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (30, 'int2', 'Integer', NULL, '2023-02-06 08:45:12', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (31, 'numeric', 'BigDecimal', 'java.math.BigDecimal', '2023-02-06 08:45:12', NULL, NULL, NULL, '0');
INSERT INTO `gen_field_type` VALUES (32, 'json', 'String', NULL, '2023-02-06 08:45:12', NULL, NULL, NULL, '0');

-- ----------------------------
-- Table structure for gen_form_conf
-- ----------------------------
DROP TABLE IF EXISTS `gen_form_conf`;
CREATE TABLE `gen_form_conf`  (
  `id` bigint NOT NULL COMMENT 'ID',
  `ds_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据库名称',
  `table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名称',
  `form_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '表单信息',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0',
  `tenant_id` bigint NULL DEFAULT NULL COMMENT '所属租户',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `table_name`(`table_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '表单配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gen_form_conf
-- ----------------------------

-- ----------------------------
-- Table structure for gen_group
-- ----------------------------
DROP TABLE IF EXISTS `gen_group`;
CREATE TABLE `gen_group`  (
  `id` bigint NOT NULL,
  `group_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分组名称',
  `group_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分组描述',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT ' ' COMMENT '创建人',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT ' ' COMMENT '修改人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改人',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标记',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '模板分组' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gen_group
-- ----------------------------
INSERT INTO `gen_group` VALUES (1, '单表增删改查', '单表增删改查', 1, ' ', ' ', NULL, NULL, '0');
INSERT INTO `gen_group` VALUES (2, '主子表表增删改查', '主子表表增删改查', 1, ' ', ' ', NULL, NULL, '0');

-- ----------------------------
-- Table structure for gen_table
-- ----------------------------
DROP TABLE IF EXISTS `gen_table`;
CREATE TABLE `gen_table`  (
  `id` bigint NOT NULL COMMENT 'id',
  `table_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名',
  `class_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名',
  `db_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据库类型',
  `table_comment` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '说明',
  `author` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '作者',
  `email` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `package_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目包名',
  `version` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目版本号',
  `i18n` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '是否生成带有i18n 0 不带有 1带有',
  `style` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '代码风格',
  `child_table_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '子表名称',
  `main_field` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主表关联键',
  `child_field` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '子表关联键',
  `generator_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '生成方式  0：zip压缩包   1：自定义目录',
  `backend_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '后端生成路径',
  `frontend_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '前端生成路径',
  `module_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模块名',
  `function_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '功能名',
  `form_layout` tinyint NULL DEFAULT NULL COMMENT '表单布局  1：一列   2：两列',
  `ds_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据源ID',
  `baseclass_id` bigint NULL DEFAULT NULL COMMENT '基类ID',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `table_name`(`table_name`, `ds_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gen_table
-- ----------------------------
INSERT INTO `gen_table` VALUES (1772154554703482881, 'dw_igw_sys_detail', 'DwIgwSysDetail', 'mysql', 'igw应用列表', 'cwg', '<EMAIL>', 'com.operation.electric', '1.0.0', '0', '1', '', '', '', '0', 'electric', 'electric-ui', 'maintenance', 'dwIgwSysDetail', 2, 'dw_igw', NULL, '2024-03-25 14:52:26');
INSERT INTO `gen_table` VALUES (1772178958279839745, 'knowledge_base', 'KnowledgeBase', 'mysql', '知识库表(未入库的知识库文档)', 'pig', '<EMAIL>', 'com.operation.electric', '1.0.0', '0', '1', '', '', '', '0', 'electric', 'electric-ui', 'electric', 'knowledgeBase', 2, 'electricx_work', NULL, '2024-03-25 16:29:24');
INSERT INTO `gen_table` VALUES (1853992583605383169, 'sys_operation_log', 'SysOperationLog', 'mysql', '电网小助日志表', '熊世杰', '<EMAIL>', 'com.operation.electric', '1.0.0', '0', '1', '', '', '', '0', 'electric', 'electric-ui', 'maintenance', 'sysOperationLog', 2, 'test_sys_operation_log', NULL, '2024-11-06 10:47:32');
INSERT INTO `gen_table` VALUES (1853997833238212610, 'log_detail', 'LogDetail', 'mysql', '第三方系统日志', '熊世杰', '<EMAIL>', 'com.operation.electric', '1.0.0', '0', '1', '', '', '', '0', 'electric', 'electric-ui', 'maintenance', 'logDetail', 2, 'test_sys_operation_log', NULL, '2024-11-06 11:08:24');
INSERT INTO `gen_table` VALUES (1874761046283030530, 'log_detail', 'LogDetail', 'mysql', '日志系统，系统使用详情日志记录信息', 'pig', '<EMAIL>', 'com.operation.electric', '1.0.0', '0', '2', 'log_staff', 'staff_no', 'staff_no', '0', 'electric', 'electric-ui', 'electric', 'logDetail', 2, 'yykb_test', NULL, '2025-01-02 18:13:59');
INSERT INTO `gen_table` VALUES (1875007419897122817, 'log_staff', 'LogStaff', 'mysql', '日志系统人员基础档案表', 'pig', '<EMAIL>', 'com.operation.electric', '1.0.0', '0', '0', NULL, NULL, NULL, '0', 'electric', 'electric-ui', 'electric', 'logStaff', 2, 'yykb_test', NULL, '2025-01-03 10:33:00');
INSERT INTO `gen_table` VALUES (1878690992123584513, 'dw_igw_sys_application_direction', 'DwIgwSysApplicationDirection', 'mysql', '应用方向', 'YiJiangHua', '<EMAIL>', 'com.operation.electric', '1.0.0', '0', '1', '', '', '', '1', 'D:\\CS-PROJECT\\ticket-center-api\\electric-maintenance\\electric-maintenance-biz', 'C:\\Users\\<USER>\\Desktop\\BD-CODE\\DwIgwSysApplicationDirection', 'yykb', 'dwIgwSysApplicationDirection', 2, 'dw_igw', NULL, '2025-01-13 14:30:12');
INSERT INTO `gen_table` VALUES (1878691022637146113, 'dw_igw_sys_application_class', 'DwIgwSysApplicationClass', 'mysql', '应用大类表单', 'YiJiangHua', '<EMAIL>', 'com.operation.electric', '1.0.0', '0', '1', '', '', '', '1', 'D:\\CS-PROJECT\\ticket-center-api\\electric-maintenance\\electric-maintenance-biz', 'C:\\Users\\<USER>\\Desktop\\BD-CODE\\DwIgwSysApplicationClass', 'yykb', 'dwIgwSysApplicationClass', 2, 'dw_igw', NULL, '2025-01-13 14:30:19');
INSERT INTO `gen_table` VALUES (1878744383281471490, 'dw_igw_sys_application_logs', 'DwIgwSysApplicationLogs', 'mysql', '上下线日志记录表单', 'YiJiangHua', '<EMAIL>', 'com.operation.electric', '1.0.0', '0', '1', '', '', '', '1', 'D:\\CS-PROJECT\\ticket-center-api\\electric-maintenance\\electric-maintenance-biz', 'C:\\Users\\<USER>\\Desktop\\BD-CODE\\DwIgwSysApplicationLogs', 'yykb', 'dwIgwSysApplicationLogs', 2, 'dw_igw', NULL, '2025-01-13 18:02:21');
INSERT INTO `gen_table` VALUES (1879785581672697858, 'dw_igw_sys_notice', 'DwIgwSysNotice', 'mysql', 'APP首页最上方广播通知表', 'YiJiangHua', '<EMAIL>', 'com.operation.electric', '1.0.0', '0', '1', '', '', '', '1', 'D:\\CS-PROJECT\\ticket-center-api\\electric-maintenance\\electric-maintenance-biz', 'C:\\Users\\<USER>\\Desktop\\BD-CODE\\DwIgwSysApplicationLogs', 'yykb', 'dwIgwSysNotice', 2, 'dw_igw', NULL, '2025-01-16 14:59:42');

-- ----------------------------
-- Table structure for gen_table_column
-- ----------------------------
DROP TABLE IF EXISTS `gen_table_column`;
CREATE TABLE `gen_table_column`  (
  `id` bigint NOT NULL COMMENT 'id',
  `ds_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据源名称',
  `table_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名称',
  `field_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字段名称',
  `field_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字段类型',
  `field_comment` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字段说明',
  `attr_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性名',
  `attr_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性类型',
  `package_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性包名',
  `sort` int NULL DEFAULT NULL COMMENT '排序',
  `auto_fill` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '自动填充  DEFAULT、INSERT、UPDATE、INSERT_UPDATE',
  `primary_pk` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '主键 0：否  1：是',
  `base_field` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '基类字段 0：否  1：是',
  `form_item` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '表单项 0：否  1：是',
  `form_required` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '表单必填 0：否  1：是',
  `form_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表单类型',
  `form_validator` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表单效验',
  `grid_item` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '列表项 0：否  1：是',
  `grid_sort` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '列表排序 0：否  1：是',
  `query_item` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '查询项 0：否  1：是',
  `query_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '查询方式',
  `query_form_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '查询表单类型',
  `field_dict` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典类型',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成表字段' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gen_table_column
-- ----------------------------
INSERT INTO `gen_table_column` VALUES (1772154554938363905, 'dw_igw', 'dw_igw_sys_detail', 'xh', 'varchar', '序号', 'xh', 'String', NULL, 0, 'DEFAULT', '1', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772154554938363906, 'dw_igw', 'dw_igw_sys_detail', 'app_id', 'varchar', '应用编号', 'appId', 'String', NULL, 1, 'DEFAULT', '0', '0', '1', '1', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772154554963529729, 'dw_igw', 'dw_igw_sys_detail', 'sys_name', 'varchar', '应用名称', 'sysName', 'String', NULL, 2, 'DEFAULT', '0', '0', '1', '1', 'text', NULL, '1', '0', '1', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772154554963529730, 'dw_igw', 'dw_igw_sys_detail', 'sys_img_url', 'varchar', '图标路径', 'sysImgUrl', 'String', NULL, 3, 'DEFAULT', '0', '0', '1', '1', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772154554963529731, 'dw_igw', 'dw_igw_sys_detail', 'sys_link', 'varchar', '应用链接', 'sysLink', 'String', NULL, 4, 'DEFAULT', '0', '0', '1', '1', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772154554963529732, 'dw_igw', 'dw_igw_sys_detail', 'sys_type', 'varchar', '应用环境类型', 'sysType', 'String', NULL, 5, 'DEFAULT', '0', '0', '1', '1', 'select', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772154554963529733, 'dw_igw', 'dw_igw_sys_detail', 'sys_class', 'varchar', '应用类别', 'sysClass', 'String', NULL, 6, 'DEFAULT', '0', '0', '1', '1', 'select', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772154554963529734, 'dw_igw', 'dw_igw_sys_detail', 'sort_nm', 'int', '排序编号', 'sortNm', 'Integer', NULL, 7, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772154554963529735, 'dw_igw', 'dw_igw_sys_detail', 'sys_fac', 'varchar', '应用所属厂家', 'sysFac', 'String', NULL, 8, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772154554963529736, 'dw_igw', 'dw_igw_sys_detail', 'open_type', 'varchar', '应用打开方式', 'openType', 'String', NULL, 9, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772154554963529737, 'dw_igw', 'dw_igw_sys_detail', 'create_by', 'varchar', '创建人', 'createBy', 'String', NULL, 10, 'INSERT', '0', '0', '0', '0', 'text', NULL, '0', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772154554963529738, 'dw_igw', 'dw_igw_sys_detail', 'create_time', 'datetime', '创建时间', 'createTime', 'LocalDateTime', 'java.time.LocalDateTime', 11, 'INSERT', '0', '0', '0', '0', 'text', NULL, '0', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772154554963529739, 'dw_igw', 'dw_igw_sys_detail', 'update_by', 'varchar', '更新人', 'updateBy', 'String', NULL, 12, 'INSERT_UPDATE', '0', '0', '0', '0', 'text', NULL, '0', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772154554963529740, 'dw_igw', 'dw_igw_sys_detail', 'update_time', 'datetime', '更新时间', 'updateTime', 'LocalDateTime', 'java.time.LocalDateTime', 13, 'INSERT_UPDATE', '0', '0', '0', '0', 'text', NULL, '0', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772178958426640385, 'electricx_work', 'knowledge_base', 'id', 'bigint', '主键id', 'id', 'Long', NULL, 0, 'DEFAULT', '1', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772178958426640386, 'electricx_work', 'knowledge_base', 'file_name', 'varchar', '文件名(关联electricx的sys_file表)', 'fileName', 'String', NULL, 1, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772178958426640387, 'electricx_work', 'knowledge_base', 'status', 'bigint', '状态(0:未入库  1:已入库)', 'status', 'Long', NULL, 2, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772178958426640388, 'electricx_work', 'knowledge_base', 'original_name', 'varchar', '文件原始名称', 'originalName', 'String', NULL, 3, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772178958426640389, 'electricx_work', 'knowledge_base', 'create_by', 'varchar', '创建人', 'createBy', 'String', NULL, 100, 'INSERT', '0', '0', '0', '0', 'text', NULL, '0', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772178958426640390, 'electricx_work', 'knowledge_base', 'update_by', 'varchar', '修改人', 'updateBy', 'String', NULL, 102, 'INSERT_UPDATE', '0', '0', '0', '0', 'text', NULL, '0', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772178958426640391, 'electricx_work', 'knowledge_base', 'create_time', 'datetime', '上传时间', 'createTime', 'LocalDateTime', 'java.time.LocalDateTime', 101, 'INSERT', '0', '0', '0', '0', 'text', NULL, '0', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772178958451806210, 'electricx_work', 'knowledge_base', 'update_time', 'datetime', '更新时间', 'updateTime', 'LocalDateTime', 'java.time.LocalDateTime', 103, 'INSERT_UPDATE', '0', '0', '0', '0', 'text', NULL, '0', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772178958451806211, 'electricx_work', 'knowledge_base', 'del_flag', 'char', '删除标志', 'delFlag', 'String', NULL, 104, 'DEFAULT', '0', '0', '0', '0', 'text', NULL, '0', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772178958451806212, 'electricx_work', 'knowledge_base', 'tenant_id', 'bigint', '所属租户', 'tenantId', 'Long', NULL, 105, 'DEFAULT', '0', '0', '0', '0', 'text', NULL, '0', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772178958451806213, 'electricx_work', 'knowledge_base', 'url', 'varchar', '文件地址', 'url', 'String', NULL, 4, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772178958451806214, 'electricx_work', 'knowledge_base', 'max_length', 'bigint', '单段文本最大长度', 'maxLength', 'Long', NULL, 5, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772178958451806215, 'electricx_work', 'knowledge_base', 'coincident_length', 'bigint', '相邻文本重合长度', 'coincidentLength', 'Long', NULL, 6, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1772178958451806216, 'electricx_work', 'knowledge_base', 'zh_title_enhance', 'tinyint', '是否开启中文标题加强 0 =未开启 1=开启', 'zhTitleEnhance', 'Integer', NULL, 7, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853992584016424961, 'test_sys_operation_log', 'sys_operation_log', 'operation_log_id', 'bigint', '主键', 'operationLogId', 'Long', NULL, 0, 'DEFAULT', '1', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853992584041590786, 'test_sys_operation_log', 'sys_operation_log', 'log_type', 'varchar', '日志类型', 'logType', 'String', NULL, 1, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', '外部日志');
INSERT INTO `gen_table_column` VALUES (1853992584054173698, 'test_sys_operation_log', 'sys_operation_log', 'log_name', 'varchar', '日志名称', 'logName', 'String', NULL, 2, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', 'like', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853992584066756609, 'test_sys_operation_log', 'sys_operation_log', 'user_id', 'bigint', '用户名称', 'userName', 'String', NULL, 3, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', 'like', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853992584075145217, 'test_sys_operation_log', 'sys_operation_log', 'class_name', 'varchar', '类名称', 'className', 'String', NULL, 4, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853992584075145218, 'test_sys_operation_log', 'sys_operation_log', 'method', 'text', '方法名称', 'method', 'String', NULL, 5, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853992584087728130, 'test_sys_operation_log', 'sys_operation_log', 'create_time', 'datetime', '创建时间', 'createTime', 'LocalDateTime', 'java.time.LocalDateTime', 13, 'INSERT', '0', '0', '0', '0', 'text', NULL, '0', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853992584112893954, 'test_sys_operation_log', 'sys_operation_log', 'succeed', 'varchar', '是否成功', 'succeed', 'String', NULL, 6, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853992584129671170, 'test_sys_operation_log', 'sys_operation_log', 'message', 'text', '备注', 'message', 'String', NULL, 7, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853992584138059777, 'test_sys_operation_log', 'sys_operation_log', 'ip_addr', 'varchar', 'IP地址', 'ipAddr', 'String', NULL, 8, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853992584142254081, 'test_sys_operation_log', 'sys_operation_log', 'result', 'longtext', '返回参数', 'result', 'String', NULL, 9, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853992584146448386, 'test_sys_operation_log', 'sys_operation_log', 'request_url', 'varchar', '请求地址', 'requestUrl', 'String', NULL, 10, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853992584154836994, 'test_sys_operation_log', 'sys_operation_log', 'request_param', 'longtext', '请求参数', 'requestParam', 'String', NULL, 11, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853992584167419905, 'test_sys_operation_log', 'sys_operation_log', 'log_level', 'varchar', '日志等级', 'logLevel', 'String', NULL, 12, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853997833577951233, 'test_sys_operation_log', 'log_detail', 'id', 'int', '日志主键', 'id', 'Integer', NULL, 0, 'DEFAULT', '1', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853997833603117057, 'test_sys_operation_log', 'log_detail', 'created_at', 'datetime', '记录时间', 'createdAt', 'LocalDateTime', 'java.time.LocalDateTime', 1, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853997833636671489, 'test_sys_operation_log', 'log_detail', 'updated_at', 'datetime', '更新时间', 'updatedAt', 'LocalDateTime', 'java.time.LocalDateTime', 2, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '0', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853997833640865793, 'test_sys_operation_log', 'log_detail', 'staff_no', 'varchar', '账号名称', 'staffNo', 'String', NULL, 3, 'DEFAULT', '0', '0', '1', '1', 'text', NULL, '1', '0', '1', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853997833674420226, 'test_sys_operation_log', 'log_detail', 'timestamp', 'datetime', '操作时间', 'timestamp', 'LocalDateTime', 'java.time.LocalDateTime', 4, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853997833691197442, 'test_sys_operation_log', 'log_detail', 'operation_class1', 'varchar', '一级业务名称', 'operationClass1', 'String', NULL, 5, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '1', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853997833724751874, 'test_sys_operation_log', 'log_detail', 'operation_class2', 'varchar', '二级业务名称', 'operationClass2', 'String', NULL, 6, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853997833728946177, 'test_sys_operation_log', 'log_detail', 'operation_class3', 'varchar', '三级业务名称', 'operationClass3', 'String', NULL, 7, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853997833728946178, 'test_sys_operation_log', 'log_detail', 'operation_class4', 'varchar', '四级业务名称', 'operationClass4', 'String', NULL, 8, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853997833728946179, 'test_sys_operation_log', 'log_detail', 'operation_class5', 'varchar', '五级业务名称', 'operationClass5', 'String', NULL, 9, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853997833749917697, 'test_sys_operation_log', 'log_detail', 'ip', 'varchar', 'IP地址', 'ip', 'String', NULL, 10, 'DEFAULT', '0', '0', '1', '1', 'text', 'noChinese', '1', '0', '1', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853997833749917698, 'test_sys_operation_log', 'log_detail', 'sys_id', 'varchar', '接入系统编号', 'sysId', 'String', NULL, 11, 'DEFAULT', '0', '0', '1', '1', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853997833758306305, 'test_sys_operation_log', 'log_detail', 'sys_name', 'varchar', '接入系统名称', 'sysName', 'String', NULL, 12, 'DEFAULT', '0', '0', '1', '1', 'text', NULL, '1', '0', '1', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853997833762500610, 'test_sys_operation_log', 'log_detail', 'sys_version', 'varchar', '接入系统版本', 'sysVersion', 'String', NULL, 13, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853997833762500611, 'test_sys_operation_log', 'log_detail', 'online_time', 'decimal', '在线时间', 'onlineTime', 'BigDecimal', 'java.math.BigDecimal', 14, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853997833796055041, 'test_sys_operation_log', 'log_detail', 'vip_flag', 'int', '是否为重要用户', 'vipFlag', 'Integer', NULL, 15, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '1', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1853997833837998082, 'test_sys_operation_log', 'log_detail', 'server_ip', 'varchar', '访问系统IP', 'serverIp', 'String', NULL, 16, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '1', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1874761046543077377, 'yykb_test', 'log_detail', 'id', 'varchar', '日志记录唯一编号UUID', 'id', 'String', NULL, 0, 'DEFAULT', '1', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1874761046610186241, 'yykb_test', 'log_detail', 'created_at', 'datetime', '数据记录时间', 'createdAt', 'LocalDateTime', 'java.time.LocalDateTime', 1, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1874761046610186242, 'yykb_test', 'log_detail', 'updated_at', 'datetime', '数据更新时间', 'updatedAt', 'LocalDateTime', 'java.time.LocalDateTime', 2, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1874761046610186243, 'yykb_test', 'log_detail', 'staff_no', 'varchar', '电力统一权限登陆平台OA账号', 'staffNo', 'String', NULL, 3, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1874761046610186244, 'yykb_test', 'log_detail', 'timestamp', 'datetime', '动作时间，使用app操作的时间点', 'timestamp', 'LocalDateTime', 'java.time.LocalDateTime', 4, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1874761046677295105, 'yykb_test', 'log_detail', 'operation_class1', 'varchar', '一级操作名称。例如：登陆/退出登陆/超超时退出', 'operationClass1', 'String', NULL, 5, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '1', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1874761046677295106, 'yykb_test', 'log_detail', 'operation_class2', 'varchar', '二级操作名称。例如：进入数字员工的电费明细清单机器人列表。则一级菜单为营销。二级操作名称为：电费明细清单打印机器人', 'operationClass2', 'String', NULL, 6, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1874761046677295107, 'yykb_test', 'log_detail', 'operation_class3', 'varchar', '三级操作名称', 'operationClass3', 'String', NULL, 7, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1874761046677295108, 'yykb_test', 'log_detail', 'operation_class4', 'varchar', '四级操作名称', 'operationClass4', 'String', NULL, 8, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1874761046677295109, 'yykb_test', 'log_detail', 'operation_class5', 'varchar', '五级操作名称', 'operationClass5', 'String', NULL, 9, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1874761046677295110, 'yykb_test', 'log_detail', 'ip', 'varchar', '使用系统时客户的电脑IP', 'ip', 'String', NULL, 10, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1874761046677295111, 'yykb_test', 'log_detail', 'sys_id', 'varchar', '接入日志系统 的第三方应用/系统的编号', 'sysId', 'String', NULL, 11, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1874761046677295112, 'yykb_test', 'log_detail', 'sys_name', 'varchar', '接入日志系统 的第三方应用/系统的名称', 'sysName', 'String', NULL, 12, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1874761046677295113, 'yykb_test', 'log_detail', 'sys_version', 'varchar', '接入日志系统 的第三方应用/系统的版本', 'sysVersion', 'String', NULL, 13, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1874761046677295114, 'yykb_test', 'log_detail', 'online_time', 'decimal', '在线时间(秒)', 'onlineTime', 'BigDecimal', 'java.math.BigDecimal', 14, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1874761046744403970, 'yykb_test', 'log_detail', 'vip_flag', 'int', '重要用户标记', 'vipFlag', 'Integer', NULL, 15, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1874761046744403971, 'yykb_test', 'log_detail', 'server_ip', 'varchar', '推送日志服务器ip', 'serverIp', 'String', NULL, 16, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1874761046744403972, 'yykb_test', 'log_detail', 'response_time', 'int', '响应时间', 'responseTime', 'Integer', NULL, 17, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1875007420278804482, 'yykb_test', 'log_staff', 'id', 'int', '主键id', 'id', 'Integer', NULL, 0, 'DEFAULT', '1', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1875007420333330434, 'yykb_test', 'log_staff', 'staff_no', 'varchar', '员工编码', 'staffNo', 'String', NULL, 1, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1875007420333330435, 'yykb_test', 'log_staff', 'staff_nm', 'varchar', '员工姓名', 'staffNm', 'String', NULL, 2, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1875007420333330436, 'yykb_test', 'log_staff', 'std_org_no', 'varchar', '标准单位编码', 'stdOrgNo', 'String', NULL, 3, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1875007420333330437, 'yykb_test', 'log_staff', 'std_org_nm', 'varchar', '标准单位名称', 'stdOrgNm', 'String', NULL, 4, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1875007420333330438, 'yykb_test', 'log_staff', 'std_dept_no', 'varchar', '标准部门编码', 'stdDeptNo', 'String', NULL, 5, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1875007420333330439, 'yykb_test', 'log_staff', 'std_dept_nm', 'varchar', '标准部门名称', 'stdDeptNm', 'String', NULL, 6, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1875007420333330440, 'yykb_test', 'log_staff', 'std_team_no', 'varchar', '标准班组编码', 'stdTeamNo', 'String', NULL, 7, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1875007420371079169, 'yykb_test', 'log_staff', 'std_team_nm', 'varchar', '标准班组名称', 'stdTeamNm', 'String', NULL, 8, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1875007420371079170, 'yykb_test', 'log_staff', 'sys_id', 'varchar', '系统编号', 'sysId', 'String', NULL, 9, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1875007420371079171, 'yykb_test', 'log_staff', 'sys_name', 'varchar', '系统名称', 'sysName', 'String', NULL, 10, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1875007420371079172, 'yykb_test', 'log_staff', 'created_at', 'datetime', '创建时间', 'createdAt', 'LocalDateTime', 'java.time.LocalDateTime', 11, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1875007420425605122, 'yykb_test', 'log_staff', 'updated_at', 'datetime', '更新时间', 'updatedAt', 'LocalDateTime', 'java.time.LocalDateTime', 12, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1875007420425605123, 'yykb_test', 'log_staff', 'server_ip', 'varchar', '推送日志服务器ip', 'serverIp', 'String', NULL, 13, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1875007420450770946, 'yykb_test', 'log_staff', 'std_dept_nm_new', 'varchar', NULL, 'stdDeptNmNew', 'String', NULL, 14, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1878690992249413633, 'dw_igw', 'dw_igw_sys_application_direction', 'id', 'bigint', '主键ID', 'id', 'Long', NULL, 0, 'DEFAULT', '1', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1878690992249413634, 'dw_igw', 'dw_igw_sys_application_direction', 'name', 'varchar', '应用方向', 'name', 'String', NULL, 1, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1878691022746198018, 'dw_igw', 'dw_igw_sys_application_class', 'id', 'bigint', '主键', 'id', 'Long', NULL, 0, 'DEFAULT', '1', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1878691022746198019, 'dw_igw', 'dw_igw_sys_application_class', 'application_name', 'varchar', '名称', 'applicationName', 'String', NULL, 1, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1878744383411494913, 'dw_igw', 'dw_igw_sys_application_logs', 'id', 'bigint', '主键ID', 'id', 'Long', NULL, 0, 'DEFAULT', '1', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1878744383411494914, 'dw_igw', 'dw_igw_sys_application_logs', 'app_id', 'varchar', '应用编号', 'appId', 'String', NULL, 1, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1878744383411494915, 'dw_igw', 'dw_igw_sys_application_logs', 'app_name', 'varchar', '应用名称', 'appName', 'String', NULL, 2, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1878744383411494916, 'dw_igw', 'dw_igw_sys_application_logs', 'app_env', 'varchar', '应用环境', 'appEnv', 'String', NULL, 3, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1878744383411494917, 'dw_igw', 'dw_igw_sys_application_logs', 'app_fac', 'varchar', '所属厂家', 'appFac', 'String', NULL, 4, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1878744383411494918, 'dw_igw', 'dw_igw_sys_application_logs', 'app_create_by', 'varchar', '操作人', 'appCreateBy', 'String', NULL, 5, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1878744383411494919, 'dw_igw', 'dw_igw_sys_application_logs', 'app_create_time', 'datetime', '发生时间', 'appCreateTime', 'LocalDateTime', 'java.time.LocalDateTime', 6, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1878744383474409474, 'dw_igw', 'dw_igw_sys_application_logs', 'app_opreation_detail', 'varchar', '操作详情', 'appOpreationDetail', 'String', NULL, 7, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1878744383474409475, 'dw_igw', 'dw_igw_sys_application_logs', 'app_opreation_result', 'varchar', '操作结果', 'appOpreationResult', 'String', NULL, 8, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1879785581806915585, 'dw_igw', 'dw_igw_sys_notice', 'id', 'bigint', '消息ID', 'id', 'Long', NULL, 0, 'DEFAULT', '1', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1879785581806915586, 'dw_igw', 'dw_igw_sys_notice', 'msg_content', 'text', '可发送html文本格式的消息', 'msgContent', 'String', NULL, 1, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1879785581806915587, 'dw_igw', 'dw_igw_sys_notice', 'msg_type', 'varchar', '消息类型（大数据、报表、RPA）', 'msgType', 'String', NULL, 2, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1879785581806915588, 'dw_igw', 'dw_igw_sys_notice', 'msg_emergency', 'tinyint', '消息紧急程度（0正常 1中等 2紧急）', 'msgEmergency', 'Integer', NULL, 3, 'DEFAULT', '0', '0', '1', '0', 'text', NULL, '1', '0', '0', '=', 'text', NULL);
INSERT INTO `gen_table_column` VALUES (1879785581806915589, 'dw_igw', 'dw_igw_sys_notice', 'create_time', 'datetime', '消息发布时间', 'createTime', 'LocalDateTime', 'java.time.LocalDateTime', 4, 'INSERT', '0', '0', '0', '0', 'text', NULL, '0', '0', '0', '=', 'text', NULL);

-- ----------------------------
-- Table structure for gen_template
-- ----------------------------
DROP TABLE IF EXISTS `gen_template`;
CREATE TABLE `gen_template`  (
  `id` bigint NOT NULL COMMENT '主键',
  `template_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板名称',
  `generator_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板路径',
  `template_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板描述',
  `template_code` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板代码',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '删除标记',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `create_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT ' ' COMMENT '创建人',
  `update_by` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT ' ' COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '模板' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gen_template
-- ----------------------------
INSERT INTO `gen_template` VALUES (1, 'vform.json', '/', '表单设计器初始化json模板', '#set($key=${dateTool.getSystemTime()})\n{\n  \"widgetList\": [\n    {\n      \"key\": $key,\n      \"type\": \"grid\",\n      \"category\": \"container\",\n      \"icon\": \"grid\",\n      \"cols\": [\n#foreach($field in $formList)\n#if($field.attrName != ${pk.attrName})\n        {\n          \"type\": \"grid-col\",\n          \"category\": \"container\",\n          \"icon\": \"grid-col\",\n          \"internal\": true,\n          \"widgetList\": [\n            {\n              \"key\": ${math.add($key,${foreach.index})},\n	#if($field.formType == \'text\')\n              \"type\": \"input\",\n              \"icon\": \"text-field\",\n	#elseif($field.formType == \'number\')\n              \"type\": \"number\",\n              \"icon\": \"number-field\",\n	#elseif($field.formType == \'textarea\')\n              \"type\": \"textarea\",\n              \"icon\": \"textarea-field\",\n	#elseif($field.formType == \'select\' && ${field.fieldDict})\n              \"type\": \"select\",\n              \"icon\": \"select-field\",\n	#elseif($field.formType == \'radio\' && ${field.fieldDict})\n              \"type\": \"radio\",\n              \"icon\": \"radio-field\",\n	#elseif($field.formType == \'checkbox\'  && ${field.fieldDict} )\n              \"type\": \"checkbox\",\n              \"icon\": \"checkbox-field\",\n	#elseif($field.formType == \'date\')\n              \"type\": \"date\",\n              \"icon\": \"date-field\",\n	#elseif($field.formType == \'datetime\')\n              \"type\": \"time\",\n              \"icon\": \"time-field\",\n	#elseif($field.formType == \'upload-file\')\n              \"type\": \"file-upload\",\n              \"icon\": \"file-upload-field\",\n	#elseif($field.formType == \'upload-img\')\n              \"type\": \"picture-upload\",\n              \"icon\": \"picture-upload-field\",\n	#elseif($field.formType == \'editor\')\n              \"type\": \"rich-editor\",\n              \"icon\": \"rich-editor-field\",\n	#else\n              \"type\": \"input\",\n              \"icon\": \"text-field\",\n	#end\n              \"formItemFlag\": true,\n              \"options\": {\n	                \"name\": \"${field.attrName}\",\n	                \"label\": \"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\",\n	#if(($field.formType == \'select\' || $field.formType == \'radio\' || $field.formType == \'checkbox\') && ${field.fieldDict})\n                    \"optionItemsDictType\": \"${field.fieldDict}\",\n	#end\n                    \"placeholder\": \"请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\"\n              },\n    #if($field.formRequired)\n             \"required\": true,\n    #end\n              \"id\": \"input${math.add($key,${foreach.index})}\"\n            }\n          ],\n          \"options\": {\n            \"name\": \"gridCol${math.add($key,${foreach.index})}\",\n            \"hidden\": false,\n            \"offset\": 0,\n            \"push\": 0,\n            \"pull\": 0,\n	#if($formLayout == 1)\n            \"span\": 24,\n	#elseif($formLayout == 2)\n            \"span\": 12,\n	#end\n            \"responsive\": false\n          },\n          \"id\": \"grid-col-${math.add($key,${foreach.index})}\"\n        }#if($foreach.hasNext),#end\n#end\n#end\n      ],\n      \"options\": {\n        \"name\": \"grid${functionName}\",\n        \"hidden\": false,\n        \"gutter\": 12\n      },\n      \"id\": \"grid${functionName}\"\n    }\n  ],\n  \"formConfig\": {\n    \"modelName\": \"form\",\n    \"refName\": \"form\",\n    \"rulesName\": \"rules\",\n    \"labelWidth\": 80,\n    \"labelPosition\": \"left\",\n    \"labelAlign\": \"label-left-align\",\n    \"layoutType\": \"PC\",\n    \"jsonVersion\": 3\n  }\n}', '2023-02-23 04:33:16', '2023-06-04 10:35:51', '0', 1, ' ', ' ');
INSERT INTO `gen_template` VALUES (2, 'vform.vue', '/', '表单设计器生成sfc模板', '<template>\n    <el-dialog :title=\"form.${pk.attrName} ? \'编辑\' : \'新增\'\" v-model=\"visible\" :close-on-click-modal=\"false\" draggable>\n      <el-form ref=\"dataFormRef\" :model=\"form\" :rules=\"dataRules\" formDialogRef label-width=\"90px\">\n#foreach($key in $resultMap.keySet())\n#set($itemList = $resultMap.get($key))\n<el-row :gutter=\"24\">\n#foreach($field in $itemList)\n  <el-col :span=\"$field.span\">\n#if($field.type == \'input\')\n        <el-form-item label=\"${field.options.label}\" prop=\"${field.options.name}\">\n          <el-input v-model=\"form.${field.options.name}\" placeholder=\"${field.options.placeholder}\"/>\n        </el-form-item>\n#elseif($field.type == \'number\')\n        <el-form-item label=\"${field.options.label}\" prop=\"${field.options.name}\">\n          <el-input-number :min=\"${field.options.min}\" :max=\"${field.options.max}\" v-model=\"form.${field.options.name}\" placeholder=\"${field.options.placeholder}\"></el-input-number>\n        </el-form-item>\n#elseif($field.type == \'textarea\')\n        <el-form-item label=\"${field.options.label}\" prop=\"${field.options.name}\">\n          <el-input type=\"textarea\" :rows=\"${field.options.rows}\" v-model=\"form.${field.options.name}\" placeholder=\"${field.options.placeholder}\"/>\n        </el-form-item>\n#elseif($field.type == \'select\')\n        <el-form-item label=\"${field.options.label}\" prop=\"${field.options.name}\">\n            <el-select v-model=\"form.${field.options.name}\" placeholder=\"${field.options.placeholder}\">\n       #if($field.options.optionItemsDictType)\n                <el-option :value=\"item.value\" :label=\"item.label\" v-for=\"(item, index) in ${field.options.optionItemsDictType}\" :key=\"index\"></el-option>\n       #else\n                <el-option label=\"请选择\">0</el-option>\n       #end\n            </el-select>\n        </el-form-item>\n#elseif($field.type == \'radio\')\n        <el-form-item label=\"${field.options.label}\" prop=\"${field.options.name}\">\n            <el-radio-group v-model=\"form.${field.options.name}\">\n       #if($field.options.optionItemsDictType)\n             <el-radio :label=\"item.value\" v-for=\"(item, index) in ${field.options.optionItemsDictType}\" border :key=\"index\">{{ item.label }}\n              </el-radio>\n       #end\n            </el-radio-group>\n        </el-form-item>\n#elseif($field.type == \'checkbox\')\n        <el-form-item label=\"${field.options.label}\" prop=\"${field.options.name}\">\n            <el-checkbox-group v-model=\"form.${field.options.name}\">\n       #if($field.options.optionItemsDictType)\n                <el-checkbox :label=\"item.value\" :name=\"item.label\" v-for=\"(item, index) in ${field.options.optionItemsDictType}\" :key=\"index\"></el-checkbox>\n       #else\n                <el-checkbox label=\"启用\" name=\"type\"></el-checkbox>\n                <el-checkbox label=\"禁用\" name=\"type\"></el-checkbox>\n       #end\n            </<el-checkbox-group>\n        </el-form-item>\n#elseif($field.type == \'date\')\n        <el-form-item label=\"${field.options.label}\" prop=\"${field.options.name}\">\n            <el-date-picker type=\"date\" placeholder=\"${field.options.placeholder}\" v-model=\"form.${field.options.name}\" :value-format=\"dateStr\"></el-date-picker>\n        </el-form-item>\n#elseif($field.type == \'time\')\n        <el-form-item label=\"${field.options.label}\" prop=\"${field.options.name}\">\n            <el-time-picker placeholder=\"${field.options.placeholder}\" v-model=\"form.${field.options.name}\" :value-format=\"dateTimeStr\"></el-date-picker>\n        </el-form-item>\n#elseif($field.type == \'file-upload\')\n        <el-form-item label=\"${field.options.label}\" prop=\"${field.options.name}\">\n            <upload-file  v-model=\"form.${field.attrName}\" limit=\"${field.options.limit}\" fileMaxSize=\"${field.options.fileMaxSize}\"></upload-file>\n        </el-form-item>\n#elseif($field.type == \'picture-upload\')\n        <el-form-item label=\"${field.options.label}\" prop=\"${field.options.name}\">\n            <upload-img v-model:imageUrl=\"form.${field.options.name}\" limit=\"${field.options.limit}\" fileMaxSize=\"${field.options.fileMaxSize}\"></upload-img>\n        </el-form-item>\n#elseif($field.type == \'rich-editor\')\n          <el-form-item label=\"${field.options.label}\" prop=\"${field.options.name}\">\n            <editor v-model:get-html=\"form.${field.options.name}\" placeholder=\"${field.options.placeholder}\"></editor>\n          </el-form-item>\n#elseif($field.type == \'switch\')\n          <el-form-item label=\"${field.options.label}\" prop=\"${field.options.name}\">\n          <el-switch v-model=\"form.${field.options.name}\" />\n          </el-form-item>\n#elseif($field.type == \'rate\')\n        <el-form-item label=\"${field.options.label}\" prop=\"${field.options.name}\">\n          <el-rate v-model=\"form.${field.options.name}\" />\n      </el-form-item>\n#elseif($field.type == \'slider\')\n        <el-form-item label=\"${field.options.label}\" prop=\"${field.options.name}\">\n          <el-slider v-model=\"form.${field.options.name}\" />\n      </el-form-item>\n#elseif($field.type == \'color\')\n        <el-form-item label=\"${field.options.label}\" prop=\"${field.options.name}\">\n          <el-color-picker v-model=\"form.${field.options.name}\" />\n      </el-form-item>\n#elseif($field.type == \'static-text\' || $field.type == \'html-text\')\n        <span>{{form.${field.options.name}}}</span>          \n#elseif($field.type == \'divider\')\n      <el-divider />\n#else\n      <el-form-item label=\"${field.options.label}\" prop=\"${field.options.name}\">\n        <el-input v-model=\"form.${field.options.name}\" placeholder=\"${field.options.placeholder}\"/>\n      </el-form-item>\n#end\n  </el-col>\n#end\n</el-row>\n#end\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"visible = false\" formDialogRef>取消</el-button>\n          <el-button type=\"primary\" @click=\"onSubmit\" formDialogRef>确认</el-button>\n        </span>\n      </template>\n    </el-dialog>\n</template>\n\n<script setup lang=\"ts\" name=\"${ClassName}Dialog\">\nimport { useDict } from \'/@/hooks/dict\';\nimport { useMessage } from \"/@/hooks/message\";\nimport { getObj, addObj, putObj } from \'/@/api/${moduleName}/${functionName}\'\nimport { rule } from \'/@/utils/validate\';\nconst emit = defineEmits([\'refresh\']);\n\n// 定义变量内容\nconst dataFormRef = ref();\nconst visible = ref(false)\n// 定义字典\n#set($fieldDict=[])\n#foreach($key in $resultMap.keySet())\n#set($itemList = $resultMap.get($key))\n#foreach($field in $itemList)\n   #if($field.options.optionItemsDictType)\n        #set($void=$fieldDict.add($field.options.optionItemsDictType))\n    #end\n#end\n#end\n#if($fieldDict)\nconst { $dict.format($fieldDict) } = useDict($dict.quotation($fieldDict))\n#end\n\n// 提交表单数据\nconst form = reactive({\n		${pk.attrName}:\"\",\n#foreach($key in $resultMap.keySet())\n#set($itemList = $resultMap.get($key))\n#foreach($field in $itemList)\n    ${field.options.name}: \"\",\n#end\n#end\n});\n\n// 定义校验规则\nconst dataRules = ref({\n#foreach($key in $resultMap.keySet())\n#set($itemList = $resultMap.get($key))\n#foreach($field in $itemList)\n#if($field.options.required && $field.options.validation)\n    ${field.options.name}: [{required: true, message: \'${field.options.label}不能为空\', trigger: \'blur\'}, {{ validator: rule.${field.options.validation}, trigger: \'blur\' }],\n#elseif($field.options.required)\n    ${field.options.name}: [{required: true, message: \'${field.options.label}不能为空\', trigger: \'blur\'}],\n#elseif($field.options.validation)\n   ${field.options.name}: [{ validator: rule.${field.options.validation}, trigger: \'blur\' }],\n#end\n#end\n#end\n})\n\n// 打开弹窗\nconst openDialog = (id: string) => {\n  visible.value = true\n  form.${pk.attrName} = \'\'\n\n  // 重置表单数据\n    nextTick(() => {\n        dataFormRef.value?.resetFields();\n    });\n  \n  // 获取${className}信息\n  if (id) {\n    form.${pk.attrName} = id\n    get${className}Data(id)\n  }\n};\n\n// 提交\nconst onSubmit = () => {\n  dataFormRef.value.validate((valid: boolean) => {\n    if (!valid) {\n      return false\n    }\n\n    // 更新\n    if (form.${pk.attrName}) {\n      putObj(form).then(() => {\n        useMessage().success(\'修改成功\')\n        visible.value = false // 关闭弹窗\n        emit(\'refresh\')\n      }).catch((err: any) => {\n        useMessage().error(err.msg)\n      })\n    } else {\n      addObj(form).then(() => {\n        useMessage().success(\'添加成功\')\n        visible.value = false // 关闭弹窗\n        emit(\'refresh\')\n      }).catch((err: any) => {\n        useMessage().error(err.msg)\n      })\n    }\n  })\n}\n\n// 初始化表单数据\nconst get${className}Data = (id: string) => {\n  // 获取数据\n  getObj(id).then((res: any) => {\n    Object.assign(form, res.data)\n  })\n};\n\n// 暴露变量\ndefineExpose({\n  openDialog\n});\n</script>', '2023-02-23 04:33:52', '2023-08-28 22:08:59', '0', 1, '', 'admin');
INSERT INTO `gen_template` VALUES (3, 'Controller', '${backendPath}/src/main/java/${packagePath}/${moduleName}/controller/${ClassName}Controller.java', '后台Controller', 'package ${package}.${moduleName}.controller;\n\n#if($queryList)\nimport cn.hutool.core.util.StrUtil;\n#end\nimport cn.hutool.core.util.ArrayUtil;\nimport cn.hutool.core.collection.CollUtil;\nimport com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;\nimport com.baomidou.mybatisplus.core.toolkit.Wrappers;\nimport com.baomidou.mybatisplus.extension.plugins.pagination.Page;\nimport com.operation.electric.common.core.util.R;\nimport com.operation.electric.common.log.annotation.SysLog;\nimport ${package}.${moduleName}.entity.${ClassName}Entity;\nimport ${package}.${moduleName}.service.${ClassName}Service;\nimport org.springframework.security.access.prepost.PreAuthorize;\nimport com.operation.electric.common.excel.annotation.ResponseExcel;\nimport io.swagger.v3.oas.annotations.security.SecurityRequirement;\n#if($isSpringBoot3)\nimport org.springdoc.core.annotations.ParameterObject;\n#else\nimport org.springdoc.api.annotations.ParameterObject;\n#end\nimport org.springframework.http.HttpHeaders;\nimport io.swagger.v3.oas.annotations.tags.Tag;\nimport io.swagger.v3.oas.annotations.Operation;\nimport lombok.RequiredArgsConstructor;\nimport org.springframework.web.bind.annotation.*;\n\nimport java.util.List;\nimport java.util.Objects;\n\n/**\n * ${tableComment}\n *\n * <AUTHOR> * @date ${datetime}\n */\n@RestController\n@RequiredArgsConstructor\n@RequestMapping(\"/${functionName}\" )\n@Tag(description = \"${functionName}\" , name = \"${tableComment}管理\" )\n@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)\npublic class ${ClassName}Controller {\n\n    private final  ${ClassName}Service ${className}Service;\n\n    /**\n     * 分页查询\n     * @param page 分页对象\n     * @param ${className} ${tableComment}\n     * @return\n     */\n    @Operation(summary = \"分页查询\" , description = \"分页查询\" )\n    @GetMapping(\"/page\" )\n    @PreAuthorize(\"@pms.hasPermission(\'${moduleName}_${functionName}_view\')\" )\n    public R get${ClassName}Page(@ParameterObject Page page, @ParameterObject ${ClassName}Entity ${className}) {\n        LambdaQueryWrapper<${ClassName}Entity> wrapper = Wrappers.lambdaQuery();\n#foreach ($field in $queryList)\n#set($getAttrName=$str.getProperty($field.attrName))\n#set($var=\"${className}.$getAttrName()\")\n#if($field.attrType == \'String\')\n#set($expression=\"StrUtil.isNotBlank\")\n#else\n#set($expression=\"Objects.nonNull\")\n#end\n#if($field.queryType == \'=\')\n		wrapper.eq($expression($var),${ClassName}Entity::$getAttrName,$var);\n#elseif( $field.queryType == \'like\' )\n		wrapper.like($expression($var),${ClassName}Entity::$getAttrName,$var);\n#elseif( $field.queryType == \'!-\' )\n		wrapper.ne($expression($var),${ClassName}Entity::$getAttrName,$var);\n#elseif( $field.queryType == \'>\' )\n		wrapper.gt($expression($var),${ClassName}Entity::$getAttrName,$var);\n#elseif( $field.queryType == \'<\' )\n		wrapper.lt($expression($var),${ClassName}Entity::$getAttrName,$var);\n#elseif( $field.queryType == \'>=\' )\n		wrapper.ge($expression($var),${ClassName}Entity::$getAttrName,$var);\n#elseif( $field.queryType == \'<=\' )\n		wrapper.le($expression($var),${ClassName}Entity::$getAttrName,$var);\n#elseif( $field.queryType == \'left like\' )\n		wrapper.likeLeft($expression($var),${ClassName}Entity::$getAttrName,$var);\n#elseif( $field.queryType == \'right like\' )\n		wrapper.likeRight($expression($var),${ClassName}Entity::$getAttrName,$var);\n#end\n#end\n        return R.ok(${className}Service.page(page, wrapper));\n    }\n\n\n    /**\n     * 通过id查询${tableComment}\n     * @param ${pk.attrName} id\n     * @return R\n     */\n    @Operation(summary = \"通过id查询\" , description = \"通过id查询\" )\n    @GetMapping(\"/{${pk.attrName}}\" )\n    @PreAuthorize(\"@pms.hasPermission(\'${moduleName}_${functionName}_view\')\" )\n    public R getById(@PathVariable(\"${pk.attrName}\" ) ${pk.attrType} ${pk.attrName}) {\n        return R.ok(${className}Service.getById(${pk.attrName}));\n    }\n\n    /**\n     * 新增${tableComment}\n     * @param ${className} ${tableComment}\n     * @return R\n     */\n    @Operation(summary = \"新增${tableComment}\" , description = \"新增${tableComment}\" )\n    @SysLog(\"新增${tableComment}\" )\n    @PostMapping\n    @PreAuthorize(\"@pms.hasPermission(\'${moduleName}_${functionName}_add\')\" )\n    public R save(@RequestBody ${ClassName}Entity ${className}) {\n        return R.ok(${className}Service.save(${className}));\n    }\n\n    /**\n     * 修改${tableComment}\n     * @param ${className} ${tableComment}\n     * @return R\n     */\n    @Operation(summary = \"修改${tableComment}\" , description = \"修改${tableComment}\" )\n    @SysLog(\"修改${tableComment}\" )\n    @PutMapping\n    @PreAuthorize(\"@pms.hasPermission(\'${moduleName}_${functionName}_edit\')\" )\n    public R updateById(@RequestBody ${ClassName}Entity ${className}) {\n        return R.ok(${className}Service.updateById(${className}));\n    }\n\n    /**\n     * 通过id删除${tableComment}\n     * @param ids ${pk.attrName}列表\n     * @return R\n     */\n    @Operation(summary = \"通过id删除${tableComment}\" , description = \"通过id删除${tableComment}\" )\n    @SysLog(\"通过id删除${tableComment}\" )\n    @DeleteMapping\n    @PreAuthorize(\"@pms.hasPermission(\'${moduleName}_${functionName}_del\')\" )\n    public R removeById(@RequestBody ${pk.attrType}[] ids) {\n        return R.ok(${className}Service.removeBatchByIds(CollUtil.toList(ids)));\n    }\n\n\n    /**\n     * 导出excel 表格\n     * @param ${className} 查询条件\n   	 * @param ids 导出指定ID\n     * @return excel 文件流\n     */\n    @ResponseExcel\n    @GetMapping(\"/export\")\n    @PreAuthorize(\"@pms.hasPermission(\'${moduleName}_${functionName}_export\')\" )\n    public List<${ClassName}Entity> export(${ClassName}Entity ${className},${pk.attrType}[] ids) {\n        return ${className}Service.list(Wrappers.lambdaQuery(${className}).in(ArrayUtil.isNotEmpty(ids), ${ClassName}Entity::$str.getProperty($pk.attrName), ids));\n    }\n}', '2023-02-23 01:16:17', '2023-10-29 12:18:12', '0', 1, '', 'admin');
INSERT INTO `gen_template` VALUES (4, 'Service', '${backendPath}/src/main/java/${packagePath}/${moduleName}/service/${ClassName}Service.java', 'Service', 'package ${package}.${moduleName}.service;\n\n#if($ChildClassName)\nimport com.github.yulichang.extension.mapping.base.MPJDeepService;\nimport ${package}.${moduleName}.entity.${ChildClassName}Entity;\n#else\nimport com.baomidou.mybatisplus.extension.service.IService;\n#end\nimport ${package}.${moduleName}.entity.${ClassName}Entity;\n\n#if($ChildClassName)\npublic interface ${ClassName}Service extends MPJDeepService<${ClassName}Entity> {\n    Boolean saveDeep(${ClassName}Entity ${className});\n\n    Boolean updateDeep(${ClassName}Entity ${className});\n\n    Boolean removeDeep(Long[] ids);\n\n    Boolean removeChild(Long[] ids);\n#else\npublic interface ${ClassName}Service extends IService<${ClassName}Entity> {\n#end\n\n}', '2023-02-23 01:16:53', '2023-06-04 10:35:25', '0', 1, ' ', ' ');
INSERT INTO `gen_template` VALUES (5, 'ServiceImpl', '${backendPath}/src/main/java/${packagePath}/${moduleName}/service/impl/${ClassName}ServiceImpl.java', 'ServiceImpl', 'package ${package}.${moduleName}.service.impl;\n\nimport com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;\nimport ${package}.${moduleName}.entity.${ClassName}Entity;\nimport ${package}.${moduleName}.mapper.${ClassName}Mapper;\nimport ${package}.${moduleName}.service.${ClassName}Service;\nimport org.springframework.stereotype.Service;\n#if($ChildClassName)\nimport cn.hutool.core.collection.CollUtil;\nimport com.baomidou.mybatisplus.core.toolkit.Wrappers;\nimport ${package}.${moduleName}.entity.${ChildClassName}Entity;\nimport ${package}.${moduleName}.mapper.${ChildClassName}Mapper;\nimport org.springframework.transaction.annotation.Transactional;\nimport lombok.RequiredArgsConstructor;\nimport java.util.Objects;\n#end\n/**\n * ${tableComment}\n *\n * <AUTHOR> * @date ${datetime}\n */\n@Service\n#if($ChildClassName)\n@RequiredArgsConstructor\n#end\npublic class ${ClassName}ServiceImpl extends ServiceImpl<${ClassName}Mapper, ${ClassName}Entity> implements ${ClassName}Service {\n#if($ChildClassName)\n  private final ${ChildClassName}Mapper ${childClassName}Mapper;\n\n    @Override\n    @Transactional(rollbackFor = Exception.class)\n    public Boolean saveDeep(${ClassName}Entity ${className}) {\n        baseMapper.insert(${className});\n        for (${ChildClassName}Entity  ${childClassName} : ${className}.get${ChildClassName}List()) {\n            ${childClassName}.$str.setProperty($childField)(${className}.$str.getProperty($mainField)());\n            ${childClassName}Mapper.insert( ${childClassName});\n        }\n\n        return Boolean.TRUE;\n    }\n\n    @Override\n    @Transactional(rollbackFor = Exception.class)\n    public Boolean updateDeep(${ClassName}Entity ${className}) {\n        baseMapper.updateById(${className});\n        for (${ChildClassName}Entity  ${childClassName} : ${className}.get${ChildClassName}List()) {\n#set($getChildPkName=$str.getProperty(${pk.attrName}))\n            if (Objects.isNull(${childClassName}.$getChildPkName())) {\n                ${childClassName}.$str.setProperty($childField)(${className}.$str.getProperty($mainField)());\n                ${childClassName}Mapper.insert(${childClassName});\n            } else {\n                ${childClassName}Mapper.updateById(${childClassName});\n            }\n        }\n        return Boolean.TRUE;\n    }\n\n    @Override\n    @Transactional(rollbackFor = Exception.class)\n    public Boolean removeDeep(Long[] ids) {\n        baseMapper.deleteBatchIds(CollUtil.toList(ids));\n        ${childClassName}Mapper.delete(Wrappers.<${ChildClassName}Entity>lambdaQuery().in(${ChildClassName}Entity::$str.getProperty($childField), ids));\n        return Boolean.TRUE;\n    }\n\n    @Override\n    @Transactional(rollbackFor = Exception.class)\n    public Boolean removeChild(Long[] ids) {\n        ${childClassName}Mapper.deleteBatchIds(CollUtil.toList(ids));\n        return Boolean.TRUE;\n    }\n#end\n}', '2023-02-23 01:17:36', '2023-08-27 23:29:58', '0', 1, ' ', ' ');
INSERT INTO `gen_template` VALUES (6, '实体', '${backendPath}/src/main/java/${packagePath}/${moduleName}/entity/${ClassName}Entity.java', 'Entity', 'package ${package}.${moduleName}.entity;\n\nimport com.baomidou.mybatisplus.annotation.*;\nimport com.baomidou.mybatisplus.extension.activerecord.Model;\nimport io.swagger.v3.oas.annotations.media.Schema;\nimport lombok.Data;\nimport lombok.EqualsAndHashCode;\n#foreach($import in $importList)\nimport $import;\n#end\n#if($ChildClassName)\nimport com.alibaba.excel.annotation.ExcelIgnore;\nimport com.github.yulichang.annotation.EntityMapping;\nimport java.util.List;\n#end\n\n/**\n * ${tableComment}\n *\n * <AUTHOR> * @date ${datetime}\n */\n@Data\n@TableName(\"${tableName}\")\n@EqualsAndHashCode(callSuper = true)\n@Schema(description = \"${tableComment}\")\npublic class ${ClassName}Entity extends Model<${ClassName}Entity> {\n\n#foreach ($field in $fieldList)\n#if(${field.fieldComment})#set($comment=${field.fieldComment})#else #set($comment=${field.attrName})#end\n\n	/**\n	* $comment\n	*/\n#if($field.primaryPk == \'1\')\n    @TableId(type = IdType.ASSIGN_ID)\n#end\n#if($field.autoFill == \'INSERT\')\n	@TableField(fill = FieldFill.INSERT)\n#elseif($field.autoFill == \'INSERT_UPDATE\')\n	@TableField(fill = FieldFill.INSERT_UPDATE)\n#elseif($field.autoFill == \'UPDATE\')\n	@TableField(fill = FieldFill.UPDATE)\n#end\n#if($field.fieldName == \'del_flag\')\n    @TableLogic\n		@TableField(fill = FieldFill.INSERT)\n#end\n    @Schema(description=\"$comment\"#if($field.hidden),hidden=$field.hidden#end)\n#if($field.formType == \'checkbox\')\n    private ${field.attrType}[] $field.attrName;\n#else\n    private $field.attrType $field.attrName;\n#end    \n#end\n#if($ChildClassName)\n    @ExcelIgnore\n    @TableField(exist = false)\n    @EntityMapping(thisField = \"$mainField\", joinField = \"$childField\")\n    private List<${ChildClassName}Entity> ${childClassName}List;\n#end\n}', '2023-02-23 01:17:53', '2023-09-12 16:20:57', '0', 1, ' ', ' ');
INSERT INTO `gen_template` VALUES (7, 'Mapper', '${backendPath}/src/main/java/${packagePath}/${moduleName}/mapper/${ClassName}Mapper.java', 'Mapper', 'package ${package}.${moduleName}.mapper;\n\nimport com.operation.electric.common.data.datascope.ElectricBaseMapper;\nimport ${package}.${moduleName}.entity.${ClassName}Entity;\nimport org.apache.ibatis.annotations.Mapper;\n\n@Mapper\npublic interface ${ClassName}Mapper extends ElectricBaseMapper<${ClassName}Entity> {\n\n\n}', '2023-02-23 01:18:18', '2023-08-13 13:52:50', '0', 1, ' ', ' ');
INSERT INTO `gen_template` VALUES (8, 'Mapper.xml', '${backendPath}/src/main/resources/mapper/${ClassName}Mapper.xml', 'Mapper.xml', '<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">\n\n<mapper namespace=\"${package}.${moduleName}.mapper.${ClassName}Mapper\">\n\n  <resultMap id=\"${className}Map\" type=\"${package}.${moduleName}.entity.${ClassName}Entity\">\n#foreach ($field in $fieldList)\n	    #if($field.primaryPk == \'1\')\n        <id property=\"$field.attrName\" column=\"$field.fieldName\"/>\n      #else\n        <result property=\"$field.attrName\" column=\"$field.fieldName\"/>\n      #end\n#end\n  </resultMap>\n</mapper>', '2023-02-23 01:18:35', '2023-09-23 21:36:09', '0', 1, ' ', ' ');
INSERT INTO `gen_template` VALUES (9, '权限菜单', '${backendPath}/menu/${functionName}_menu.sql', 'menu.sql', '-- 该脚本不要直接执行， 注意维护菜单的父节点ID 默认 父节点-1 , 默认租户 1\n#set($menuId=${dateTool.getSystemTime()})\n\n-- 菜单SQL\ninsert into sys_menu ( menu_id,parent_id, path, permission, menu_type, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)\nvalues (${menuId}, \'-1\', \'/${moduleName}/${functionName}/index\', \'\', \'0\', \'icon-bangzhushouji\', \'0\', null , \'8\', null , \'${tableComment}管理\', 1);\n\n-- 菜单对应按钮SQL\ninsert into sys_menu ( menu_id,parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)\nvalues (${math.add($menuId,1)},${menuId}, \'${moduleName}_${functionName}_view\', \'1\', null, \'1\',  \'0\', null, \'0\', null, \'${tableComment}查看\', 1);\n\ninsert into sys_menu ( menu_id,parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)\nvalues (${math.add($menuId,2)},${menuId}, \'${moduleName}_${functionName}_add\', \'1\', null, \'1\',  \'0\', null, \'1\', null, \'${tableComment}新增\', 1);\n\ninsert into sys_menu (menu_id, parent_id, permission, menu_type, path, icon,  del_flag, create_time, sort_order, update_time, name, tenant_id)\nvalues (${math.add($menuId,3)},${menuId}, \'${moduleName}_${functionName}_edit\', \'1\', null, \'1\',  \'0\', null, \'2\', null, \'${tableComment}修改\', 1);\n\ninsert into sys_menu (menu_id, parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)\nvalues (${math.add($menuId,4)},${menuId}, \'${moduleName}_${functionName}_del\', \'1\', null, \'1\',  \'0\', null, \'3\', null, \'${tableComment}删除\', 1);\n\ninsert into sys_menu ( menu_id,parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)\nvalues (${math.add($menuId,5)},${menuId}, \'${moduleName}_${functionName}_export\', \'1\', null, \'1\',  \'0\', null, \'3\', null, \'导入导出\', 1);', '2023-02-23 01:19:08', '2023-08-27 23:16:31', '0', 1, ' ', ' ');
INSERT INTO `gen_template` VALUES (10, 'api.ts', '${frontendPath}/src/api/${moduleName}/${functionName}.ts', 'api.ts', 'import request from \"/@/utils/request\"\n\nexport function fetchList(query?: Object) {\n  return request({\n    url: \'/${moduleName}/${functionName}/page\',\n    method: \'get\',\n    params: query\n  })\n}\n\nexport function addObj(obj?: Object) {\n  return request({\n    url: \'/${moduleName}/${functionName}\',\n    method: \'post\',\n    data: obj\n  })\n}\n\nexport function getObj(id?: string) {\n  return request({\n    url: \'/${moduleName}/${functionName}/\' + id,\n    method: \'get\'\n  })\n}\n\nexport function delObjs(ids?: Object) {\n  return request({\n    url: \'/${moduleName}/${functionName}\',\n    method: \'delete\',\n    data: ids\n  })\n}\n\nexport function putObj(obj?: Object) {\n  return request({\n    url: \'/${moduleName}/${functionName}\',\n    method: \'put\',\n    data: obj\n  })\n}\n\n#if($ChildClassName)\nexport function delChildObj(ids?: Object) {\n  return request({\n    url: \'/${moduleName}/${functionName}/child\',\n    method: \'delete\',\n    data: ids\n  })\n}\n#end', '2023-02-23 01:19:23', '2023-06-04 10:34:17', '0', 1, ' ', ' ');
INSERT INTO `gen_template` VALUES (11, '表格', '${frontendPath}/src/views/${moduleName}/${functionName}/index.vue', '表格不含i18n', '<template>\n  <div class=\"layout-padding\">\n    <div class=\"layout-padding-auto layout-padding-view\">\n#if($queryList)\n      <el-row v-show=\"showSearch\">\n        <el-form :model=\"state.queryForm\" ref=\"queryRef\" :inline=\"true\" @keyup.enter=\"getDataList\">\n#foreach($field in $queryList)\n#if($field.queryFormType == \'select\')\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n            <el-select v-model=\"state.queryForm.${field.attrName}\" placeholder=\"请选择#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\">\n       #if($field.fieldDict)\n              <el-option :label=\"item.label\" :value=\"item.value\" v-for=\"(item, index) in ${field.fieldDict}\" :key=\"index\"></el-option>\n         #else\n              <el-option label=\"请选择\">0</el-option>\n         #end\n            </el-select>\n      </el-form-item>\n#elseif($field.queryFormType == \'date\')\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n      <el-date-picker type=\"date\" placeholder=\"请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" v-model=\"state.queryForm.${field.attrName}\"></el-date-picker>\n      </el-form-item>\n#elseif($field.queryFormType == \'datetime\')\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\" >\n            <el-date-picker type=\"datetime\" placeholder=\"请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" v-model=\"state.queryForm.${field.attrName}\"></el-date-picker>\n      </el-form-item>\n      </el-col>\n#else\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\" >\n        <el-input placeholder=\"请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" v-model=\"state.queryForm.${field.attrName}\" />\n      </el-form-item>\n#end\n#end\n          <el-form-item>\n            <el-button icon=\"search\" type=\"primary\" @click=\"getDataList\">\n              查询\n            </el-button>\n            <el-button icon=\"Refresh\" @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n      </el-row>\n#end\n      <el-row>\n        <div class=\"mb8\" style=\"width: 100%\">\n          <el-button icon=\"folder-add\" type=\"primary\" class=\"ml10\" @click=\"formDialogRef.openDialog()\"\n            v-auth=\"\'${moduleName}_${functionName}_add\'\">\n            新 增\n          </el-button>\n          <el-button plain :disabled=\"multiple\" icon=\"Delete\" type=\"primary\"\n            v-auth=\"\'${moduleName}_${functionName}_del\'\" @click=\"handleDelete(selectObjs)\">\n            删除\n          </el-button>\n          <right-toolbar v-model:showSearch=\"showSearch\" :export=\"\'${moduleName}_${functionName}_export\'\"\n                @exportExcel=\"exportExcel\" class=\"ml10 mr20\" style=\"float: right;\"\n            @queryTable=\"getDataList\"></right-toolbar>\n        </div>\n      </el-row>\n      <el-table :data=\"state.dataList\" v-loading=\"state.loading\" border \n        :cell-style=\"tableStyle.cellStyle\" :header-cell-style=\"tableStyle.headerCellStyle\"\n				@selection-change=\"selectionChangHandle\"\n        @sort-change=\"sortChangeHandle\">\n        <el-table-column type=\"selection\" width=\"40\" align=\"center\" />\n        <el-table-column type=\"index\" label=\"#\" width=\"40\" />\n      #foreach($field in $gridList)\n        #if($field.fieldDict)\n          <el-table-column prop=\"${field.attrName}\" label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" show-overflow-tooltip>\n      <template #default=\"scope\">\n                <dict-tag :options=\"$field.fieldDict\" :value=\"scope.row.${field.attrName}\"></dict-tag>\n            </template>\n          </el-table-column>\n        #else\n          <el-table-column prop=\"${field.attrName}\" label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" #if(${field.gridSort} == \'1\')sortable=\"custom\"#end show-overflow-tooltip/>\n        #end\n     #end\n        <el-table-column label=\"操作\" width=\"150\">\n          <template #default=\"scope\">\n            <el-button icon=\"edit-pen\" text type=\"primary\" v-auth=\"\'${moduleName}_${functionName}_edit\'\"\n              @click=\"formDialogRef.openDialog(scope.row.${pk.attrName})\">编辑</el-button>\n            <el-button icon=\"delete\" text type=\"primary\" v-auth=\"\'${moduleName}_${functionName}_del\'\" @click=\"handleDelete([scope.row.${pk.attrName}])\">删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination @size-change=\"sizeChangeHandle\" @current-change=\"currentChangeHandle\" v-bind=\"state.pagination\" />\n    </div>\n\n    <!-- 编辑、新增  -->\n    <form-dialog ref=\"formDialogRef\" @refresh=\"getDataList(false)\" />\n\n  </div>\n</template>\n\n<script setup lang=\"ts\" name=\"system${ClassName}\">\nimport { BasicTableProps, useTable } from \"/@/hooks/table\";\nimport { fetchList, delObjs } from \"/@/api/${moduleName}/${functionName}\";\nimport { useMessage, useMessageBox } from \"/@/hooks/message\";\nimport { useDict } from \'/@/hooks/dict\';\n\n// 引入组件\nconst FormDialog = defineAsyncComponent(() => import(\'./form.vue\'));\n// 定义查询字典\n#set($fieldDict=[])\n#foreach($field in $queryList)\n  #if($field.fieldDict)\n    #set($void=$fieldDict.add($field.fieldDict))\n  #end\n#end\n\n#foreach($field in $gridList)\n  #if($field.fieldDict)\n    #set($void=$fieldDict.add($field.fieldDict))\n  #end\n#end\n#if($fieldDict)\nconst { $dict.format($fieldDict) } = useDict($dict.quotation($fieldDict))\n#end\n// 定义变量内容\nconst formDialogRef = ref()\n// 搜索变量\nconst queryRef = ref()\nconst showSearch = ref(true)\n// 多选变量\nconst selectObjs = ref([]) as any\nconst multiple = ref(true)\n\nconst state: BasicTableProps = reactive<BasicTableProps>({\n  queryForm: {},\n  pageList: fetchList\n})\n\n//  table hook\nconst {\n  getDataList,\n  currentChangeHandle,\n  sizeChangeHandle,\n  sortChangeHandle,\n  downBlobFile,\n	tableStyle\n} = useTable(state)\n\n// 清空搜索条件\nconst resetQuery = () => {\n  // 清空搜索条件\n  queryRef.value?.resetFields()\n  // 清空多选\n  selectObjs.value = []\n  getDataList()\n}\n\n// 导出excel\nconst exportExcel = () => {\n  downBlobFile(\'/${moduleName}/${functionName}/export\', Object.assign(state.queryForm, { ids: selectObjs }), \'${functionName}.xlsx\')\n}\n\n// 多选事件\nconst selectionChangHandle = (objs: { $pk.attrName: string }[]) => {\n  selectObjs.value = objs.map(({ $pk.attrName }) => $pk.attrName);\n  multiple.value = !objs.length;\n};\n\n// 删除操作\nconst handleDelete = async (ids: string[]) => {\n  try {\n    await useMessageBox().confirm(\'此操作将永久删除\');\n  } catch {\n    return;\n  }\n\n  try {\n    await delObjs(ids);\n    getDataList();\n    useMessage().success(\'删除成功\');\n  } catch (err: any) {\n    useMessage().error(err.msg);\n  }\n};\n</script>', '2023-02-23 01:19:35', '2023-08-29 14:27:53', '0', 1, '', 'admin');
INSERT INTO `gen_template` VALUES (12, '表单', '${frontendPath}/src/views/${moduleName}/${functionName}/form.vue', '表单不含i18n', '<template>\n    <el-dialog :title=\"form.${pk.attrName} ? \'编辑\' : \'新增\'\" v-model=\"visible\"\n      :close-on-click-modal=\"false\" draggable>\n      <el-form ref=\"dataFormRef\" :model=\"form\" :rules=\"dataRules\" formDialogRef label-width=\"90px\" v-loading=\"loading\">\n       <el-row :gutter=\"24\">\n#foreach($field in $formList)\n#if($field.attrName != ${pk.attrName})\n#if($formLayout == 1)\n    <el-col :span=\"24\" class=\"mb20\">\n#elseif($formLayout == 2)\n    <el-col :span=\"12\" class=\"mb20\">\n#end\n#if($field.formType == \'text\')\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n        <el-input v-model=\"form.${field.attrName}\" placeholder=\"请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\"/>\n      </el-form-item>\n      </el-col>\n#elseif($field.formType == \'textarea\')\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n        <el-input type=\"textarea\" v-model=\"form.${field.attrName}\" placeholder=\"请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\"/>\n      </el-form-item>\n      </el-col>\n#elseif($field.formType == \'select\')\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n          <el-select v-model=\"form.${field.attrName}\" placeholder=\"请选择#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\">\n     #if($field.fieldDict)\n            <el-option :value=\"item.value\" :label=\"item.label\" v-for=\"(item, index) in ${field.fieldDict}\" :key=\"index\"></el-option>\n       #end\n     #if(!$field.fieldDict)\n            <el-option label=\"请选择\">0</el-option>\n       #end\n          </el-select>\n        </el-form-item>\n      </el-col>\n#elseif($field.formType == \'radio\')\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n            <el-radio-group v-model=\"form.${field.attrName}\">\n     #if($field.fieldDict)\n             <el-radio :label=\"item.value\" v-for=\"(item, index) in ${field.fieldDict}\" border :key=\"index\">{{ item.label }}\n            </el-radio>\n       #else\n           <el-radio label=\"${field.fieldComment}\" border>${field.fieldComment}</el-radio>\n       #end\n            </el-radio-group>\n        </el-form-item>\n      </el-col>\n#elseif($field.formType == \'checkbox\')\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n            <el-checkbox-group v-model=\"form.${field.attrName}\">\n     #if($field.fieldDict)\n						<el-checkbox :label=\"item.value\" v-for=\"(item, index) in ${field.fieldDict}\" :key=\"index\">{{ item.label }}</el-checkbox>\n       #end\n     #if(!$field.fieldDict)\n                <el-checkbox label=\"启用\" name=\"type\"></el-checkbox>\n                <el-checkbox label=\"禁用\" name=\"type\"></el-checkbox>\n       #end\n            </el-checkbox-group>\n        </el-form-item>\n      </el-col>\n#elseif($field.formType == \'date\')\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n      <el-date-picker type=\"date\" placeholder=\"请选择#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" v-model=\"form.${field.attrName}\" :value-format=\"dateStr\"></el-date-picker>\n      </el-form-item>\n      </el-col>\n#elseif($field.formType == \'datetime\')\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n            <el-date-picker type=\"datetime\" placeholder=\"请选择#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" v-model=\"form.${field.attrName}\" :value-format=\"dateTimeStr\"></el-date-picker>\n      </el-form-item>\n      </el-col>\n\n#elseif($field.formType == \'number\')\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n        <el-input-number :min=\"1\" :max=\"1000\" v-model=\"form.${field.attrName}\" placeholder=\"请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\"></el-input-number>\n      </el-form-item>\n    </el-col>\n#elseif($field.formType == \'upload-file\')\n  <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n    <upload-file v-model:imageUrl=\"form.${field.attrName}\"></upload-file>\n  </el-form-item>\n  </el-col>\n#elseif($field.formType == \'upload-img\')\n  <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n    <upload-img v-model=\"form.${field.attrName}\"></upload-img>\n  </el-form-item>\n  </el-col>\n#elseif($field.formType == \'editor\')\n  <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n    <editor v-if=\"visible\" v-model:get-html=\"form.${field.attrName}\" placeholder=\"请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\"></editor>\n  </el-form-item>\n  </el-col>\n#end\n\n#if(!$field.formType)\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${column.attrName}\">\n        <el-input v-model=\"form.${field.attrName}\" placeholder=\"请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\"/>\n      </el-form-item>\n    </el-col>\n#end\n#end\n#end\n			</el-row>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"visible = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"onSubmit\" :disabled=\"loading\">确认</el-button>\n        </span>\n      </template>\n    </el-dialog>\n</template>\n\n<script setup lang=\"ts\" name=\"${ClassName}Dialog\">\nimport { useDict } from \'/@/hooks/dict\';\nimport { useMessage } from \"/@/hooks/message\";\nimport { getObj, addObj, putObj } from \'/@/api/${moduleName}/${functionName}\'\nimport { rule } from \'/@/utils/validate\';\nconst emit = defineEmits([\'refresh\']);\n\n// 定义变量内容\nconst dataFormRef = ref();\nconst visible = ref(false)\nconst loading = ref(false)\n// 定义字典\n#set($fieldDict=[])\n#foreach($field in $gridList)\n	#if($field.fieldDict)\n		#set($void=$fieldDict.add($field.fieldDict))\n	#end\n#end\n#if($fieldDict)\nconst { $dict.format($fieldDict) } = useDict($dict.quotation($fieldDict))\n#end\n\n// 提交表单数据\nconst form = reactive({\n#if(!$formList.contains(${pk.attrName}))\n		${pk.attrName}:\'\',\n#end\n#foreach($field in $formList)\n#if($field.formType == \'number\')\n		${field.attrName}: 0,\n#elseif($field.formType == \'checkbox\')\n    ${field.attrName}: [],\n#else\n	  ${field.attrName}: \'\',\n#end\n#end\n});\n\n// 定义校验规则\nconst dataRules = ref({\n#foreach($field in $formList)\n#if($field.formRequired == \'1\' && $field.formValidator)\n    ${field.attrName}: [{required: true, message: \'${field.fieldComment}不能为空\', trigger: \'blur\'}, { validator: rule.${field.formValidator}, trigger: \'blur\' }],\n#elseif($field.formRequired == \'1\')\n        ${field.attrName}: [{required: true, message: \'${field.fieldComment}不能为空\', trigger: \'blur\'}],\n#elseif($field.formValidator)\n        ${field.attrName}: [{ validator: rule.${field.formValidator}, trigger: \'blur\' }],\n#end\n#end\n})\n\n// 打开弹窗\nconst openDialog = (id: string) => {\n  visible.value = true\n  form.${pk.attrName} = \'\'\n\n  // 重置表单数据\n	nextTick(() => {\n		dataFormRef.value?.resetFields();\n	});\n\n  // 获取${className}信息\n  if (id) {\n    form.${pk.attrName} = id\n    get${className}Data(id)\n  }\n};\n\n// 提交\nconst onSubmit = async () => {\n	const valid = await dataFormRef.value.validate().catch(() => {});\n	if (!valid) return false;\n\n	try {\n    loading.value = true;\n		form.${pk.attrName} ? await putObj(form) : await addObj(form);\n		useMessage().success(form.${pk.attrName} ? \'修改成功\' : \'添加成功\');\n		visible.value = false;\n		emit(\'refresh\');\n	} catch (err: any) {\n		useMessage().error(err.msg);\n	} finally {\n    loading.value = false;\n  }\n};\n\n\n// 初始化表单数据\nconst get${className}Data = (id: string) => {\n  // 获取数据\n  loading.value = true\n  getObj(id).then((res: any) => {\n    Object.assign(form, res.data)\n  }).finally(() => {\n    loading.value = false\n  })\n};\n\n// 暴露变量\ndefineExpose({\n  openDialog\n});\n</script>', '2023-02-23 01:19:48', '2023-12-07 13:20:22', '0', 1, '', 'admin');
INSERT INTO `gen_template` VALUES (13, 'i18n英文模板', '${frontendPath}/src/views/${moduleName}/${functionName}/i18n/en.ts', 'i18n英文模板', 'export default {\n   ${functionName}: {\n        index: \'#\',\n        import${className}Tip: \'import ${ClassName}\',\n#foreach($field in $fieldList)\n        ${field.attrName}: \'${field.attrName}\',\n#end\n#foreach($field in $fieldList)\n        input$str.pascalCase(${field.attrName})Tip: \'input ${field.attrName}\',\n#end\n    }\n}', '2023-02-23 01:20:25', '2023-06-04 10:49:25', '0', 1, '', 'admin');
INSERT INTO `gen_template` VALUES (14, 'i18n中文模板', '${frontendPath}/src/views/${moduleName}/${functionName}/i18n/zh-cn.ts', 'i18n中文模板', 'export default {\n   ${functionName}: {\n        index: \'#\',\n        import${className}Tip: \'导入${tableComment}\',\n#foreach($field in $fieldList)\n        ${field.attrName}: \'#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\',\n#end\n#foreach($field in $fieldList)\n        input$str.pascalCase(${field.attrName})Tip: \'请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\',\n#end\n    }\n}', '2023-02-23 01:20:40', '2023-06-04 10:49:28', '0', 1, '', 'admin');
INSERT INTO `gen_template` VALUES (15, '子实体', '${backendPath}/src/main/java/${packagePath}/${moduleName}/entity/${ChildClassName}Entity.java', '子表实体对象', 'package ${package}.${moduleName}.entity;\n\nimport com.baomidou.mybatisplus.annotation.*;\nimport com.baomidou.mybatisplus.extension.activerecord.Model;\nimport io.swagger.v3.oas.annotations.media.Schema;\nimport lombok.Data;\nimport lombok.EqualsAndHashCode;\n#foreach($import in $importList)\nimport $import;\n#end\n\n/**\n * ${tableComment}\n *\n * <AUTHOR> * @date ${datetime}\n */\n@Data\n@TableName(\"${childTableName}\")\n@EqualsAndHashCode(callSuper = true)\n@Schema(description = \"${childTableName}\")\npublic class ${ChildClassName}Entity extends Model<${ChildClassName}Entity> {\n\n#foreach ($field in $childFieldList)\n#if(${field.fieldComment})#set($comment=${field.fieldComment})#else #set($comment=${field.attrName})#end\n	/**\n	* $comment\n	*/\n#if($field.primaryPk == \'1\')\n	@TableId(type = IdType.ASSIGN_ID)\n#end\n#if($field.autoFill == \'INSERT\')\n	@TableField(fill = FieldFill.INSERT)\n#elseif($field.autoFill == \'INSERT_UPDATE\')\n	@TableField(fill = FieldFill.INSERT_UPDATE)\n#elseif($field.autoFill == \'UPDATE\')\n	@TableField(fill = FieldFill.UPDATE)\n#end\n#if($field.fieldName == \'del_flag\')\n  @TableLogic\n	@TableField(fill = FieldFill.INSERT)\n#end\n	@Schema(description=\"$comment\"#if($field.hidden),hidden=$field.hidden#end)\n#if($field.formType == \'checkbox\')\n   private ${field.attrType}[] $field.attrName;\n#else\n   private $field.attrType $field.attrName;\n#end \n#end\n}\n', '2023-06-01 11:07:14', '2023-09-12 16:32:33', '0', 1, ' ', ' ');
INSERT INTO `gen_template` VALUES (16, '主子Contoller', '${backendPath}/src/main/java/${packagePath}/${moduleName}/controller/${ClassName}Controller.java', '子表Controller对象', 'package ${package}.${moduleName}.controller;\n\n#if($queryList)\nimport cn.hutool.core.util.StrUtil;\n#end\nimport cn.hutool.core.util.ArrayUtil;\nimport com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;\nimport com.baomidou.mybatisplus.core.toolkit.Wrappers;\nimport com.baomidou.mybatisplus.extension.plugins.pagination.Page;\nimport com.operation.electric.common.core.util.R;\nimport com.operation.electric.common.log.annotation.SysLog;\nimport ${package}.${moduleName}.entity.${ClassName}Entity;\nimport ${package}.${moduleName}.entity.${ChildClassName}Entity;\nimport ${package}.${moduleName}.service.${ClassName}Service;\nimport org.springframework.security.access.prepost.PreAuthorize;\nimport com.operation.electric.common.excel.annotation.ResponseExcel;\nimport io.swagger.v3.oas.annotations.security.SecurityRequirement;\n#if($isSpringBoot3)\nimport org.springdoc.core.annotations.ParameterObject;\n#else\nimport org.springdoc.api.annotations.ParameterObject;\n#end\nimport org.springframework.http.HttpHeaders;\nimport io.swagger.v3.oas.annotations.tags.Tag;\nimport io.swagger.v3.oas.annotations.Operation;\nimport lombok.RequiredArgsConstructor;\nimport org.springframework.web.bind.annotation.*;\n\nimport java.util.List;\nimport java.util.Objects;\n\n/**\n * ${tableComment}\n *\n * <AUTHOR> * @date ${datetime}\n */\n@RestController\n@RequiredArgsConstructor\n@RequestMapping(\"/${functionName}\" )\n@Tag(description = \"${functionName}\" , name = \"${tableComment}管理\" )\n@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)\npublic class ${ClassName}Controller {\n\n    private final  ${ClassName}Service ${className}Service;\n\n    /**\n     * 分页查询\n     * @param page 分页对象\n     * @param ${className} ${tableComment}\n     * @return\n     */\n    @Operation(summary = \"分页查询\" , description = \"分页查询\" )\n    @GetMapping(\"/page\" )\n    @PreAuthorize(\"@pms.hasPermission(\'${moduleName}_${functionName}_view\')\" )\n    public R get${ClassName}Page(@ParameterObject Page page, @ParameterObject ${ClassName}Entity ${className}) {\n        LambdaQueryWrapper<${ClassName}Entity> wrapper = Wrappers.lambdaQuery();\n#foreach ($field in $queryList)\n#set($getAttrName=$str.getProperty($field.attrName))\n#set($var=\"${className}.$getAttrName()\")\n#if($field.attrType == \'String\')\n#set($expression=\"StrUtil.isNotBlank\")\n#else\n#set($expression=\"Objects.nonNull\")\n#end\n#if($field.queryType == \'=\')\n		wrapper.eq($expression($var),${ClassName}Entity::$getAttrName,$var);\n#elseif( $field.queryType == \'like\' )\n		wrapper.like($expression($var),${ClassName}Entity::$getAttrName,$var);\n#elseif( $field.queryType == \'!-\' )\n		wrapper.ne($expression($var),${ClassName}Entity::$getAttrName,$var);\n#elseif( $field.queryType == \'>\' )\n		wrapper.gt($expression($var),${ClassName}Entity::$getAttrName,$var);\n#elseif( $field.queryType == \'<\' )\n		wrapper.lt($expression($var),${ClassName}Entity::$getAttrName,$var);\n#elseif( $field.queryType == \'>=\' )\n		wrapper.ge($expression($var),${ClassName}Entity::$getAttrName,$var);\n#elseif( $field.queryType == \'<=\' )\n		wrapper.le($expression($var),${ClassName}Entity::$getAttrName,$var);\n#elseif( $field.queryType == \'left like\' )\n		wrapper.likeLeft($expression($var),${ClassName}Entity::$getAttrName,$var);\n#elseif( $field.queryType == \'right like\' )\n		wrapper.likeRight($expression($var),${ClassName}Entity::$getAttrName,$var);\n#end\n#end\n        return R.ok(${className}Service.page(page, wrapper));\n    }\n\n    /**\n     * 通过id查询${tableComment}\n     * @param ${pk.attrName} id\n     * @return R\n     */\n    @Operation(summary = \"通过id查询\" , description = \"通过id查询\" )\n    @GetMapping(\"/{${pk.attrName}}\" )\n    @PreAuthorize(\"@pms.hasPermission(\'${moduleName}_${functionName}_view\')\" )\n    public R getById(@PathVariable(\"${pk.attrName}\" ) ${pk.attrType} ${pk.attrName}) {\n        return R.ok(${className}Service.getByIdDeep(${pk.attrName}));\n    }\n\n    /**\n     * 新增${tableComment}\n     * @param ${className} ${tableComment}\n     * @return R\n     */\n    @Operation(summary = \"新增${tableComment}\" , description = \"新增${tableComment}\" )\n    @SysLog(\"新增${tableComment}\" )\n    @PostMapping\n    @PreAuthorize(\"@pms.hasPermission(\'${moduleName}_${functionName}_add\')\" )\n    public R save(@RequestBody ${ClassName}Entity ${className}) {\n        return R.ok(${className}Service.saveDeep(${className}));\n    }\n\n    /**\n     * 修改${tableComment}\n     * @param ${className} ${tableComment}\n     * @return R\n     */\n    @Operation(summary = \"修改${tableComment}\" , description = \"修改${tableComment}\" )\n    @SysLog(\"修改${tableComment}\" )\n    @PutMapping\n    @PreAuthorize(\"@pms.hasPermission(\'${moduleName}_${functionName}_edit\')\" )\n    public R updateById(@RequestBody ${ClassName}Entity ${className}) {\n        return R.ok(${className}Service.updateDeep(${className}));\n    }\n\n    /**\n     * 通过id删除${tableComment}\n     * @param ids ${pk.attrName}列表\n     * @return R\n     */\n    @Operation(summary = \"通过id删除${tableComment}\" , description = \"通过id删除${tableComment}\" )\n    @SysLog(\"通过id删除${tableComment}\" )\n    @DeleteMapping\n    @PreAuthorize(\"@pms.hasPermission(\'${moduleName}_${functionName}_del\')\" )\n    public R removeById(@RequestBody ${pk.attrType}[] ids) {\n        return R.ok(${className}Service.removeDeep(ids));\n    }\n\n    /**\n     * 通过id删除${tableComment}子表数据\n     * @param ids ${pk.attrName}列表\n     * @return R\n     */\n    @Operation(summary = \"通过id删除${tableComment}子表数据\" , description = \"通过id删除${tableComment}子表数据\" )\n    @SysLog(\"通过id删除${tableComment}子表数据\" )\n    @DeleteMapping(\"/child\")\n    @PreAuthorize(\"@pms.hasPermission(\'${moduleName}_${functionName}_del\')\" )\n    public R removeChild(@RequestBody ${pk.attrType}[] ids) {\n        return R.ok(${className}Service.removeChild(ids));\n    }\n\n    /**\n     * 导出excel 表格\n     * @param ${className} 查询条件\n   	 * @param ids 导出指定ID\n     * @return excel 文件流\n     */\n    @ResponseExcel\n    @GetMapping(\"/export\")\n    @PreAuthorize(\"@pms.hasPermission(\'${moduleName}_${functionName}_export\')\" )\n    public List<${ClassName}Entity> export(${ClassName}Entity ${className},${pk.attrType}[] ids) {\n        return ${className}Service.list(Wrappers.lambdaQuery(${className}).in(ArrayUtil.isNotEmpty(ids), ${ClassName}Entity::$str.getProperty($pk.attrName), ids));\n    }\n}', '2023-06-01 11:25:28', '2023-10-29 12:17:52', '0', 1, '', 'admin');
INSERT INTO `gen_template` VALUES (17, '主子表单', '${frontendPath}/src/views/${moduleName}/${functionName}/form.vue', '子表表单', '<template>\n  <el-drawer :title=\"form.${pk.attrName} ? (detail ? \'详情\' : \'编辑\') : \'添加\'\" v-model=\"visible\" size=\"50%\">\n      <el-form ref=\"dataFormRef\" :model=\"form\" :rules=\"dataRules\" label-width=\"90px\" :disabled=\"detail\" v-loading=\"loading\">\n        <el-row :gutter=\"24\">\n#foreach($field in $formList)\n#if($field.attrName != ${pk.attrName})\n#if($formLayout == 1)\n    <el-col :span=\"24\" class=\"mb20\">\n#elseif($formLayout == 2)\n    <el-col :span=\"12\" class=\"mb20\">\n#end\n#if($field.formType == \'text\')\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n        <el-input v-model=\"form.${field.attrName}\" placeholder=\"请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\"/>\n      </el-form-item>\n      </el-col>\n#elseif($field.formType == \'textarea\')\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n        <el-input type=\"textarea\" v-model=\"form.${field.attrName}\" placeholder=\"请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\"/>\n      </el-form-item>\n      </el-col>\n#elseif($field.formType == \'select\')\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n          <el-select v-model=\"form.${field.attrName}\" placeholder=\"请选择#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\">\n     #if($field.fieldDict)\n            <el-option :value=\"item.value\" :label=\"item.label\" v-for=\"(item, index) in ${field.fieldDict}\" :key=\"index\"></el-option>\n       #end\n     #if(!$field.fieldDict)\n            <el-option label=\"请选择\">0</el-option>\n       #end\n          </el-select>\n        </el-form-item>\n      </el-col>\n#elseif($field.formType == \'radio\')\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n            <el-radio-group v-model=\"form.${field.attrName}\">\n     #if($field.fieldDict)\n             <el-radio :label=\"item.value\" v-for=\"(item, index) in ${field.fieldDict}\" border :key=\"index\">{{ item.label }}\n            </el-radio>\n       #else\n           <el-radio label=\"${field.fieldComment}\" border>${field.fieldComment}</el-radio>\n       #end\n            </el-radio-group>\n        </el-form-item>\n      </el-col>\n#elseif($field.formType == \'checkbox\')\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n            <el-checkbox-group v-model=\"form.${field.attrName}\">\n     #if($field.fieldDict)\n                <el-checkbox :label=\"item.value\" v-for=\"(item, index) in ${field.fieldDict}\" :key=\"index\">{{ item.label }}</el-checkbox>\n       #end\n     #if(!$field.fieldDict)\n                <el-checkbox label=\"启用\" name=\"type\"></el-checkbox>\n                <el-checkbox label=\"禁用\" name=\"type\"></el-checkbox>\n       #end\n            </el-checkbox-group>\n        </el-form-item>\n      </el-col>\n#elseif($field.formType == \'date\')\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n      <el-date-picker type=\"date\" placeholder=\"请选择#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" v-model=\"form.${field.attrName}\" :value-format=\"dateStr\"></el-date-picker>\n      </el-form-item>\n      </el-col>\n#elseif($field.formType == \'datetime\')\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n            <el-date-picker type=\"datetime\" placeholder=\"请选择#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" v-model=\"form.${field.attrName}\" :value-format=\"dateTimeStr\"></el-date-picker>\n      </el-form-item>\n      </el-col>\n#elseif($field.formType == \'number\')\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n        <el-input-number :min=\"1\" :max=\"1000\" v-model=\"form.${field.attrName}\" placeholder=\"请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\"></el-input-number>\n      </el-form-item>\n    </el-col>\n#elseif($field.formType == \'upload-file\')\n  <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n    <upload-file  v-model=\"form.${field.attrName}\"></upload-file>\n  </el-form-item>\n  </el-col>\n#elseif($field.formType == \'upload-img\')\n  <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n    <upload-img v-model:imageUrl=\"form.${field.attrName}\"></upload-img>\n  </el-form-item>\n  </el-col>\n#elseif($field.formType == \'editor\')\n  <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n    <editor v-model:get-html=\"form.${field.attrName}\" placeholder=\"请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\"></editor>\n  </el-form-item>\n  </el-col>\n#end\n#if(!$field.formType)\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${column.attrName}\">\n        <el-input v-model=\"form.${field.attrName}\" placeholder=\"请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\"/>\n      </el-form-item>\n    </el-col>\n#end\n#end\n#end\n    </el-row>\n  <el-row :gutter=\"24\">\n    <sc-form-table\n      v-model=\"form.${childClassName}List\"\n      :addTemplate=\"childTemp\"\n      @delete=\"deleteChild\"\n      placeholder=\"暂无数据\"\n    >\n#set($ignoreColumnList = [\"create_by\",\"create_time\",\"update_by\",\"update_time\",\"del_flag\",\"tenant_id\"])\n#foreach($field in $childFieldList)\n#if($field.primaryPk == \'1\')\n#elseif($ignoreColumnList.contains(${field.fieldName}))\n#elseif($field.attrName == $childField)\n#else  \n      <el-table-column label=\"${field.fieldComment}\" prop=\"${field.attrName}\">\n        <template #default=\"{ row, $index }\">\n          <el-form-item :prop=\"`${childClassName}List.${$index}.${field.attrName}`\" :rules=\"[{ required: true, trigger: \'blur\' }]\">\n            <el-input v-model=\"row.${field.attrName}\" style=\"width: 100%\" />\n          </el-form-item>\n        </template>\n      </el-table-column>\n#end\n#end\n    </sc-form-table>\n  </el-row>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"visible = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"onSubmit\" :disabled=\"loading\">确认</el-button>\n        </span>\n      </template>\n    </el-drawer>\n</template>\n\n<script setup lang=\"ts\" name=\"${ClassName}Dialog\">\nimport { useDict } from \'/@/hooks/dict\';\nimport { rule } from \'/@/utils/validate\';\nimport { useMessage } from \"/@/hooks/message\";\nimport { getObj, addObj, putObj, delChildObj } from \'/@/api/${moduleName}/${functionName}\'\nconst scFormTable = defineAsyncComponent(() => import(\'/@/components/FormTable/index.vue\'));\nconst emit = defineEmits([\'refresh\']);\n\n// 定义变量内容\nconst dataFormRef = ref();\nconst visible = ref(false);\nconst loading = ref(false);\nconst detail = ref(false);\n\n// 定义字典\n#set($fieldDict=[])\n#foreach($field in $gridList)\n	#if($field.fieldDict)\n		#set($void=$fieldDict.add($field.fieldDict))\n	#end\n#end\n#if($fieldDict)\nconst { $dict.format($fieldDict) } = useDict($dict.quotation($fieldDict))\n#end\n\n// 提交表单数据\nconst form = reactive({\n#if(!$formList.contains(${pk.attrName}))\n		${pk.attrName}:\'\',\n#end\n#foreach($field in $formList)\n#if($field.formType == \'number\')\n		${field.attrName}: 0,\n#elseif($field.formType == \'checkbox\')\n    ${field.attrName}: [],\n#else\n	  ${field.attrName}: \'\',\n#end\n#end\n	  ${childClassName}List:[],\n});\n\nconst childTemp = reactive({\n  #foreach($field in $childFieldList)\n    ${field.attrName}: \'\',\n  #end\n})\n\n// 定义校验规则\nconst dataRules = ref({\n#foreach($field in $formList)\n#if($field.formRequired == \'1\' && $field.formValidator)\n    ${field.attrName}: [{required: true, message: \'${field.fieldComment}不能为空\', trigger: \'blur\'}, { validator: rule.${field.formValidator}, trigger: \'blur\' }],\n#elseif($field.formRequired == \'1\')\n        ${field.attrName}: [{required: true, message: \'${field.fieldComment}不能为空\', trigger: \'blur\'}],\n#elseif($field.formValidator)\n        ${field.attrName}: [{ validator: rule.${field.formValidator}, trigger: \'blur\' }],\n#end\n#end\n})\n\n// 打开弹窗\nconst openDialog = (id: string, isDetail: boolean) => {\n  visible.value = true\n  detail.value = isDetail\n  form.${pk.attrName} = \'\'\n\n  // 重置表单数据\n  nextTick(() => {\n    dataFormRef.value?.resetFields();\n    form.${childClassName}List = [];\n  });\n\n  // 获取${className}信息\n  if (id) {\n    form.${pk.attrName} = id\n    get${ClassName}Data(id)\n  }\n};\n\n// 提交\nconst onSubmit = async () => {\n  const valid = await dataFormRef.value.validate().catch(() => {});\n  if (!valid) return false;\n\n  try {\n    loading.value = true;\n    form.${pk.attrName} ? await putObj(form) : await addObj(form);\n    useMessage().success(form.${pk.attrName} ? \'修改成功\' : \'添加成功\');\n    visible.value = false;\n    emit(\'refresh\');\n  } catch (err: any) {\n    useMessage().error(err.msg);\n  } finally {\n    loading.value = false;\n  }\n};\n#foreach ($field in $childFieldList)\n#if($field.primaryPk == \'1\')\n#set($childPkName=$field.attrName)\n#end\n#end\n// 删除子表数据\nconst deleteChild = async (obj: { $childPkName: string }) => {\n  if (obj.$childPkName) {\n    try {\n      await delChildObj([obj.$childPkName]);\n      useMessage().success(\'删除成功\');\n    } catch (err: any) {\n      useMessage().error(err.msg);\n    }\n  }\n};\n\n// 初始化表单数据\nconst get${ClassName}Data = (id: string) => {\n  // 获取数据\n  getObj(id).then((res: any) => {\n    Object.assign(form, res.data)\n  })\n};\n\n// 暴露变量\ndefineExpose({\n  openDialog\n});\n</script>', '2023-06-01 15:42:46', '2023-12-07 13:22:29', '0', 1, '', 'admin');
INSERT INTO `gen_template` VALUES (18, '主子表格', '${frontendPath}/src/views/${moduleName}/${functionName}/index.vue', '子表单表格', '<template>\n  <div class=\"layout-padding\">\n    <div class=\"layout-padding-auto layout-padding-view\">\n#if($queryList)\n      <el-row v-show=\"showSearch\">\n        <el-form :model=\"state.queryForm\" ref=\"queryRef\" :inline=\"true\" @keyup.enter=\"getDataList\">\n#foreach($field in $queryList)\n#if($field.queryFormType == \'select\')\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n            <el-select v-model=\"state.queryForm.${field.attrName}\" placeholder=\"请选择#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\">\n       #if($field.fieldDict)\n              <el-option :label=\"item.label\" :value=\"item.value\" v-for=\"(item, index) in ${field.fieldDict}\" :key=\"index\"></el-option>\n         #else\n              <el-option label=\"请选择\">0</el-option>\n         #end\n            </el-select>\n      </el-form-item>\n#elseif($field.queryFormType == \'date\')\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\">\n      <el-date-picker type=\"date\" placeholder=\"请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" v-model=\"state.queryForm.${field.attrName}\"></el-date-picker>\n      </el-form-item>\n#elseif($field.queryFormType == \'datetime\')\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\" >\n            <el-date-picker type=\"datetime\" placeholder=\"请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" v-model=\"state.queryForm.${field.attrName}\"></el-date-picker>\n      </el-form-item>\n      </el-col>\n#else\n      <el-form-item label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" prop=\"${field.attrName}\" >\n        <el-input placeholder=\"请输入#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" v-model=\"state.queryForm.${field.attrName}\"\n          style=\"max-width: 180px\" />\n      </el-form-item>\n#end\n#end\n          <el-form-item>\n            <el-button icon=\"search\" type=\"primary\" @click=\"getDataList\">\n              查询\n            </el-button>\n            <el-button icon=\"Refresh\" @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n      </el-row>\n#end\n      <el-row>\n        <div class=\"mb8\" style=\"width: 100%\">\n          <el-button icon=\"folder-add\" type=\"primary\" class=\"ml10\" @click=\"formDialogRef.openDialog()\"\n            v-auth=\"\'${moduleName}_${functionName}_add\'\">\n            新 增\n          </el-button>\n          <el-button plain :disabled=\"multiple\" icon=\"Delete\" type=\"primary\"\n            v-auth=\"\'${moduleName}_${functionName}_del\'\" @click=\"handleDelete(selectObjs)\">\n            删除\n          </el-button>\n          <right-toolbar v-model:showSearch=\"showSearch\" :export=\"\'${moduleName}_${functionName}_export\'\"\n                @exportExcel=\"exportExcel\" class=\"ml10 mr20\" style=\"float: right;\"\n            @queryTable=\"getDataList\"></right-toolbar>\n        </div>\n      </el-row>\n      <el-table :data=\"state.dataList\" v-loading=\"state.loading\" border \n        :cell-style=\"tableStyle.cellStyle\" :header-cell-style=\"tableStyle.headerCellStyle\"\n        @selection-change=\"selectionChangeHandle\" @sort-change=\"sortChangeHandle\">\n        <el-table-column type=\"selection\" width=\"40\" align=\"center\" />\n        <el-table-column type=\"index\" label=\"#\" width=\"40\" />\n      #foreach($field in $gridList)\n        #if($field.fieldDict)\n          <el-table-column prop=\"${field.attrName}\" label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" show-overflow-tooltip>\n      <template #default=\"scope\">\n                <dict-tag :options=\"$field.fieldDict\" :value=\"scope.row.${field.attrName}\"></dict-tag>\n            </template>\n          </el-table-column>\n        #else\n          <el-table-column prop=\"${field.attrName}\" label=\"#if(${field.fieldComment})${field.fieldComment}#else ${field.attrName}#end\" #if(${field.gridSort} == \'1\')sortable=\"custom\"#end show-overflow-tooltip/>\n        #end\n     #end\n        <el-table-column label=\"操作\" width=\"200\">\n          <template #default=\"scope\">\n          #if($ChildClassName)\n            <el-button text type=\"primary\" icon=\"view\" v-auth=\"\'sys_role_edit\'\" @click=\"formDialogRef.openDialog(scope.row.${pk.attrName}, true)\">\n              详情\n            </el-button>\n          #end\n            <el-button icon=\"edit-pen\" text type=\"primary\" v-auth=\"\'${moduleName}_${functionName}_edit\'\"\n              @click=\"formDialogRef.openDialog(scope.row.${pk.attrName})\">编辑</el-button>\n            <el-button icon=\"delete\" text type=\"primary\" v-auth=\"\'${moduleName}_${functionName}_del\'\" @click=\"handleDelete([scope.row.${pk.attrName}])\">\n              删除\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination @size-change=\"sizeChangeHandle\" @current-change=\"currentChangeHandle\" v-bind=\"state.pagination\" />\n    </div>\n\n    <!-- 编辑、新增  -->\n    <form-dialog ref=\"formDialogRef\" @refresh=\"getDataList(false)\" />\n  </div>\n</template>\n\n<script setup lang=\"ts\" name=\"system${ClassName}\">\nimport { BasicTableProps, useTable } from \"/@/hooks/table\";\nimport { fetchList, delObjs } from \"/@/api/${moduleName}/${functionName}\";\nimport { useMessage, useMessageBox } from \"/@/hooks/message\";\nimport { useDict } from \'/@/hooks/dict\';\n// 引入组件\nconst FormDialog = defineAsyncComponent(() => import(\'./form.vue\'));\n\n// 定义查询字典\n#set($fieldDict=[])\n#foreach($field in $queryList)\n  #if($field.fieldDict)\n    #set($void=$fieldDict.add($field.fieldDict))\n  #end\n#end\n\n#foreach($field in $gridList)\n  #if($field.fieldDict)\n    #set($void=$fieldDict.add($field.fieldDict))\n  #end\n#end\n#if($fieldDict)\nconst { $dict.format($fieldDict) } = useDict($dict.quotation($fieldDict))\n#end\n// 定义变量内容\nconst formDialogRef = ref()\n// 搜索变量\nconst queryRef = ref()\nconst showSearch = ref(true)\n// 多选变量\nconst selectObjs = ref([]) as any\nconst multiple = ref(true)\n\nconst state: BasicTableProps = reactive<BasicTableProps>({\n  queryForm: {},\n  pageList: fetchList\n})\n\n//  table hook\nconst {\n  getDataList,\n  currentChangeHandle,\n  sizeChangeHandle,\n  sortChangeHandle,\n  downBlobFile,\n  tableStyle\n} = useTable(state)\n\n// 清空搜索条件\nconst resetQuery = () => {\n  // 清空搜索条件\n  queryRef.value?.resetFields()\n  // 清空多选\n  selectObjs.value = []\n  getDataList()\n}\n\n// 导出excel\nconst exportExcel = () => {\n  downBlobFile(\'/${moduleName}/${functionName}/export\', Object.assign(state.queryForm, { ids: selectObjs }), \'${functionName}.xlsx\')\n}\n\n// 多选事件\nconst selectionChangeHandle = (objs: { $pk.attrName: string }[]) => {\n  selectObjs.value = objs.map(({ $pk.attrName }) => $pk.attrName);\n  multiple.value = !objs.length;\n};\n\n// 删除操作\nconst handleDelete = async (ids: string[]) => {\n  try {\n    await useMessageBox().confirm(\'此操作将永久删除\');\n  } catch {\n    return;\n  }\n\n  try {\n    await delObjs(ids);\n    getDataList();\n    useMessage().success(\'删除成功\');\n  } catch (err: any) {\n    useMessage().error(err.msg);\n  }\n};\n</script>', '2023-06-01 15:43:31', '2023-08-29 10:53:23', '0', 1, ' ', ' ');
INSERT INTO `gen_template` VALUES (19, '子Mapper', '${backendPath}/src/main/java/${packagePath}/${moduleName}/mapper/${ChildClassName}Mapper.java', '子Mapper', 'package ${package}.${moduleName}.mapper;\n\nimport com.operation.electric.common.data.datascope.ElectricBaseMapper;\n#if($ChildClassName)\nimport ${package}.${moduleName}.entity.${ChildClassName}Entity;\n#else\nimport ${package}.${moduleName}.entity.${ClassName}Entity;\n#end\nimport org.apache.ibatis.annotations.Mapper;\n\n@Mapper\n#if($ChildClassName)\npublic interface ${ChildClassName}Mapper extends ElectricBaseMapper<${ChildClassName}Entity> {\n#else\npublic interface ${ClassName}Mapper extends ElectricBaseMapper<${ClassName}Entity> {\n#end\n\n}', '2023-02-23 01:18:18', '2023-08-07 09:54:36', '0', 1, ' ', ' ');

-- ----------------------------
-- Table structure for gen_template_group
-- ----------------------------
DROP TABLE IF EXISTS `gen_template_group`;
CREATE TABLE `gen_template_group`  (
  `group_id` bigint NOT NULL COMMENT '分组id',
  `template_id` bigint NOT NULL COMMENT '模板id',
  PRIMARY KEY (`group_id`, `template_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '模板分组关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gen_template_group
-- ----------------------------
INSERT INTO `gen_template_group` VALUES (1, 3);
INSERT INTO `gen_template_group` VALUES (1, 4);
INSERT INTO `gen_template_group` VALUES (1, 5);
INSERT INTO `gen_template_group` VALUES (1, 6);
INSERT INTO `gen_template_group` VALUES (1, 7);
INSERT INTO `gen_template_group` VALUES (1, 8);
INSERT INTO `gen_template_group` VALUES (1, 9);
INSERT INTO `gen_template_group` VALUES (1, 10);
INSERT INTO `gen_template_group` VALUES (1, 11);
INSERT INTO `gen_template_group` VALUES (1, 12);
INSERT INTO `gen_template_group` VALUES (2, 4);
INSERT INTO `gen_template_group` VALUES (2, 5);
INSERT INTO `gen_template_group` VALUES (2, 6);
INSERT INTO `gen_template_group` VALUES (2, 7);
INSERT INTO `gen_template_group` VALUES (2, 8);
INSERT INTO `gen_template_group` VALUES (2, 9);
INSERT INTO `gen_template_group` VALUES (2, 10);
INSERT INTO `gen_template_group` VALUES (2, 15);
INSERT INTO `gen_template_group` VALUES (2, 16);
INSERT INTO `gen_template_group` VALUES (2, 17);
INSERT INTO `gen_template_group` VALUES (2, 18);
INSERT INTO `gen_template_group` VALUES (2, 19);

SET FOREIGN_KEY_CHECKS = 1;
