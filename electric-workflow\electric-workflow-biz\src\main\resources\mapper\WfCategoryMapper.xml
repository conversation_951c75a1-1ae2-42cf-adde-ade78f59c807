<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.operation.electric.workflow.mapper.WfCategoryMapper">

    <resultMap type="com.operation.electric.workflow.entity.WfCategory" id="FlowCategoryResult">
        <result property="categoryId" column="category_id"/>
        <result property="categoryName" column="category_name"/>
        <result property="code" column="code"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

</mapper>
