package com.operation.electric.flow.task.controller;

import com.operation.electric.common.core.util.R;
import com.operation.electric.flow.task.dto.ProcessInstanceParamDto;
import com.operation.electric.flow.task.dto.TaskQueryParamDto;
import com.operation.electric.flow.task.service.IProcessInstanceService;
import com.operation.electric.flow.task.vo.NodeFormatParamVo;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.web.bind.annotation.*;

/**
 * 流程实例
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/process-instance")
public class ProcessInstanceController {

	private final IProcessInstanceService processInstanceService;

	/**
	 * 启动流程
	 * @param processInstanceParamDto
	 * @return
	 */
	@SneakyThrows
	@PostMapping("startProcessInstance")
	public R startProcessInstance(@RequestBody ProcessInstanceParamDto processInstanceParamDto) {
		return processInstanceService.startProcessInstance(processInstanceParamDto);
	}

	/**
	 * 查询当前登录用户的待办任务
	 * @param pageDto
	 * @return
	 */
	@SneakyThrows
	@PostMapping("queryMineTask")
	public R queryMineTask(@RequestBody TaskQueryParamDto queryParamDto) {
		return processInstanceService.queryMineTask(queryParamDto);
	}

	/**
	 * 查询当前登录用户已办任务
	 * @param taskQueryParamDto
	 * @return
	 */
	@SneakyThrows
	@PostMapping("queryMineEndTask")
	public R queryMineEndTask(@RequestBody TaskQueryParamDto taskQueryParamDto) {
		return processInstanceService.queryMineEndTask(taskQueryParamDto);
	}

	/**
	 * 查询我发起的
	 * @param pageDto
	 * @return
	 */
	@SneakyThrows
	@PostMapping("queryMineStarted")
	public R queryMineStarted(@RequestBody TaskQueryParamDto taskQueryParamDto) {
		return processInstanceService.queryMineStarted(taskQueryParamDto);
	}

	/**
	 * 查询抄送我的
	 * @param pageDto
	 * @return
	 */
	@SneakyThrows
	@PostMapping("queryMineCC")
	public R queryMineCC(@RequestBody TaskQueryParamDto taskQueryParamDto) {
		return processInstanceService.queryMineCC(taskQueryParamDto);
	}

	/**
	 * 格式化流程显示
	 * @param nodeFormatParamVo
	 * @return
	 */
	@PostMapping("formatStartNodeShow")
	public R formatStartNodeShow(@RequestBody NodeFormatParamVo nodeFormatParamVo) {
		return processInstanceService.formatStartNodeShow(nodeFormatParamVo);
	}

	/**
	 * 流程详情
	 * @param processInstanceId
	 * @return
	 */
	@GetMapping("detail")
	public R detail(String processInstanceId) {
		return processInstanceService.detail(processInstanceId);
	}

	/**
	 * 要修改的流程详情数据
	 * @param processInstanceId
	 * @return
	 */
	@GetMapping("editDetail")
	public R editDetail(String processInstanceId) {
		return processInstanceService.editDetail(processInstanceId);
	}

	/**
	 * 删除流程(逻辑删除)
	 * @param ids
	 * @return
	 */
	@DeleteMapping("deleteProcessInstanceByIds/{ids}")
	public R deleteProcessInstanceByIds(@PathVariable Long[] ids){
		return processInstanceService.deleteProcessInstanceByIds(ids);
	}

	/**
	 * 修改流程表单数据
	 */
	@PutMapping("updateFormData")
	public R updateFormData(@RequestBody ProcessInstanceParamDto processInstanceParamDto){
		return processInstanceService.updateFormData(processInstanceParamDto);
	}

	@GetMapping("/getWorkFolwId/{workFolwName}")
	public R<String> getWorkFolwId(@PathVariable("workFolwName") String workFolwName){
		return processInstanceService.getWorkFolwId(workFolwName);
	}

}
