package com.operation.electric.yykbLog.utils;


import com.fasterxml.jackson.core.type.TypeReference;
import com.operation.electric.yykbLog.entity.BusinessReq;
import com.operation.electric.yykbLog.entity.BusinessRes;
import com.operation.electric.yykbLog.entity.Journal;
import com.operation.electric.yykbLog.entity.OperateUser;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

@Slf4j
public class BusinessLogUtils {

    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");
    private static final OkHttpClient client = new OkHttpClient.Builder().build();

    @SneakyThrows
    public static Journal sendLog(String domain, BusinessReq<Journal> businessReq) {
        checkParams(businessReq);
        String encode = JsonUtils.encode(businessReq.getData());
        Request request = new Request.Builder()
                .url(domain + "/api/v2/log/details")
                .addHeader("APP-KEY", businessReq.getAppKey())
                .addHeader("SYS-ID", businessReq.getSysId())
                .addHeader("Content-Type", "application/json")
                .post(RequestBody.create(JSON, encode))
                .build();

        try (Response execute = client.newCall(request).execute()) {
            assert execute.body() != null;
            String string1 = execute.body().string();
            execute.body().close();
            BusinessRes<Journal> decode = JsonUtils.decode(string1, new TypeReference<BusinessRes<Journal>>() {

            });
            log.info(string1);
            if (execute.isSuccessful()) {
                return decode.getData();
            } else {
                log.error("send log failed, code: {}, message: {}", execute.code(), execute.message());
                String format = String.format("send log failed, code: %s, message: %s", execute.code(), execute.message());
                throw new RuntimeException(format);
            }
        }

    }

    @SneakyThrows
    public static OperateUser registerUser(String domain, BusinessReq<OperateUser> businessReq) {
        checkParams(businessReq);
        RequestBody body = RequestBody.create(
                MediaType.parse("application/json"), JsonUtils.encode(businessReq.getData()));
        Request request = new Request.Builder()
                .url(domain + "/api/v2/log/staffs")
                .addHeader("APP-KEY", businessReq.getAppKey())
                .addHeader("SYS-ID", businessReq.getSysId())
                .addHeader("Content-Type", "application/json")
                .post(body)
                .build();
        try (Response execute = client.newCall(request).execute()) {
            assert execute.body() != null;
            String string1 = execute.body().string();
            execute.body().close();
            BusinessRes<OperateUser> decode = JsonUtils.decode(string1, new TypeReference<BusinessRes<OperateUser>>() {

            });
            if (execute.isSuccessful()) {
                return decode.getData();
            } else {
                log.error("send register user failed, code: {}, message: {}", execute.code(), execute.message());
                String format = String.format("send register user failed, code: %s, message: %s", execute.code(), execute.message());
                throw new RuntimeException(format);
            }
        }
    }

    private static boolean checkParams(BusinessReq businessReq) {
        if (businessReq.getAppKey() == null || "".equals(businessReq.getAppKey())) {
            throw new RuntimeException("appKey is null");
        }
        if (businessReq.getSysId() == null || "".equals(businessReq.getSysId())) {
            throw new RuntimeException("sysId is null");
        }
        if (businessReq.getData() == null) {
            throw new RuntimeException("data is null");
        }
        return true;
    }


}
