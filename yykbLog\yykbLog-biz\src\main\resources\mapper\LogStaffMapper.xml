<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.operation.electric.yykbLog.mapper.LogStaffMapper">

  <resultMap id="logStaffMap" type="com.operation.electric.yykbLog.entity.LogStaffEntity">
        <id property="id" column="id"/>
        <result property="staffNo" column="staff_no"/>
        <result property="staffNm" column="staff_nm"/>
        <result property="stdOrgNo" column="std_org_no"/>
        <result property="stdOrgNm" column="std_org_nm"/>
        <result property="stdDeptNo" column="std_dept_no"/>
        <result property="stdDeptNm" column="std_dept_nm"/>
        <result property="stdTeamNo" column="std_team_no"/>
        <result property="stdTeamNm" column="std_team_nm"/>
        <result property="sysId" column="sys_id"/>
        <result property="sysName" column="sys_name"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
  </resultMap>
</mapper>