FROM mysql/mysql-server:8.0.32

MAINTAINER electric(<EMAIL>)

ENV TZ=Asia/Shanghai

RUN ln -sf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

COPY ./1schema.sql /docker-entrypoint-initdb.d

COPY ./2electricx.sql /docker-entrypoint-initdb.d

COPY ./3electricx_flow.sql /docker-entrypoint-initdb.d

COPY ./4electricx_job.sql /docker-entrypoint-initdb.d

COPY ./5electricx_mp.sql /docker-entrypoint-initdb.d

COPY ./6electricx_config.sql /docker-entrypoint-initdb.d

COPY ./7electricx_pay.sql /docker-entrypoint-initdb.d

COPY ./8electricx_codegen.sql /docker-entrypoint-initdb.d

COPY ./99electricx_bi.sql /docker-entrypoint-initdb.d

COPY ./999electricx_app.sql /docker-entrypoint-initdb.d
