package com.operation.electric.yykbLog.service;

import com.operation.electric.yykbLog.entity.LogImportantStaffEntity;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;

@Component
public class LogPostVariables {

    @Autowired
    private LogImportantStaffService importantStaffService;

    private static HashSet<String> importantStaffNos = new HashSet<>();

    public static boolean isVipExist (String param){
        return importantStaffNos.contains(param);
    }

    @PostConstruct
    public void loadImportantStaffNos(){
        List<LogImportantStaffEntity> list = importantStaffService.list();
        for (LogImportantStaffEntity staff : list) {
            importantStaffNos.add(staff.getStaffNo());
        }
    }
}
