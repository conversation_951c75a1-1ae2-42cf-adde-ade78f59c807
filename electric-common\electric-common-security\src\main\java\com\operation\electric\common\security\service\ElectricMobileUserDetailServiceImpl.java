package com.operation.electric.common.security.service;

import com.operation.electric.admin.api.dto.UserInfo;
import com.operation.electric.admin.api.feign.RemoteUserService;
import com.operation.electric.common.core.constant.SecurityConstants;
import com.operation.electric.common.core.util.R;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
public class ElectricMobileUserDetailServiceImpl implements ElectricUserDetailsService {

	private final UserDetailsService electricDefaultUserDetailsServiceImpl;

	private final RemoteUserService remoteUserService;

	@Override
	@SneakyThrows
	public UserDetails loadUserByUsername(String phone) {
		R<UserInfo> result = remoteUserService.social(phone, SecurityConstants.FROM_IN);
		return getUserDetails(result);
	}

	@Override
	public UserDetails loadUserByUser(ElectricUser electricUser) {
		return electricDefaultUserDetailsServiceImpl.loadUserByUsername(electricUser.getUsername());
	}

	/**
	 * 支持所有的 mobile 类型
	 * @param clientId 目标客户端
	 * @param grantType 授权类型
	 * @return true/false
	 */
	@Override
	public boolean support(String clientId, String grantType) {
		return SecurityConstants.GRANT_MOBILE.equals(grantType);
	}

}
