package com.operation.electric.yykbLog.entity;

import lombok.*;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class BusinessReq<T extends Serializable> implements Serializable {
    private static final long serialVersionUID = 1L;

    private String sysId;

    private String appKey;

    private T data;

    @Override
    public String toString() {
        return "{" +
                "sysId:'" + sysId + '\'' +
                ", appKey:'" + appKey + '\'' +
                ", data:" + data +
                '}';
    }

}
