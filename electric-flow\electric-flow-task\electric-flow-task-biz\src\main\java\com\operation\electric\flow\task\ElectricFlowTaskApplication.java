package com.operation.electric.flow.task;

import com.operation.electric.common.feign.annotation.EnableElectricFeignClients;
import com.operation.electric.common.security.annotation.EnableElectricResourceServer;
import com.operation.electric.common.swagger.annotation.EnableOpenApi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * <AUTHOR> archetype
 * <p>
 * 项目启动类
 */
@EnableOpenApi("task")
@EnableElectricFeignClients
@EnableDiscoveryClient
@EnableElectricResourceServer
@SpringBootApplication
public class ElectricFlowTaskApplication {

	public static void main(String[] args) {
		SpringApplication.run(ElectricFlowTaskApplication.class, args);
	}

}
