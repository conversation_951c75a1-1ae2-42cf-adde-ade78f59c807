package com.operation.electric.yykb.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * APP首页最上方广播通知表
 *
 * <AUTHOR>
 * @date 2025-01-16 15:10:17
 */
@Data
@TableName("dw_igw_sys_notice")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "APP首页最上方广播通知表")
public class DwIgwSysNoticeEntity extends Model<DwIgwSysNoticeEntity> {


	/**
	* 消息ID
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="消息ID")
    private Long id;

	/**
	* 可发送html文本格式的消息
	*/
    @Schema(description="可发送html文本格式的消息")
    private String msgContent;

	/**
	* 消息类型（大数据、报表、RPA）
	*/
    @Schema(description="消息类型（大数据、报表、RPA）")
    private String msgType;

	/**
	* 消息紧急程度（0正常 1中等 2紧急）
	*/
    @Schema(description="消息紧急程度（0正常 1中等 2紧急）")
    private Integer msgEmergency;

	/**
	* 消息发布时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="消息发布时间")
    private LocalDateTime createTime;
}