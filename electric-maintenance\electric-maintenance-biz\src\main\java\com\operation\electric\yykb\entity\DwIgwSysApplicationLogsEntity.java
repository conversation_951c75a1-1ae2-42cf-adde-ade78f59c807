package com.operation.electric.yykb.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 上下线日志记录表单
 *
 * <AUTHOR>
 * @date 2025-01-13 18:07:46
 */
@Data
@TableName("dw_igw_sys_application_logs")
//@EqualsAndHashCode(callSuper = true)
@Schema(description = "上下线日志记录表单")
public class DwIgwSysApplicationLogsEntity  {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 应用编号
	*/
    @Schema(description="应用编号")
    private String appId;

	/**
	* 应用名称
	*/
    @Schema(description="应用名称")
    private String appName;

	/**
	* 应用环境
	*/
    @Schema(description="应用环境")
    private String appEnv;

	/**
	* 所属厂家
	*/
    @Schema(description="所属厂家")
    private String appFac;

	/**
	* 操作人
	*/
    @Schema(description="操作人")
    private String appCreateBy;

	/**
	* 发生时间
	*/
    @Schema(description="发生时间")
    private LocalDateTime appCreateTime;

	/**
	* 操作详情
	*/
    @Schema(description="操作详情")
    private String appOpreationDetail;

	/**
	* 操作结果
	*/
    @Schema(description="操作结果")
    private String appOpreationResult;

}