<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~      Copyright (c) 2018-2025, electric All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: electric
  ~
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.operation</groupId>
        <artifactId>electric-workflow</artifactId>
        <version>5.3.0</version>
    </parent>

    <artifactId>electric-workflow-api</artifactId>
    <packaging>jar</packaging>

    <description>electric 通用用户权限管理系统公共api模块</description>


    <dependencies>

        <!-- 自定义验证注解 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <!-- 连表查询注解 -->
        <dependency>
            <groupId>com.github.yulichang</groupId>
            <artifactId>mybatis-plus-join-annotation</artifactId>
        </dependency>
        <!--spring security 、oauth、jwt依赖-->
        <dependency>
            <groupId>com.operation</groupId>
            <artifactId>electric-common-security</artifactId>
        </dependency>
        <!--core 工具类-->
        <dependency>
            <groupId>com.operation</groupId>
            <artifactId>electric-common-core</artifactId>
        </dependency>
        <!--mybatis plus extension,包含了mybatis plus core-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
        </dependency>
        <!--feign 工具类-->
        <dependency>
            <groupId>com.operation</groupId>
            <artifactId>electric-common-feign</artifactId>
        </dependency>
        <!-- excel 导入导出 -->
        <dependency>
            <groupId>com.operation</groupId>
            <artifactId>electric-common-excel</artifactId>
        </dependency>
        <!--字段审计注解-->
        <dependency>
            <groupId>org.javers</groupId>
            <artifactId>javers-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-extra</artifactId>
        </dependency>
        <!-- flowable 核心依赖-->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-bpmn-layout</artifactId>
            <version>${flowable.version}</version>
        </dependency>

        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
        </dependency>

    </dependencies>
</project>
