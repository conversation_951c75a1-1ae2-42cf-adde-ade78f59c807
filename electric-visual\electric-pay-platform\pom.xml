<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.operation</groupId>
        <artifactId>electric-visual</artifactId>
        <version>5.3.0</version>
    </parent>

    <artifactId>electric-pay-platform</artifactId>
    <packaging>jar</packaging>

    <description>支付系统</description>
    <properties>
        <yungouos.version>2.0.4</yungouos.version>
    </properties>

    <dependencies>
        <!--注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!--mybatis-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
        </dependency>

        <!--PG-->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>

        <!--DM8-->
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
        </dependency>

        <!-- druid 连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-3-starter</artifactId>
        </dependency>
        <!--common-->
        <dependency>
            <groupId>com.operation</groupId>
            <artifactId>electric-common-data</artifactId>
        </dependency>
        <dependency>
            <groupId>com.operation</groupId>
            <artifactId>electric-common-sequence</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-extra</artifactId>
        </dependency>
        <!--灰度支持-->
        <dependency>
            <groupId>com.operation</groupId>
            <artifactId>electric-common-gray</artifactId>
        </dependency>
        <!-- sentinel-->
        <dependency>
            <groupId>com.operation</groupId>
            <artifactId>electric-common-sentinel</artifactId>
        </dependency>
        <!--feign 接口-->
        <dependency>
            <groupId>com.operation</groupId>
            <artifactId>electric-upms-api</artifactId>
        </dependency>
        <!--swagger-->
        <dependency>
            <groupId>com.operation</groupId>
            <artifactId>electric-common-swagger</artifactId>
        </dependency>
        <!--安全模块-->
        <dependency>
            <groupId>com.operation</groupId>
            <artifactId>electric-common-xss</artifactId>
        </dependency>
        <dependency>
            <groupId>com.operation</groupId>
            <artifactId>electric-common-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.operation</groupId>
            <artifactId>electric-common-log</artifactId>
        </dependency>
        <!-- 支付依赖-->
        <dependency>
            <groupId>com.github.javen205</groupId>
            <artifactId>IJPay-WxPay</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.javen205</groupId>
            <artifactId>IJPay-AliPay</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-mp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yungouos.pay</groupId>
            <artifactId>yungouos-pay-sdk</artifactId>
            <version>${yungouos.version}</version>
        </dependency>
        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
