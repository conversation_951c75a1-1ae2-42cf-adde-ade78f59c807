package com.operation.electric.yykbLog.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.operation.electric.yykbLog.dto.StaffResult;
import com.operation.electric.yykbLog.entity.LogStaffEntity;
import com.operation.electric.yykbLog.exception.StaffExistException;
import com.operation.electric.yykbLog.logRest.v1.vm.StaffVm;
import com.operation.electric.yykbLog.mapper.LogStaffMapper;
import com.operation.electric.yykbLog.service.LogStaffService;
import com.operation.electric.yykbLog.utils.QdAuth;
import jakarta.validation.constraints.NotNull;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 日志系统人员基础档案表
 *
 * <AUTHOR>
 * @date 2024-03-21 16:54:37
 */
@Service
public class LogStaffServiceImpl extends ServiceImpl<LogStaffMapper, LogStaffEntity> implements LogStaffService {
    @Override
    public StaffResult findBySpec(@NotNull String staffNo, @NotNull String sysId) {
        return this.baseMapper.selectList(Wrappers.<LogStaffEntity>lambdaQuery().eq(LogStaffEntity::getStaffNo, staffNo).eq(LogStaffEntity::getSysId, sysId)).stream().findFirst().map(staff -> {
            StaffResult staffResult = new StaffResult();
            BeanUtil.copyProperties(staff, staffResult);
            return staffResult;
        }).orElse(null);
    }

    @Override
    public StaffVm addStaff(StaffVm vm) {
        vm.setStaffNo(vm.getStaffNo().trim());

        StaffResult bySpec = findBySpec(vm.getStaffNo(), vm.getSysId());
        if(bySpec != null) {
            throw new StaffExistException(vm.getStaffNo(),vm.getSysId());
        }

        LogStaffEntity staff = new LogStaffEntity();
        BeanUtil.copyProperties(vm, staff);
        QdAuth.setOrgNm(staff);
        staff.setCreatedAt(new Date());
        save(staff);
        vm.setId(staff.getId());
        return vm;
    }
}