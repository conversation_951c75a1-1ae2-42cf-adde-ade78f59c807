package com.operation.electric.yykbLog.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.operation.electric.yykbLog.entity.LogStaffEntity;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class QdAuth {
    //轻点账号和密码
    private static String USERNAME = "daw";
    private static String PASSWORD = "Daw@123!";
    public static String TOKEN;

    public static String getAccessToken() {
        String apiUrl = "http://25.212.175.3:19003/qd-boot/sys/getToken";

        Map<String,String> queryParam = new HashMap<>(2);
        queryParam.put("username", USERNAME);
        queryParam.put("password", PASSWORD);

        String jsonReq = JSONObject.toJSONString(queryParam);
        JSONObject pageObject = JSON.parseObject(HttpClientUtil.getInstance().post(apiUrl, jsonReq, null));
        TOKEN = pageObject.getJSONObject("result").getString("token");
        System.out.println(TOKEN);
        return TOKEN;
    }

    public static void setOrgNm(LogStaffEntity staff){
        String apiUrl = "http://25.212.175.3:19003/qd-boot/api/isc/agent";

        try {
            getAccessToken();
            Map<String,String> token = new HashMap<>(2);
            token.put("X-Access-Token", QdAuth.TOKEN);

            String getParam = "?landingNm=" + staff.getStaffNo();
            System.out.println(apiUrl + getParam);
            JSONObject jsonObject = JSON.parseObject(HttpClientUtil.getInstance().get(apiUrl + getParam, token));
            if (401 == jsonObject.getInteger("code")) {
                return;
            }

            JSONObject result = jsonObject.getJSONObject("result");
            
            if (Objects.isNull(result)) return;

            String cityOrgNo = result.getString("cityOrgNo");
            String cityOrgNm = result.getString("cityOrgNm");
            String countyOrgNo = result.getString("countyOrgNo");
            String countyOrgNm = result.getString("countyOrgNm");
            String orgNo = result.getString("orgNo");
            String orgNm = result.getString("orgNm");
            String userNm = result.getString("userNm");
            if (StrUtil.isNotEmpty(userNm)){
                staff.setStaffNm(userNm);
            }
            if (StrUtil.isNotEmpty(cityOrgNo)){
                staff.setStdOrgNo(cityOrgNo);
            }
            if (StrUtil.isNotEmpty(cityOrgNm)){
                staff.setStdOrgNm(cityOrgNm);
            }
            if (StrUtil.isNotEmpty(countyOrgNo)){
                staff.setStdDeptNo(countyOrgNo);
            }
            if (StrUtil.isNotEmpty(countyOrgNm) && ! "临时组织".equals(countyOrgNm) && ! "其他虚拟机构".equals(countyOrgNm)){
                staff.setStdDeptNm(countyOrgNm);
            }
            if (StrUtil.isNotEmpty(orgNo)){
                staff.setStdTeamNo(orgNo);
            }
            if (StrUtil.isNotEmpty(orgNm) && ! "临时组织".equals(orgNm) && ! "其他虚拟机构".equals(orgNm)){
                staff.setStdTeamNm(orgNm);
            }
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
    }
}
