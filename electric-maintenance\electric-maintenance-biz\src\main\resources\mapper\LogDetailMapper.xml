<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.operation.electric.yykb.mapper.LogDetailMapper">

  <resultMap id="logDetailMap" type="com.operation.electric.yykb.entity.LogDetailEntity">
        <id property="id" column="id"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="staffNo" column="staff_no"/>
        <result property="timestamp" column="timestamp"/>
        <result property="operationClass1" column="operation_class1"/>
        <result property="operationClass2" column="operation_class2"/>
        <result property="operationClass3" column="operation_class3"/>
        <result property="operationClass4" column="operation_class4"/>
        <result property="operationClass5" column="operation_class5"/>
        <result property="ip" column="ip"/>
        <result property="sysId" column="sys_id"/>
        <result property="sysName" column="sys_name"/>
        <result property="sysVersion" column="sys_version"/>
        <result property="onlineTime" column="online_time"/>
        <result property="vipFlag" column="vip_flag"/>
        <result property="serverIp" column="server_ip"/>
  </resultMap>
    <select id="selectStaffByOrgNo" resultType="java.lang.String" parameterType="java.lang.String">
          select staff_nm from log_staff
          <where>
                <if test="orgNo != null || orgNo != ''">
                      std_org_no = #{orgNo} or std_dept_no = #{orgNo} or std_team_no = #{orgNo}
                </if>
          </where>
    </select>
      <select id="selectSystemInfoList" resultType="java.lang.String">
            select sys_name from log_detail group by sys_name
      </select>
</mapper>
