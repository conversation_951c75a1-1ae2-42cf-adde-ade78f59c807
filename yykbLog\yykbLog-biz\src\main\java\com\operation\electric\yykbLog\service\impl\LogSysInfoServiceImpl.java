package com.operation.electric.yykbLog.service.impl;

import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.operation.electric.common.security.service.ElectricUser;
import com.operation.electric.common.security.util.SecurityUtils;
import com.operation.electric.yykbLog.entity.BusinessReq;
import com.operation.electric.yykbLog.entity.Journal;
import com.operation.electric.yykbLog.entity.LogSysInfoEntity;
import com.operation.electric.yykbLog.entity.OperateUser;
import com.operation.electric.yykbLog.exception.SysInfoAuthError;
import com.operation.electric.yykbLog.mapper.LogSysInfoMapper;
import com.operation.electric.yykbLog.service.LogSysInfoService;
import com.operation.electric.yykbLog.utils.BusinessLogUtils;
import net.bytebuddy.utility.RandomString;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
 * 应用系统档案
 *
 * <AUTHOR>
 * @date 2024-03-21 17:21:46
 */
@Service
public class LogSysInfoServiceImpl extends ServiceImpl<LogSysInfoMapper, LogSysInfoEntity> implements LogSysInfoService {


    @Override
    public LogSysInfoEntity add(LogSysInfoEntity param) {
        param.setSalt(this.generateSale());
        param.setAppKey(this.generateAppKey());
        param.setAppSecret(md5(param));
        Long id = SecurityUtils.getUser().getId();
        param.setCreatorId(id);
        param.setSysId(RandomString.make(5));
        this.save(param);
        String format = String.format("%05d", param.getId());
        param.setSysId(format);
        param.setAppSecret(md5(param));
        update(Wrappers.<LogSysInfoEntity>lambdaUpdate().set(LogSysInfoEntity::getSysId, format)
                .set(LogSysInfoEntity::getAppSecret, param.getAppSecret())
                .eq(LogSysInfoEntity::getId, param.getId()));

        addLog(param);
        System.out.println(param);
        return param;
    }

    private void addLog(LogSysInfoEntity param){

        ElectricUser user = SecurityUtils.getUser();

        //先注册用户
        BusinessReq<OperateUser> userReq = new BusinessReq<>();
        userReq.setSysId(param.getSysId());
        userReq.setAppKey(param.getAppKey());
        userReq.setData(OperateUser.builder()
                .staffNm(user.getName())
                .staffNo(user.getName())
                .stdDeptNm("--")
                .stdDeptNo(user.getDeptId().toString())
                .stdTeamNo("--")

                .stdTeamNm("--")
                .stdOrgNo("--")
                .stdOrgNm("--")
                .sysName(param.getSysName())
                .build());

        BusinessLogUtils.registerUser("http://**********:19666", userReq);

        BusinessReq<Journal> build = new BusinessReq<>();

        build.setSysId(param.getSysId());
        build.setAppKey(param.getAppKey());
        Journal journal = Journal.builder()
                .ip("127.0.0.1")
                .timestamp(new Date())
                .operationClass1("日志监控")
                .staffNo(user.getName())
                .operationClass2("应用系统档案信息")
                .operationClass3("新增")
                .sysName(param.getSysName()).build();
        build.setData(journal);
        BusinessLogUtils.sendLog("http://**********:19666", build);

    }

    /**
     * 校验系统信息
     *
     * @param appId
     * @param sysId
     */
    @Override
    public Boolean checkSysInfo(String appId, String sysId) {
        if (StringUtils.isBlank(appId) || StringUtils.isBlank(sysId)) {
            throw new SysInfoAuthError("认证失败，SYS-ID或APP-KEY错误！");
        }
        LogSysInfoEntity sysInfo = this.baseMapper.selectByAppIdAndSysId(appId, sysId);
        if (sysInfo == null) {
            throw new SysInfoAuthError("认证失败，SYS-ID或APP-KEY错误！");
        }
        if (sysId.length() != 5) {
            throw new SysInfoAuthError("认证失败，SYS-ID或APP-KEY错误！");
        }
        String encrypt = md5(LogSysInfoEntity.builder().appKey(appId).salt(sysInfo.getSalt()).sysId(sysId).build());
        if (!encrypt.equals(sysInfo.getAppSecret())) {
            throw new SysInfoAuthError("认证失败，SYS-ID或APP-KEY错误！");
        }
        return true;
    }

    private String generateAppKey() {
        return RandomString.make(12);
    }

    private String generateSale() {
        return RandomString.make(10);
    }

    private String md5(LogSysInfoEntity entity) {
        return SecureUtil.md5((new StringBuilder(entity.getAppKey()).append(entity.getSalt()).append(entity.getSysId()).toString()));
    }

}