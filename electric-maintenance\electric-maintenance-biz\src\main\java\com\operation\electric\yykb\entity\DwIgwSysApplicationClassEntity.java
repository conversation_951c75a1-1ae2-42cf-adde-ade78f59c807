package com.operation.electric.yykb.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 应用大类表单
 *
 * <AUTHOR>
 * @date 2025-01-13 14:33:45
 */
@Data
@TableName("dw_igw_sys_application_class")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "应用大类表单")
public class DwIgwSysApplicationClassEntity extends Model<DwIgwSysApplicationClassEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

	/**
	* 名称
	*/
    @Schema(description="名称")
    private String applicationName;
}