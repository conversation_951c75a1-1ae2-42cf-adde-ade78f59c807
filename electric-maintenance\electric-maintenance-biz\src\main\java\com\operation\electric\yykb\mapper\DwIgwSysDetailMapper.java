package com.operation.electric.yykb.mapper;

import com.operation.electric.common.data.datascope.ElectricBaseMapper;
import com.operation.electric.yykb.api.entity.DwIgwSysDetailEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DwIgwSysDetailMapper extends ElectricBaseMapper<DwIgwSysDetailEntity> {

    Integer monitor(String appId);

    List<DwIgwSysDetailEntity> monitoringAllData();

    String monitorError(String appId);

    Integer quantitativeRestriction();

    List<String> findAll();

    List<DwIgwSysDetailEntity> findDetailByAppID(@Param("appId") String appId);
}