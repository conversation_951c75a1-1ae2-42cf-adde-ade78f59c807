package com.operation.electric.daemon.elastic.job;

import com.operation.electric.admin.api.dto.UserInfo;
import com.operation.electric.admin.api.feign.RemoteUserService;
import com.operation.electric.app.api.dto.AppUserInfo;
import com.operation.electric.app.api.feign.RemoteAppUserService;
import com.operation.electric.common.core.constant.SecurityConstants;
import com.operation.electric.common.core.util.R;
import com.operation.electric.common.security.service.ElectricUserDetailsService;
import com.operation.electric.common.security.util.SecurityUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;


/**
 * Name:ticket-center-api
 * User: yjh
 * Date: 2024/12/11
 * Time: 16:59
 * Description:
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ExecuteBean {
	private static final Logger logger = LoggerFactory.getLogger(ExecuteBean.class);

	private final RemoteUserService remoteUserService;

	@XxlJob("test01")
	public void test01(){
		String jobParam = XxlJobHelper.getJobParam();
		logger.info("请求被执行："  + jobParam);
		R<UserInfo> admin = remoteUserService.info("admin", SecurityConstants.FROM_IN);
		System.out.println("你好 我被执行了！！！！！！！！！！！" + admin.getData().toString());
	}
}
