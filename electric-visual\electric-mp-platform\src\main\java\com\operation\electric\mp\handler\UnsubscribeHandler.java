package com.operation.electric.mp.handler;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.operation.electric.mp.constant.SubStatusEnum;
import com.operation.electric.mp.entity.WxAccountFans;
import com.operation.electric.mp.mapper.WxAccountFansMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR> <PERSON>(https://github.com/binarywang)
 */
@Slf4j
@Component
@AllArgsConstructor
public class UnsubscribeHandler extends AbstractHandler {

	private final WxAccountFansMapper wxAccountFansMapper;

	@Override
	public WxMpXmlOutMessage handle(WxMpXmlMessage wxMessage, Map<String, Object> context, WxMpService wxMpService,
			WxSessionManager sessionManager) {
		String openId = wxMessage.getFromUser();
		log.info("取消关注用户 OPENID: " + openId);
		WxAccountFans fans = new WxAccountFans();
		fans.setSubscribeStatus(SubStatusEnum.UNSUB.getType());
		wxAccountFansMapper.update(fans, Wrappers.<WxAccountFans>lambdaUpdate().eq(WxAccountFans::getOpenid, openId));
		return null;
	}

}
