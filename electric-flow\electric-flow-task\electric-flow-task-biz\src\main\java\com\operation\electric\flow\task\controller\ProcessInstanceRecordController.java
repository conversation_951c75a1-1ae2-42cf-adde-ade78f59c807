package com.operation.electric.flow.task.controller;

import com.operation.electric.common.core.util.R;
import com.operation.electric.flow.task.service.IProcessInstanceRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/process-instance-record")
public class ProcessInstanceRecordController {

    private final IProcessInstanceRecordService processInstanceRecordService;

    @GetMapping("/findScheduleData")
    public R findScheduleData(){

        return R.ok();

    }
}
