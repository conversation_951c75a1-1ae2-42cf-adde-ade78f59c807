package com.operation.electric.yykb.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.operation.electric.yykb.entity.DwIgwPhotoEntity;
import com.operation.electric.yykb.entity.DwIgwQuestionFeedbackEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: zhuoxaing.peng
 * @Date: 2025-06-23 16:12
 * @description:
 * @Version: 1.0
 */
@Data
public class DwIgwQuestionFeedbackDto extends DwIgwQuestionFeedbackEntity {

	/**
	 * 图片集合
	 */
	private List<DwIgwPhotoEntity> photoEntityList;
}
