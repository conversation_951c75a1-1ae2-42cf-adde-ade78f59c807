package com.operation.electric.yykb.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.operation.electric.common.data.datascope.ElectricBaseMapper;
import com.operation.electric.yykb.api.dto.LogDetailDTO;
import com.operation.electric.yykb.entity.LogDetailEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface LogDetailMapper extends ElectricBaseMapper<LogDetailEntity> {


    IPage<LogDetailDTO> selectLogDetailAndLogStaffInSysName(@Param("page")Page page, @Param("params") LogDetailDTO logDetailDTO);
}
