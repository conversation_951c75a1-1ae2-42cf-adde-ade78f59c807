<!doctype html>
<html>
  <head>
    <title>Druid ActiveConnection StackTrace</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf8" />
    <link href='css/bootstrap.min.css' rel="stylesheet" />
    <link href="css/style.css" type="text/css" rel="stylesheet"/>
    <script type="text/javascript" src="js/jquery.min.js"></script>
    <script src="js/lang.js" type="text/javascript" charset="utf8"></script>
    <script src="js/common.js" type="text/javascript" charset="utf8"></script>
    
  </head>
  <body onload="init();">
  	
    <div class="container-fluid">
      <h2>ActiveConnection StackTrace for Datasource
        <a href="activeConnectionStackTrace-{datasourceId}.json" target="_blank">[View JSON API]</a>
      </h2>
      <div id="activeConnectionStackTraceDiv">
        
      </div>
    </div>
  <script type="text/javascript">
    function init() {
      druid.common.buildHead(7);
    }
    $.namespace("druid.activeConnectionStackTrace");
    druid.activeConnectionStackTrace = function () { 
    var datasourceId = druid.common.getUrlVar("datasourceId");
      return  {
        init : function() {
          druid.activeConnectionStackTrace.ajaxRequestForActiveConnectionStackTrace();
          var time2=setInterval("druid.activeConnectionStackTrace.ajaxRequestForActiveConnectionStackTrace();",8000);
      },
      
      ajaxRequestForActiveConnectionStackTrace : function() {
        $.ajax({
          type: 'POST',
          url: 'activeConnectionStackTrace-' + datasourceId + '.json',
          success: function(data) {
            var conntionStackTraceList = data.Content;
            if (conntionStackTraceList == null)
              return;

            var content = [];

            for ( var i = 0; i < conntionStackTraceList.length; i++) {
              if (i > 0) {
                content.push("<br>");
              }
              var conntionStackTrace = conntionStackTraceList[i];
              content.push("<textarea rows='15' style='width:680px;' >");
              content.push(conntionStackTrace);
              content.push("</textarea>");
            } 
            $("#activeConnectionStackTraceDiv").html(content.join(""));
          },
          dataType: "json"
        });
      }
    }
  }();

  $(document).ready(function() {
    druid.activeConnectionStackTrace.init();
  });
  </script>
  </body>
</html>
