package com.operation.electric.yykbLog.logRest.v1;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.operation.electric.common.security.annotation.Inner;
import com.operation.electric.yykbLog.entity.LogSysInfoEntity;
import com.operation.electric.yykbLog.logRest.security.ApiOptionAuth;
import com.operation.electric.yykbLog.logRest.v1.vm.AppInfoVm;
import com.operation.electric.yykbLog.response.RestResponseData;
import com.operation.electric.yykbLog.service.LogSysInfoService;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@Inner(false)
@RestController
@RequestMapping("/api/log/apps")
@Tag(name = "系统应用清单", description = "系统应用信息列表")
@Slf4j
public class AppResource {


    @Autowired
    private LogSysInfoService sysInfoService;

    @PostMapping("/list")
    @ApiOptionAuth(required = false)
    @Schema(name = "获取应用系统清单", description = "获取应用系统清单")
    @Tag(name = "v1")
    public RestResponseData<IPage> index(@ParameterObject Page page, @RequestBody AppInfoVm params) {
        if (StringUtils.isBlank(params.getSysId()) || StringUtils.isBlank(params.getAppKey())) {
            return RestResponseData.error("sysId 和 appKey不能为空");
        }
        LogSysInfoEntity one = sysInfoService.getOne(Wrappers.<LogSysInfoEntity>lambdaQuery().eq(LogSysInfoEntity::getSysId, params.getSysId()));
        if (one == null) {
            return RestResponseData.error("sysId不存在");
        }
        if (!one.getAppKey().equals(params.getAppKey())) {
            return RestResponseData.error("appKey错误");
        }

        LambdaQueryWrapper<LogSysInfoEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StrUtil.isNotBlank(params.getSysName()),LogSysInfoEntity::getSysName,params.getSysName());
        IPage pageBySpec = sysInfoService.page(page, wrapper);
        List<AppInfoVm> result = new ArrayList<>();
        for (Object datum : pageBySpec.getRecords()) {
            LogSysInfoEntity infoResult = (LogSysInfoEntity) datum;
            AppInfoVm appInfoVm = new AppInfoVm();
            BeanUtil.copyProperties(infoResult, appInfoVm);
            appInfoVm.setAppKey("");
            result.add(appInfoVm);
        }
        pageBySpec.setRecords(result);
        log.info("获取应用系统清单接口!获取数据条数:{}", result.size());
        return RestResponseData.success(pageBySpec);
    }

}
