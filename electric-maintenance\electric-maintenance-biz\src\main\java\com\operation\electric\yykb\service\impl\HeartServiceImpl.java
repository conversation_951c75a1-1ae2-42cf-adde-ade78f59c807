package com.operation.electric.yykb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.operation.electric.yykb.api.entity.DwIgwSysDetailEntity;
import com.operation.electric.yykb.entity.HeartMessage;
import com.operation.electric.yykb.entity.LogSysInfo;
import com.operation.electric.yykb.mapper.HeartMapper;
import com.operation.electric.yykb.service.DwIgwSysDetailService;
import com.operation.electric.yykb.service.HeartService;
import com.operation.electric.yykb.service.LogSysInfoService;
import com.operation.electric.yykb.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 【】
 *
 * <AUTHOR>
 * @date 2024/11/07
 */
@Service
public class HeartServiceImpl extends ServiceImpl<HeartMapper, HeartMessage> implements HeartService {

    @Autowired
    private HeartMapper heartMapper;
    @Autowired
    private LogSysInfoService logSysInfoService;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private DwIgwSysDetailService dwIgwSysDetailService;
    /**
     * 心跳过期时间5分钟
     * @param heartMessage
     * @return
     */
    @Override
    public Boolean checkHeart(HeartMessage heartMessage) {
		LogSysInfo sysInfoApplication = this.getSysInfoApplication(heartMessage);
		if(null == sysInfoApplication){
			return false;
		}
		//3.如果存在,将应用信息存储在应用心跳表中以及缓存中，用于后续的应用下线逻辑处理
        redisUtils.setCacheObject("heart:" + heartMessage.getAppKey(), sysInfoApplication, 5, java.util.concurrent.TimeUnit.MINUTES);
        //4.返回true
        return true;
    }

	private LogSysInfo getSysInfoApplication(HeartMessage heartMessage){
		//1.根据心跳实体的appKey和appSecret查询数据库，判断应用是否在我们的系统中是否存在
		LambdaQueryWrapper<LogSysInfo> lqw = new LambdaQueryWrapper<>();
		lqw.eq(LogSysInfo::getAppKey, heartMessage.getAppKey())
				.eq(LogSysInfo::getAppSecret, heartMessage.getAppSecret())
				.eq(LogSysInfo::getStatus, 1).last("limit 1");
		LogSysInfo one = logSysInfoService.getOne(lqw);
		//2.如果存在，则返回true，否则返回false
			return one;
	}
}
