package com.operation.electric.yykbLog.response;



import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(
        description = "响应数据"
)
public class RestResponseData<T> implements Serializable {
    private T data;
    public static final String DEFAULT_SUCCESS_MESSAGE = "请求成功";
    public static final String DEFAULT_ERROR_MESSAGE = "网络异常";
    public static final Integer DEFAULT_SUCCESS_CODE = 200;
    public static final Integer DEFAULT_ERROR_CODE = 500;
    @Schema(
            name = "是否成功",
            description = "true:成功,false:失败",
            example = "true"
    )
    private Boolean success;
    @Schema(
            name = "返回码",
            example = "200"
    )
    private Integer code;
    @Schema(
            name = "返回消息",
            example = "请求成功"
    )
    private String message;

    public RestResponseData() {
    }

    public RestResponseData(Boolean success, Integer code, String message, T data) {
        this.success = success;
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static <T extends Serializable> SuccessRestResponseData<T> success() {
        return new SuccessRestResponseData<>();
    }

    public static <T extends Serializable> SuccessRestResponseData<T> success(T object) {
        return new SuccessRestResponseData<T>(object);
    }

    public static <T extends Serializable> SuccessRestResponseData<T> success(Integer code, String message, T object) {
        return new SuccessRestResponseData<>(code, message, object);
    }

    public static ErrorRestResponseData error(String message) {
        return new ErrorRestResponseData<>(message);
    }

    public static ErrorRestResponseData error(Integer code, String message) {
        return new ErrorRestResponseData<>(code, message);
    }

    public static <T extends Serializable> ErrorRestResponseData<T> error(Integer code, String message, T object) {
        return new ErrorRestResponseData<T>(code, message, object);
    }

    public Boolean getSuccess() {
        return this.success;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }

    public T getData() {
        return this.data;
    }

    public void setSuccess(final Boolean success) {
        this.success = success;
    }

    public void setCode(final Integer code) {
        this.code = code;
    }

    public void setMessage(final String message) {
        this.message = message;
    }

    public void setData(final T data) {
        this.data = data;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof RestResponseData)) {
            return false;
        } else {
            RestResponseData other = (RestResponseData) o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label59:
                {
                    Object this$success = this.getSuccess();
                    Object other$success = other.getSuccess();
                    if (this$success == null) {
                        if (other$success == null) {
                            break label59;
                        }
                    } else if (this$success.equals(other$success)) {
                        break label59;
                    }

                    return false;
                }

                Object this$code = this.getCode();
                Object other$code = other.getCode();
                if (this$code == null) {
                    if (other$code != null) {
                        return false;
                    }
                } else if (!this$code.equals(other$code)) {
                    return false;
                }

                Object this$message = this.getMessage();
                Object other$message = other.getMessage();
                if (this$message == null) {
                    if (other$message != null) {
                        return false;
                    }
                } else if (!this$message.equals(other$message)) {
                    return false;
                }

                Object this$data = this.getData();
                Object other$data = other.getData();
                if (this$data == null) {
                    if (other$data != null) {
                        return false;
                    }
                } else if (!this$data.equals(other$data)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof RestResponseData;
    }

    public int hashCode() {
        boolean PRIME = true;
        int result = 1;
        Object $success = this.getSuccess();
        result = result * 59 + ($success == null ? 43 : $success.hashCode());
        Object $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        Object $message = this.getMessage();
        result = result * 59 + ($message == null ? 43 : $message.hashCode());
        Object $data = this.getData();
        result = result * 59 + ($data == null ? 43 : $data.hashCode());
        return result;
    }

    public String toString() {
        return "RestResponseData(success=" + this.getSuccess() + ", code=" + this.getCode() + ", message=" + this.getMessage() + ", data=" + this.getData() + ")";
    }
}
