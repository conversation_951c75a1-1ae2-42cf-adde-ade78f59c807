2025-07-10 19:07:26 jdbc[4]: null
org.h2.message.DbException: The connection was not closed by the application and is garbage collected [90018-214]
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.jdbc.JdbcConnection.closeOld(JdbcConnection.java:190)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:132)
	at org.h2.Driver.connect(Driver.java:59)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1801)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2946)
Caused by: org.h2.jdbc.JdbcSQLNonTransientConnectionException: The connection was not closed by the application and is garbage collected [90018-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:678)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	... 9 more
2025-07-10 19:07:30 jdbc[6]: java.lang.Exception: Open Stack Trace
	at org.h2.util.CloseWatcher.register(CloseWatcher.java:85)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:133)
	at org.h2.Driver.connect(Driver.java:59)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1801)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2946)

org.h2.message.DbException: The connection was not closed by the application and is garbage collected [90018-214]
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.jdbc.JdbcConnection.closeOld(JdbcConnection.java:190)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:132)
	at org.h2.Driver.connect(Driver.java:59)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1801)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2946)
Caused by: org.h2.jdbc.JdbcSQLNonTransientConnectionException: The connection was not closed by the application and is garbage collected [90018-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:678)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	... 9 more
2025-07-10 19:07:30 jdbc[6]: java.lang.Exception: Open Stack Trace
	at org.h2.util.CloseWatcher.register(CloseWatcher.java:85)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:133)
	at org.h2.Driver.connect(Driver.java:59)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1801)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2946)

org.h2.message.DbException: The connection was not closed by the application and is garbage collected [90018-214]
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.jdbc.JdbcConnection.closeOld(JdbcConnection.java:190)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:132)
	at org.h2.Driver.connect(Driver.java:59)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1801)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2946)
Caused by: org.h2.jdbc.JdbcSQLNonTransientConnectionException: The connection was not closed by the application and is garbage collected [90018-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:678)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	... 9 more
2025-07-10 19:07:35 jdbc[7]: java.lang.Exception: Open Stack Trace
	at org.h2.util.CloseWatcher.register(CloseWatcher.java:85)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:133)
	at org.h2.Driver.connect(Driver.java:59)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1801)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2946)

org.h2.message.DbException: The connection was not closed by the application and is garbage collected [90018-214]
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.jdbc.JdbcConnection.closeOld(JdbcConnection.java:190)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:132)
	at org.h2.Driver.connect(Driver.java:59)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1801)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2946)
Caused by: org.h2.jdbc.JdbcSQLNonTransientConnectionException: The connection was not closed by the application and is garbage collected [90018-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:678)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	... 9 more
2025-07-10 19:07:36 jdbc[8]: java.lang.Exception: Open Stack Trace
	at org.h2.util.CloseWatcher.register(CloseWatcher.java:85)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:133)
	at org.h2.Driver.connect(Driver.java:59)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1801)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2946)

org.h2.message.DbException: The connection was not closed by the application and is garbage collected [90018-214]
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.jdbc.JdbcConnection.closeOld(JdbcConnection.java:190)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:132)
	at org.h2.Driver.connect(Driver.java:59)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1801)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2946)
Caused by: org.h2.jdbc.JdbcSQLNonTransientConnectionException: The connection was not closed by the application and is garbage collected [90018-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:678)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	... 9 more
2025-07-10 19:07:41 jdbc[10]: java.lang.Exception: Open Stack Trace
	at org.h2.util.CloseWatcher.register(CloseWatcher.java:85)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:133)
	at org.h2.Driver.connect(Driver.java:59)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1801)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2946)

org.h2.message.DbException: The connection was not closed by the application and is garbage collected [90018-214]
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.jdbc.JdbcConnection.closeOld(JdbcConnection.java:190)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:132)
	at org.h2.Driver.connect(Driver.java:59)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1801)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2946)
Caused by: org.h2.jdbc.JdbcSQLNonTransientConnectionException: The connection was not closed by the application and is garbage collected [90018-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:678)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	... 9 more
2025-07-10 19:07:41 jdbc[10]: java.lang.Exception: Open Stack Trace
	at org.h2.util.CloseWatcher.register(CloseWatcher.java:85)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:133)
	at org.h2.Driver.connect(Driver.java:59)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1801)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2946)

org.h2.message.DbException: The connection was not closed by the application and is garbage collected [90018-214]
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.message.DbException.get(DbException.java:188)
	at org.h2.jdbc.JdbcConnection.closeOld(JdbcConnection.java:190)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:132)
	at org.h2.Driver.connect(Driver.java:59)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1801)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2946)
Caused by: org.h2.jdbc.JdbcSQLNonTransientConnectionException: The connection was not closed by the application and is garbage collected [90018-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:678)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	... 9 more
