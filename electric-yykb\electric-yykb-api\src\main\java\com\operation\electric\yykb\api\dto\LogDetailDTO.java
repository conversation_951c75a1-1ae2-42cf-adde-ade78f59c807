package com.operation.electric.yykb.api.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 日志系统，系统使用详情日志记录信息
 *
 * <AUTHOR>
 * @date 2025-01-02 18:14:48
 */
@Data
@EqualsAndHashCode
@Schema(description = "日志系统，系统使用详情日志记录信息")
public class LogDetailDTO implements Serializable {


	/**
	* 日志记录唯一编号UUID
	*/
    @Schema(description="日志记录唯一编号UUID")
    private String id;

	/**
	* 数据记录时间
	*/
    @Schema(description="数据记录时间")
    private LocalDateTime createdAt;

	/**
	* 数据更新时间
	*/
    @Schema(description="数据更新时间")
    private LocalDateTime updatedAt;

	/**
	* 电力统一权限登陆平台OA账号
	*/
    @Schema(description="电力统一权限登陆平台OA账号")
    private String staffNo;

	/**
	* 动作时间，使用app操作的时间点
	*/
    @Schema(description="动作时间，使用app操作的时间点")
    private LocalDateTime timestamp;

	/**
	* 一级操作名称。例如：登陆/退出登陆/超超时退出
	*/
    @Schema(description="一级操作名称。例如：登陆/退出登陆/超超时退出")
    private String operationClass1;

	/**
	* 二级操作名称。例如：进入数字员工的电费明细清单机器人列表。则一级菜单为营销。二级操作名称为：电费明细清单打印机器人
	*/
    @Schema(description="二级操作名称。例如：进入数字员工的电费明细清单机器人列表。则一级菜单为营销。二级操作名称为：电费明细清单打印机器人")
    private String operationClass2;

	/**
	* 三级操作名称
	*/
    @Schema(description="三级操作名称")
    private String operationClass3;

	/**
	* 四级操作名称
	*/
    @Schema(description="四级操作名称")
    private String operationClass4;

	/**
	* 五级操作名称
	*/
    @Schema(description="五级操作名称")
    private String operationClass5;

	/**
	* 使用系统时客户的电脑IP
	*/
    @Schema(description="使用系统时客户的电脑IP")
    private String ip;

	/**
	* 接入日志系统 的第三方应用/系统的编号
	*/
    @Schema(description="接入日志系统 的第三方应用/系统的编号")
    private String sysId;

	/**
	* 接入日志系统 的第三方应用/系统的名称
	*/
    @Schema(description="接入日志系统 的第三方应用/系统的名称")
    private String sysName;

	/**
	* 接入日志系统 的第三方应用/系统的版本
	*/
    @Schema(description="接入日志系统 的第三方应用/系统的版本")
    private String sysVersion;

	/**
	* 在线时间(秒)
	*/
    @Schema(description="在线时间(秒)")
    private BigDecimal onlineTime;

	/**
	* 重要用户标记
	*/
    @Schema(description="重要用户标记")
    private Integer vipFlag;

	/**
	* 推送日志服务器ip
	*/
    @Schema(description="推送日志服务器ip")
    private String serverIp;

	/**
	* 响应时间
	*/
    @Schema(description="响应时间")
    private Integer responseTime;

	/**
	 * 员工姓名
	 */
	private String staffNm;

	/**
	 * 单位名称
	 */
	private String stdOrgNm;

	/**
	 * 部门名称
	 */
	private String stdDeptNm;

	/**
	 * 班组名称
	 */
	private String stdTeamNm;

	/**
     * 应用名称,集合
	 */
	@TableField(exist = false)
	private List<String> sysNames;
}
