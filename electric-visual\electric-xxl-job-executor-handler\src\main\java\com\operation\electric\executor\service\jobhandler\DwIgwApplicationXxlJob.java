package com.operation.electric.executor.service.jobhandler;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.operation.electric.yykb.api.entity.DwIgwSysDetailEntity;
import com.operation.electric.yykb.entity.DwIgwSysApplicationLogsEntity;
import com.operation.electric.yykb.mapper.DwIgwSysDetailMapper;
import com.operation.electric.yykb.service.DwIgwSysApplicationLogsService;
import com.operation.electric.yykb.service.DwIgwSysDetailService;
import com.operation.electric.yykb.service.impl.DwIgwSysDetailServiceImpl;
import com.operation.electric.yykb.utils.RedisUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * XxlJob开发示例（Bean模式）
 *
 * 开发步骤：
 *      1、任务开发：在Spring Bean实例中，开发Job方法；
 *      2、注解配置：为Job方法添加注解 "@XxlJob(value="自定义jobhandler名称", init = "JobHandler初始化方法", destroy = "JobHandler销毁方法")"，注解value值对应的是调度中心新建任务的JobHandler属性的值。
 *      3、执行日志：需要通过 "XxlJobHelper.log" 打印执行日志；
 *      4、任务结果：默认任务结果为 "成功" 状态，不需要主动设置；如有诉求，比如设置任务结果为失败，可以通过 "XxlJobHelper.handleFail/handleSuccess" 自主设置任务结果；
 *
 * <AUTHOR>
 */
@Component
public class DwIgwApplicationXxlJob {
	private static Logger logger = LoggerFactory.getLogger(DwIgwApplicationXxlJob.class);
	@Resource
	private DwIgwSysDetailMapper dwIgwSysDetailMapper;
	@Resource
	private DwIgwSysDetailServiceImpl dwIgwSysDetailService;
	@Resource
	private DwIgwSysApplicationLogsService dwIgwSysApplicationLogsService;
	/**
	 * 编写定时任务,遍历缓存,超过8分钟未更新缓存的应用，则认为应用下线，删除缓存，更新心跳表的数据,并记录应用上下线日志
	 */
	@XxlJob("dwIgwApplicationOnline")
	public void dwIgwApplicationOnline() throws Exception {
		List<DwIgwSysApplicationLogsEntity> logList = new ArrayList<>();
		//筛选出10分钟内没有进行更新的应用
		List<DwIgwSysDetailEntity> dwIgwSysDetailEntities = this.dwIgwSysDetailMapper.monitoringAllData();
		for (DwIgwSysDetailEntity dwIgwSysDetailEntity : dwIgwSysDetailEntities) {
			//自动下线应用操作
			DwIgwSysApplicationLogsEntity dwIgwSysApplicationLogsEntity = this.dwIgwSysDetailService.changeLineStatus(dwIgwSysDetailEntity, "",
					"SYSTEM", "第三方未更新心跳/应用离线", "TRUE", "prod", 0);
			logList.add(dwIgwSysApplicationLogsEntity);
		}
		logger.info("dwIgwApplicationOnline=====>执行一次完成");
		this.dwIgwSysApplicationLogsService.saveBatch(logList);
		//TODO 批量插入下线日志，并且给对应的厂家发送邮箱
	}

	//模拟发送心跳
	@XxlJob("heartSendBatch")
	public void heartSendBatch() throws Exception {
		QueryWrapper<DwIgwSysDetailEntity> queryWrapper =
				new QueryWrapper<>();
		queryWrapper.eq("application_state" , "1");
		queryWrapper.eq("sys_type" , "prod");
		queryWrapper.eq("online" , "TRUE");
		//批量发送心跳
		List<DwIgwSysDetailEntity> batchApplicationList = this.dwIgwSysDetailService.list(queryWrapper);
		logger.info("batchApplicationList::" + batchApplicationList.size());
		for (DwIgwSysDetailEntity dwIgwSysDetailEntity : batchApplicationList) {
			//直接不管物件征集
//			if("100011".equals(dwIgwSysDetailEntity.getAppId())){
//				continue;
//			}
			dwIgwSysDetailEntity.setStateUpdateTime(LocalDateTime.now());
		}
		this.dwIgwSysDetailService.saveOrUpdateBatch(batchApplicationList);
		logger.info("heartSendBatch=====>执行一次完成");
	}

	/**
	 * 2、分片广播任务
	 */
	@XxlJob("shardingJobHandler")
	public void shardingJobHandler() throws Exception {

		// 分片参数
		int shardIndex = XxlJobHelper.getShardIndex();
		int shardTotal = XxlJobHelper.getShardTotal();

		XxlJobHelper.log("分片参数：当前分片序号 = {}, 总分片数 = {}", shardIndex, shardTotal);

		// 业务逻辑
		for (int i = 0; i < shardTotal; i++) {
			if (i == shardIndex) {
				XxlJobHelper.log("第 {} 片, 命中分片开始处理", i);
			} else {
				XxlJobHelper.log("第 {} 片, 忽略", i);
			}
		}

	}


	/**
	 * 3、命令行任务
	 */
	@XxlJob("commandJobHandler")
	public void commandJobHandler() throws Exception {
		String command = XxlJobHelper.getJobParam();
		int exitValue = -1;

		BufferedReader bufferedReader = null;
		try {
			// command process
			ProcessBuilder processBuilder = new ProcessBuilder();
			processBuilder.command(command);
			processBuilder.redirectErrorStream(true);

			Process process = processBuilder.start();
			//Process process = Runtime.getRuntime().exec(command);

			BufferedInputStream bufferedInputStream = new BufferedInputStream(process.getInputStream());
			bufferedReader = new BufferedReader(new InputStreamReader(bufferedInputStream));

			// command log
			String line;
			while ((line = bufferedReader.readLine()) != null) {
				XxlJobHelper.log(line);
			}

			// command exit
			process.waitFor();
			exitValue = process.exitValue();
		} catch (Exception e) {
			XxlJobHelper.log(e);
		} finally {
			if (bufferedReader != null) {
				bufferedReader.close();
			}
		}

		if (exitValue == 0) {
			// default success
		} else {
			XxlJobHelper.handleFail("command exit value("+exitValue+") is failed");
		}

	}


	/**
	 * 4、跨平台Http任务
	 *  参数示例：
	 *      "url: http://www.baidu.com\n" +
	 *      "method: get\n" +
	 *      "data: content\n";
	 */
	@XxlJob("httpJobHandler")
	public void httpJobHandler() throws Exception {

		// param parse
		String param = XxlJobHelper.getJobParam();
		if (param==null || param.trim().length()==0) {
			XxlJobHelper.log("param["+ param +"] invalid.");

			XxlJobHelper.handleFail();
			return;
		}

		String[] httpParams = param.split("\n");
		String url = null;
		String method = null;
		String data = null;
		for (String httpParam: httpParams) {
			if (httpParam.startsWith("url:")) {
				url = httpParam.substring(httpParam.indexOf("url:") + 4).trim();
			}
			if (httpParam.startsWith("method:")) {
				method = httpParam.substring(httpParam.indexOf("method:") + 7).trim().toUpperCase();
			}
			if (httpParam.startsWith("data:")) {
				data = httpParam.substring(httpParam.indexOf("data:") + 5).trim();
			}
		}

		// param valid
		if (url==null || url.trim().length()==0) {
			XxlJobHelper.log("url["+ url +"] invalid.");

			XxlJobHelper.handleFail();
			return;
		}
		if (method==null || !Arrays.asList("GET", "POST").contains(method)) {
			XxlJobHelper.log("method["+ method +"] invalid.");

			XxlJobHelper.handleFail();
			return;
		}
		boolean isPostMethod = method.equals("POST");

		// request
		HttpURLConnection connection = null;
		BufferedReader bufferedReader = null;
		try {
			// connection
			URL realUrl = new URL(url);
			connection = (HttpURLConnection) realUrl.openConnection();

			// connection setting
			connection.setRequestMethod(method);
			connection.setDoOutput(isPostMethod);
			connection.setDoInput(true);
			connection.setUseCaches(false);
			connection.setReadTimeout(5 * 1000);
			connection.setConnectTimeout(3 * 1000);
			connection.setRequestProperty("connection", "Keep-Alive");
			connection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
			connection.setRequestProperty("Accept-Charset", "application/json;charset=UTF-8");

			// do connection
			connection.connect();

			// data
			if (isPostMethod && data!=null && data.trim().length()>0) {
				DataOutputStream dataOutputStream = new DataOutputStream(connection.getOutputStream());
				dataOutputStream.write(data.getBytes("UTF-8"));
				dataOutputStream.flush();
				dataOutputStream.close();
			}

			// valid StatusCode
			int statusCode = connection.getResponseCode();
			if (statusCode != 200) {
				throw new RuntimeException("Http Request StatusCode(" + statusCode + ") Invalid.");
			}

			// result
			bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
			StringBuilder result = new StringBuilder();
			String line;
			while ((line = bufferedReader.readLine()) != null) {
				result.append(line);
			}
			String responseMsg = result.toString();

			XxlJobHelper.log(responseMsg);

			return;
		} catch (Exception e) {
			XxlJobHelper.log(e);

			XxlJobHelper.handleFail();
			return;
		} finally {
			try {
				if (bufferedReader != null) {
					bufferedReader.close();
				}
				if (connection != null) {
					connection.disconnect();
				}
			} catch (Exception e2) {
				XxlJobHelper.log(e2);
			}
		}

	}

	/**
	 * 5、生命周期任务示例：任务初始化与销毁时，支持自定义相关逻辑；
	 */
	@XxlJob(value = "demoJobHandler2", init = "init", destroy = "destroy")
	public void demoJobHandler2() throws Exception {

	}
	public void init(){
		logger.info("init");
	}
	public void destroy(){
		logger.info("destroy");
	}


}
