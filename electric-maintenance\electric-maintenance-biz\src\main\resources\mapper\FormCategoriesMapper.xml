<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.operation.electric.yykb.mapper.FormCategoriesMapper">

  <resultMap id="formCategoriesMap" type="com.operation.electric.yykb.entity.FormCategoriesEntity">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="parentId" column="parent_id"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
  </resultMap>


	<select id="selectCategoryList" resultType="com.operation.electric.yykb.entity.FormCategoryNode">
		SELECT
			id,
			NAME,
			description,
			parent_id AS parentId,
			sort_order AS sortOrder
		FROM
			form_categories
		ORDER BY
			parent_id,
			sort_order
</select>

	<select id="selectCategoryListByParentID" resultType="com.operation.electric.yykb.entity.FormCategoryNode">
		SELECT
			id,
			NAME,
			description,
			parent_id AS parentId,
			sort_order AS sortOrder
		FROM
			form_categories WHERE parent_id ={pid}
		ORDER BY
			parent_id,
			sort_order
	</select>

	<select id="getChildById" resultType="com.operation.electric.yykb.entity.FormCategoriesEntity">
		SELECT
			*
		FROM
			form_categories
		WHERE
			1 = 1
		  AND parent_id = #{parentId}
	</select>
</mapper>