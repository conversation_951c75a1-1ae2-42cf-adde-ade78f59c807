package com.operation.electric.yykb.api.entity;

public class SuccessResponseDataSeria extends ResponseDataSeria{
    public SuccessResponseDataSeria() {
        super(true, DEFAULT_SUCCESS_CODE, "请求成功", (Object)null);
    }

    public SuccessResponseDataSeria(Object object) {
        super(true, DEFAULT_SUCCESS_CODE, "请求成功", object);
    }

    public SuccessResponseDataSeria(Integer code, String message, Object object) {
        super(true, code, message, object);
    }
}
