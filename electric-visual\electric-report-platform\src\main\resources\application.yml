server:
  port: 9095

spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        server-addr: ${NACOS_HOST:localhost}:${NACOS_PORT:18848}
      config:
        server-addr: ${NACOS_HOST:localhost}:${NACOS_PORT:18848}
  config:
    import:
      - optional:nacos:<EMAIL>@.yml
      - optional:nacos:${spring.application.name}-@profiles.active@.yml
