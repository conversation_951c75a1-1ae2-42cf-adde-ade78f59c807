<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~
  ~      Copyright (c) 2018-2025, electric All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: electric
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.operation.electric.pay.mapper.PayChannelMapper">

	<resultMap id="payChannelMap" type="com.operation.electric.pay.entity.PayChannel">
		<id property="id" column="id"/>
		<result property="appId" column="app_id"/>
		<result property="channelName" column="channel_name"/>
		<result property="channelMchId" column="channel_mch_id"/>
		<result property="channelId" column="channel_id"/>
		<result property="state" column="state"/>
		<result property="param" column="param"/>
		<result property="remark" column="remark"/>
		<result property="delFlag" column="del_flag"/>
		<result property="createTime" column="create_time"/>
		<result property="updateTime" column="update_time"/>
		<result property="tenantId" column="tenant_id"/>
	</resultMap>
</mapper>
