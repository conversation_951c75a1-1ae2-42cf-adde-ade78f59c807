package com.operation.electric.workflow.entity.dto;

import com.operation.electric.admin.api.entity.SysRole;
import com.operation.electric.admin.api.entity.SysUser;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 动态人员、组
 * <AUTHOR>
 * @createTime 2022/3/10 00:12
 */
@Data
public class WfNextDto implements Serializable {

    private String type;

    private String vars;

    private List<SysUser> userList;

    private List<SysRole> roleList;
}
