package com.operation.electric.common.api.encrypt.annotation.decrypt;

import com.operation.electric.common.api.encrypt.enums.EncryptType;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @version 2018/9/7
 * @see ApiDecrypt
 */
@Target({ ElementType.TYPE, ElementType.METHOD, ElementType.PARAMETER })
@Retention(RetentionPolicy.RUNTIME)
@Documented
@ApiDecrypt(EncryptType.RSA)
public @interface ApiDecryptRsa {

}
