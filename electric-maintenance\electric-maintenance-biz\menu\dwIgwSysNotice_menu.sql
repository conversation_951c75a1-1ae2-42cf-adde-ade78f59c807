-- 该脚本不要直接执行， 注意维护菜单的父节点ID 默认 父节点-1 , 默认租户 1

-- 菜单SQL
insert into sys_menu ( menu_id,parent_id, path, permission, menu_type, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (1737011417384, '-1', '/yykb/dwIgwSysNotice/index', '', '0', 'icon-bangzhushouji', '0', null , '8', null , 'APP首页最上方广播通知表管理', 1);

-- 菜单对应按钮SQL
insert into sys_menu ( menu_id,parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (1737011417385,1737011417384, 'yykb_dwIgwSysNotice_view', '1', null, '1',  '0', null, '0', null, 'APP首页最上方广播通知表查看', 1);

insert into sys_menu ( menu_id,parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (1737011417386,1737011417384, 'yykb_dwIgwSysNotice_add', '1', null, '1',  '0', null, '1', null, 'APP首页最上方广播通知表新增', 1);

insert into sys_menu (menu_id, parent_id, permission, menu_type, path, icon,  del_flag, create_time, sort_order, update_time, name, tenant_id)
values (1737011417387,1737011417384, 'yykb_dwIgwSysNotice_edit', '1', null, '1',  '0', null, '2', null, 'APP首页最上方广播通知表修改', 1);

insert into sys_menu (menu_id, parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (1737011417388,1737011417384, 'yykb_dwIgwSysNotice_del', '1', null, '1',  '0', null, '3', null, 'APP首页最上方广播通知表删除', 1);

insert into sys_menu ( menu_id,parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (1737011417389,1737011417384, 'yykb_dwIgwSysNotice_export', '1', null, '1',  '0', null, '3', null, '导入导出', 1);