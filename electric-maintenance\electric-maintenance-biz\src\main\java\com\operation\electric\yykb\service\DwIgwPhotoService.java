package com.operation.electric.yykb.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.operation.electric.yykb.entity.DwIgwPhotoEntity;

import java.time.LocalDateTime;
import java.util.List;

public interface DwIgwPhotoService extends IService<DwIgwPhotoEntity> {


	/**
	 * 查找前一天没有将图片和反馈内容绑定的数据
	 * @param threshold
	 * @return
	 */
	List<DwIgwPhotoEntity> findByIsLinkedFalseAndUploadTimeBefore(LocalDateTime threshold);
}