package com.operation.electric.yykb.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 应用分类
 *
 * <AUTHOR>
 * @date 2025-01-09 17:57:39
 */
@Data
@TableName("dw_igw_sys_categories")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "应用分类")
public class DwIgwSysCategoriesEntity extends Model<DwIgwSysCategoriesEntity> {


	/**
	* 分类ID
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="分类ID")
    private Integer id;

	/**
	* 分类名称
	*/
    @Schema(description="分类名称")
    private String name;

	/**
	* 分类描述
	*/
    @Schema(description="分类描述")
    private String description;

	/**
	* 父级ID
	*/
    @Schema(description="父级ID")
    private Integer parentId;

	/**
	* 所属排序
	*/
    @Schema(description="所属排序")
    private Integer sortOrder;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createdAt;

	/**
	* 更新时间
	*/
    @Schema(description="更新时间")
    private LocalDateTime updatedAt;
}