package com.operation.electric.yykb.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.operation.electric.common.core.util.R;
import com.operation.electric.common.log.annotation.SysLog;
import com.operation.electric.yykb.entity.DwIgwSysApplicationClassEntity;
import com.operation.electric.yykb.entity.DwIgwSysApplicationLogsEntity;
import com.operation.electric.yykb.service.DwIgwSysApplicationLogsService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.operation.electric.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 上下线日志记录表单
 * <AUTHOR>
 * @date 2025-01-13 18:07:46
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/dwIgwSysApplicationLogs" )
@Tag(description = "dwIgwSysApplicationLogs" , name = "上下线日志记录表单管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class DwIgwSysApplicationLogsController {

    private final  DwIgwSysApplicationLogsService dwIgwSysApplicationLogsService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param dwIgwSysApplicationLogs 上下线日志记录表单
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysApplicationLogs_view')" )
    public R getDwIgwSysApplicationLogsPage(@ParameterObject Page page, @ParameterObject DwIgwSysApplicationLogsEntity dwIgwSysApplicationLogs) {
        LambdaQueryWrapper<DwIgwSysApplicationLogsEntity> wrapper = Wrappers.lambdaQuery();
		wrapper.like(StringUtils.isNotBlank(dwIgwSysApplicationLogs.getAppId() )
				, DwIgwSysApplicationLogsEntity::getAppId , dwIgwSysApplicationLogs.getAppId())
				.like(StringUtils.isNotBlank(dwIgwSysApplicationLogs.getAppName()) , DwIgwSysApplicationLogsEntity::getAppName , dwIgwSysApplicationLogs.getAppName())
				.like(StringUtils.isNotBlank(dwIgwSysApplicationLogs.getAppEnv()) , DwIgwSysApplicationLogsEntity::getAppEnv , dwIgwSysApplicationLogs.getAppEnv())
				.like(StringUtils.isNotBlank(dwIgwSysApplicationLogs.getAppFac()) , DwIgwSysApplicationLogsEntity::getAppFac , dwIgwSysApplicationLogs.getAppFac())
				.like(StringUtils.isNotBlank(dwIgwSysApplicationLogs.getAppCreateBy()) , DwIgwSysApplicationLogsEntity::getAppCreateBy , dwIgwSysApplicationLogs.getAppCreateBy())
//				.like(StringUtils.isNotBlank(String.valueOf(dwIgwSysApplicationLogs.getAppCreateTime())) , DwIgwSysApplicationLogsEntity::getAppCreateTime , dwIgwSysApplicationLogs.getAppCreateTime())
				.like(StringUtils.isNotBlank(dwIgwSysApplicationLogs.getAppOpreationDetail() ) , DwIgwSysApplicationLogsEntity::getAppOpreationDetail , dwIgwSysApplicationLogs.getAppOpreationDetail())
				.like(StringUtils.isNotBlank(dwIgwSysApplicationLogs.getAppOpreationResult()) , DwIgwSysApplicationLogsEntity::getAppOpreationResult , dwIgwSysApplicationLogs.getAppOpreationResult());
        return R.ok(dwIgwSysApplicationLogsService.page(page, wrapper));
    }

    /**
     * 通过id查询上下线日志记录表单
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysApplicationLogs_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(dwIgwSysApplicationLogsService.getById(id));
    }

    /**
     * 新增上下线日志记录表单
     * @param dwIgwSysApplicationLogs 上下线日志记录表单
     * @return R
     */
    @Operation(summary = "新增上下线日志记录表单" , description = "新增上下线日志记录表单" )
    @SysLog("新增上下线日志记录表单" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysApplicationLogs_add')" )
    public R save(@RequestBody DwIgwSysApplicationLogsEntity dwIgwSysApplicationLogs) {
        return R.ok(dwIgwSysApplicationLogsService.save(dwIgwSysApplicationLogs));
    }

    /**
     * 修改上下线日志记录表单
     * @param dwIgwSysApplicationLogs 上下线日志记录表单
     * @return R
     */
    @Operation(summary = "修改上下线日志记录表单" , description = "修改上下线日志记录表单" )
    @SysLog("修改上下线日志记录表单" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysApplicationLogs_edit')" )
    public R updateById(@RequestBody DwIgwSysApplicationLogsEntity dwIgwSysApplicationLogs) {
        return R.ok(dwIgwSysApplicationLogsService.updateById(dwIgwSysApplicationLogs));
    }

    /**
     * 通过id删除上下线日志记录表单
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除上下线日志记录表单" , description = "通过id删除上下线日志记录表单" )
    @SysLog("通过id删除上下线日志记录表单" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysApplicationLogs_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(dwIgwSysApplicationLogsService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param dwIgwSysApplicationLogs 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysApplicationLogs_export')" )
    public List<DwIgwSysApplicationLogsEntity> export(DwIgwSysApplicationLogsEntity dwIgwSysApplicationLogs,Long[] ids) {
        return dwIgwSysApplicationLogsService.list(Wrappers.lambdaQuery(dwIgwSysApplicationLogs).in(ArrayUtil.isNotEmpty(ids), DwIgwSysApplicationLogsEntity::getId, ids));
    }
}