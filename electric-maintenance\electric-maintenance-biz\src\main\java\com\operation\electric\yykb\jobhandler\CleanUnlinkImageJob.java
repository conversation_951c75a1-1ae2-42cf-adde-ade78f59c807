package com.operation.electric.yykb.jobhandler;

import com.operation.electric.yykb.entity.DwIgwPhotoEntity;
import com.operation.electric.yykb.service.DwIgwPhotoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: zhuoxaing.peng
 * @Date: 2025-06-24 17:05
 * @description:
 * @Version: 1.0
 */
@Slf4j
public class CleanUnlinkImageJob {


	@Autowired
	private DwIgwPhotoService photoService;


	// 清理未关联图片的定时任务
	@Scheduled(cron = "0 0 0 * * ?") // 每天执行
	public void cleanUnlinkedImages() {
		LocalDateTime threshold = LocalDateTime.now().minusDays(1);
		log.info("{},cleanUnlinkedImages定时任务开始执行",LocalDateTime.now());
		List<DwIgwPhotoEntity> images = photoService.findByIsLinkedFalseAndUploadTimeBefore(threshold);
		for (DwIgwPhotoEntity image : images) {
			try {
				photoService.removeById(image.getId());
				Files.deleteIfExists(Paths.get(image.getFilePath()));
			} catch (IOException e) {
				// 记录日志
				log.error("cleanUnlinkedImages clean error", e);
			}
		}


	}
}