<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>druid monitor</title>
    <link href='css/bootstrap.min.css' rel="stylesheet" >
    <script type="text/javascript" src="js/jquery.min.js"></script>
    <style type="text/css">
      /* Override some defaults */
      html, body {
        background-color: #eee;
      }
      body {
        padding-top: 40px; 
      }
      .container {
        width: 300px;
      }

      /* The white background content wrapper */
      .container > .content {
        background-color: #fff;
        padding: 20px;
        margin: 0 -20px; 
        -webkit-border-radius: 10px 10px 10px 10px;
           -moz-border-radius: 10px 10px 10px 10px;
                border-radius: 10px 10px 10px 10px;
        -webkit-box-shadow: 0 1px 2px rgba(0,0,0,.15);
           -moz-box-shadow: 0 1px 2px rgba(0,0,0,.15);
                box-shadow: 0 1px 2px rgba(0,0,0,.15);
      }

    .login-form {
      margin-left: 65px;
    }
  
    legend {
      margin-right: -50px;
      font-weight: bold;
      color: #404040;
    }

    </style>

</head>
<body>
  	<div class="container">
      	<div class="content">
          	<div class="row">
              	<div class="login-form">
                  	<h2>Login</h2>
                  	<form id="loginForm" method="post" autocomplete="off" >
                      	<fieldset>
                      	  	<div id="alertInfo" class="alert alert-error clearfix" style="margin-bottom: 5px;width: 195px; padding: 2px 15px 2px 10px;display: none;">
                      	  		The username or password you entered is incorrect.
						  	</div>
                          	<div class="clearfix">
                              	<input type="text" placeholder="用户名" name="loginUsername" autofocus="autofocus">
                          	</div>
                          	<div class="clearfix">
                              	<input type="password" placeholder="密码" name="loginPassword">
                          	</div>
                          	<button id="loginBtn" class="btn btn-primary" type="button">Sign in</button>
                          	<button class="btn" type="reset">Reset</button>
                      	</fieldset>
                  	</form>
              	</div>
          	</div>
      	</div>
  	</div> <!-- /container -->
	<script type="text/javascript">
  		$.namespace("druid.login");
   		druid.login = function () {  
   			return  {
	  			login : function() {
	  				$.ajax({
	  				  type: 'POST',
	  				  url: "submitLogin",
	  				  data: $("#loginForm").serialize(),
	  				  success: function(data) {
	  					if("success" == data)
	  						location.href = "index.html";
	  					else {
	  						$("#alertInfo").show();
	  						$("#loginForm")[0].reset();
							$("input[name=loginUsername]").focus();
	  					}
	  				  },
	  				  dataType: "text"
	  				});
	  			},
				unamecr : function(e) {
					if(e.which == 13) { // enter key event
						$("input[name=loginPassword]").focus();
					}
				},
				upasscr : function(e) {
					if(e.which == 13) { // enter key event
						$("#loginBtn").click();
					}
				}
   			}
  		}();

  		$(document).ready(function() {
  			$("#loginBtn").click(druid.login.login);
  			$("input[name=loginUsername]").keypress(druid.login.unamecr);
  			$("input[name=loginPassword]").keypress(druid.login.upasscr);
 		});
  	</script>
</body>
</html>