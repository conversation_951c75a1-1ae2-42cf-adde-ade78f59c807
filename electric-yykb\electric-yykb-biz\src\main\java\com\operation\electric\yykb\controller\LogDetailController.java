package com.operation.electric.yykb.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.operation.electric.common.core.util.R;
import com.operation.electric.common.log.annotation.SysLog;
import com.operation.electric.common.security.annotation.Inner;
import com.operation.electric.yykb.api.dto.LogDetailDTO;
import com.operation.electric.yykb.entity.LogDetailEntity;
import com.operation.electric.yykb.service.LogDetailService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.operation.electric.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 日志系统，系统使用详情日志记录信息
 *
 * <AUTHOR>
 * @date 2025-01-02 18:14:48
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/logDetail")
@Tag(description = "logDetail", name = "日志系统，系统使用详情日志记录信息管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class LogDetailController {

    private final LogDetailService logDetailService;

    /**
     * 分页查询
     *
     * @param page      分页对象
     * @param logDetail 日志系统，系统使用详情日志记录信息
     * @return
     */
    @Inner(value = false)
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
//    @PreAuthorize("@pms.hasPermission('electric_logDetail_view')" )
    public R getLogDetailPage(@ParameterObject Page page, @ParameterObject LogDetailDTO logDetailDTO) {
        IPage<LogDetailDTO> logDetailDTOIPage =  logDetailService.selectLogDetailAndLogStaffInSysName(page,logDetailDTO);
        return R.ok(logDetailDTOIPage);
    }


    /**
     * 通过id查询日志系统，系统使用详情日志记录信息
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('electric_logDetail_view')")
    public R getById(@PathVariable("id") String id) {
        return R.ok(logDetailService.getById(id));
    }

    /**
     * 新增日志系统，系统使用详情日志记录信息
     *
     * @param logDetail 日志系统，系统使用详情日志记录信息
     * @return R
     */
    @Operation(summary = "新增日志系统，系统使用详情日志记录信息", description = "新增日志系统，系统使用详情日志记录信息")
    @SysLog("新增日志系统，系统使用详情日志记录信息")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('electric_logDetail_add')")
    public R save(@RequestBody LogDetailEntity logDetail) {
        return R.ok(logDetailService.save(logDetail));
    }

    /**
     * 修改日志系统，系统使用详情日志记录信息
     *
     * @param logDetail 日志系统，系统使用详情日志记录信息
     * @return R
     */
    @Operation(summary = "修改日志系统，系统使用详情日志记录信息", description = "修改日志系统，系统使用详情日志记录信息")
    @SysLog("修改日志系统，系统使用详情日志记录信息")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('electric_logDetail_edit')")
    public R updateById(@RequestBody LogDetailEntity logDetail) {
        return R.ok(logDetailService.updateById(logDetail));
    }

    /**
     * 通过id删除日志系统，系统使用详情日志记录信息
     *
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除日志系统，系统使用详情日志记录信息", description = "通过id删除日志系统，系统使用详情日志记录信息")
    @SysLog("通过id删除日志系统，系统使用详情日志记录信息")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('electric_logDetail_del')")
    public R removeById(@RequestBody String[] ids) {
        return R.ok(logDetailService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     *
     * @param logDetail 查询条件
     * @param ids       导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('electric_logDetail_export')")
    public List<LogDetailEntity> export(LogDetailEntity logDetail, String[] ids) {
        return logDetailService.list(Wrappers.lambdaQuery(logDetail).in(ArrayUtil.isNotEmpty(ids), LogDetailEntity::getId, ids));
    }
}
