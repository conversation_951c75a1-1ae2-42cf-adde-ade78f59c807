package com.operation.electric.workflow.controller;

import com.operation.electric.common.core.util.R;
import com.operation.electric.common.security.service.ElectricUser;
import com.operation.electric.common.security.util.SecurityUtils;

/**
 * web层通用数据处理
 *
 * <AUTHOR> Li
 */
public class BaseController {

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected R<Void> toAjax(int rows) {
        return rows > 0 ? R.ok() : R.failed();
    }

    /**
     * 响应返回结果
     *
     * @param result 结果
     * @return 操作结果
     */
    protected R<Void> toAjax(boolean result) {
        return result ? R.ok() : R.failed();
    }

    /**
     * 获取用户缓存信息
     */
    public ElectricUser getLoginUser() {
        return SecurityUtils.getUser();
    }

    /**
     * 获取登录用户id
     */
    public Long getUserId() {
        return SecurityUtils.getUser().getId();
    }

    /**
     * 获取登录部门id
     */
    public Long getDeptId() {
        return SecurityUtils.getUser().getDeptId();
    }

    /**
     * 获取登录用户名
     */
    public String getUsername() {
        return SecurityUtils.getUser().getUsername();
    }
}
