/*
 *
 *      Copyright (c) 2018-2025, electric All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: electric
 *
 */

package com.operation.electric.workflow;

import com.operation.electric.common.feign.annotation.EnableElectricFeignClients;
import com.operation.electric.common.security.annotation.EnableElectricResourceServer;
import com.operation.electric.common.swagger.annotation.EnableOpenApi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * <AUTHOR>
 * @date 2018年06月21日
 * <p>
 * 用户统一管理系统
 */
@EnableOpenApi("workflow")
@EnableElectricFeignClients
@EnableElectricResourceServer
@EnableDiscoveryClient
@SpringBootApplication
@MapperScan({"com.operation.electric.workflow.mapper","com.operation.electric.workflow.service.impl"})
public class ElectricWorkFlowApplication {

	public static void main(String[] args) {
		SpringApplication.run(ElectricWorkFlowApplication.class, args);
	}

}
