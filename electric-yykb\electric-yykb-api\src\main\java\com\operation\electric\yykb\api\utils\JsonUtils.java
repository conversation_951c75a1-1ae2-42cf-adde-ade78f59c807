package com.operation.electric.yykb.api.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: ${file_name}
 * @Package ${package_name}
 * @Description: TODO
 * @date ${date} ${time}
 */
@Slf4j
public class JsonUtils {

    /**
     * Logger for this class
     */
    private final static ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.configure(JsonParser.Feature.ALLOW_COMMENTS, true);
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    private JsonUtils() {
    }

    public static ObjectMapper getObjectMapper() {
        return objectMapper;
    }

    public static String encode(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (IOException e) {
            log.error("encode(Object)", e); //$NON-NLS-1$
        }
        return null;
    }

    /**
     * 将json string反序列化成对象
     */
    public static <T> T decode(String json, Class<T> valueType) {
        try {
            return objectMapper.readValue(json, valueType);
        } catch (IOException e) {
            log.error("decode(String, Class<T>)", e);
        }
        return null;
    }

    /**
     * 将json array反序列化为对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T decode(String json, TypeReference<T> typeReference) {
        try {
            return (T) objectMapper.readValue(json, typeReference);
        } catch (IOException e) {
            log.error("decode(String, JsonTypeReference<T>)", e);
        }
        return null;
    }

    /**
     * 将json string反序列化成对象
     */
    public static <T> T decode(InputStream input, Class<T> valueType) {
        try {
            return objectMapper.readValue(input, valueType);
        } catch (IOException e) {
            log.error("decode(InputStream, Class<T>)", e);
        }
        return null;

    }

    /**
     * 将json array反序列化为对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T decode(InputStream input, TypeReference<T> typeReference) {
        try {
            return (T) objectMapper.readValue(input, typeReference);
        } catch (IOException e) {
            log.error("decode(InputStream, JsonTypeReference<T>)", e);
        }
        return null;
    }

    /**
     * 将json string反序列化成对象
     */
    public static <T> T decode(byte[] input, Class<T> valueType) {
        try {
            return objectMapper.readValue(input, valueType);
        } catch (IOException e) {
            log.error("decode(byte[], Class<T>)", e);
        }
        return null;

    }

    /**
     * 将json array反序列化为对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T decode(byte[] b, TypeReference<T> typeReference) {
        try {
            return (T) objectMapper.readValue(b, typeReference);
        } catch (IOException e) {
            log.error("decode(byte[], JsonTypeReference<T>)", e);
        }
        return null;
    }

    public static <T> T convert(Object obj, TypeReference<T> typeReference) {
        return objectMapper.convertValue(obj, typeReference);
    }

    public static <T> T convert(Object obj, Class<T> type) {
        return objectMapper.convertValue(obj, type);
    }
}
