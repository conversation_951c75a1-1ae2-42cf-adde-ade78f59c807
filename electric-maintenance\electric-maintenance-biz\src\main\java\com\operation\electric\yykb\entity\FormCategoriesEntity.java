package com.operation.electric.yykb.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 应用分类管理模块
 *
 * <AUTHOR>
 * @date 2024-12-13 11:12:34
 */
@Data
@TableName("form_categories")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "应用分类管理模块")
public class FormCategoriesEntity extends Model<FormCategoriesEntity> {


	/**
	* 类别ID
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="类别ID")
    private Integer id;

	/**
	* 类别名称
	*/
    @Schema(description="类别名称")
    private String name;

	/**
	* 类别描述
	*/
    @Schema(description="类别描述")
    private String description;

	/**
	* 父级ID
	*/
    @Schema(description="父级ID")
    private Integer parentId;

	/**
	* 所属排序
	*/
    @Schema(description="所属排序")
    private Integer sortOrder;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private LocalDateTime createdAt;

	/**
	* 更新人
	*/
    @Schema(description="更新人")
    private LocalDateTime updatedAt;
}