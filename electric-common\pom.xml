<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~      Copyright (c) 2018-2025, electric All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: electric
  ~
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.operation</groupId>
		<artifactId>electric</artifactId>
		<version>5.3.0</version>
	</parent>

	<artifactId>electric-common</artifactId>
	<packaging>pom</packaging>

	<description>electric 公共聚合模块</description>

	<modules>
		<module>electric-common-audit</module>
		<module>electric-common-bom</module>
		<module>electric-common-core</module>
		<module>electric-common-data</module>
		<module>electric-common-datasource</module>
		<module>electric-common-excel</module>
		<module>electric-common-encrypt-api</module>
		<module>electric-common-feign</module>
		<module>electric-common-gateway</module>
		<module>electric-common-gray</module>
		<module>electric-common-idempotent</module>
		<module>electric-common-job</module>
		<module>electric-common-log</module>
		<module>electric-common-oss</module>
		<module>electric-common-seata</module>
		<module>electric-common-security</module>
		<module>electric-common-sentinel</module>
		<module>electric-common-sequence</module>
		<module>electric-common-swagger</module>
		<module>electric-common-websocket</module>
		<module>electric-common-xss</module>
	</modules>
</project>
