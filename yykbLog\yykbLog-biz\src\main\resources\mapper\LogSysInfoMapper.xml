<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.operation.electric.yykbLog.mapper.LogSysInfoMapper">

  <resultMap id="logSysInfoMap" type="com.operation.electric.yykbLog.entity.LogSysInfoEntity">
        <id property="id" column="id"/>
        <result property="sysName" column="sys_name"/>
        <result property="salt" column="salt"/>
        <result property="createdAt" column="created_at"/>
        <result property="creatorId" column="creator_id"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="updatorId" column="updator_id"/>
        <result property="status" column="status"/>
        <result property="appKey" column="app_key"/>
        <result property="appSecret" column="app_secret"/>
        <result property="sysId" column="sys_id"/>
  </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id AS "id",sys_id as "sysId", sys_name AS "sysName", salt AS "salt", created_at AS "createdAt", creator_id AS "creatorId", updated_at AS "updatedAt", updator_id AS "updatorId", status AS "status", app_key AS "appKey", app_secret AS "appSecret"
    </sql>

    <select id="selectByAppIdAndSysId" resultType="com.operation.electric.yykbLog.entity.LogSysInfoEntity">
        select
        <include refid="Base_Column_List"/>
        from log_sys_info where app_key = #{appKey} and sys_id = #{sysId}
    </select>
</mapper>