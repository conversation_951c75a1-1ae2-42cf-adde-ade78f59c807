<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~      Copyright (c) 2018-2025, electric All rights reserved.
  ~
  ~   Redistribution and use in source and binary forms, with or without
  ~   modification, are permitted provided that the following conditions are met:
  ~
  ~   Redistributions of source code must retain the above copyright notice,
  ~   this list of conditions and the following disclaimer.
  ~   Redistributions in binary form must reproduce the above copyright
  ~   notice, this list of conditions and the following disclaimer in the
  ~   documentation and/or other materials provided with the distribution.
  ~   Neither the name of the pig4cloud.com developer nor the names of its
  ~   contributors may be used to endorse or promote products derived from
  ~   this software without specific prior written permission.
  ~   Author: electric
  ~
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.operation</groupId>
		<artifactId>electric-common</artifactId>
		<version>5.3.0</version>
	</parent>

	<artifactId>electric-common-seata</artifactId>
	<packaging>jar</packaging>
	<description>electric 分布式事务处理模块</description>

	<dependencies>
		<!--核心依赖-->
		<dependency>
			<groupId>com.operation</groupId>
			<artifactId>electric-common-core</artifactId>
		</dependency>
		<!-- seata-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-seata</artifactId>
		</dependency>
	</dependencies>

</project>
