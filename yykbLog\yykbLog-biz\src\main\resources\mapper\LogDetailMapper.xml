<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.operation.electric.yykbLog.mapper.LogDetailMapper">

  <resultMap id="logDetailMap" type="com.operation.electric.yykbLog.entity.LogDetailEntity">
        <id property="id" column="id"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="staffNo" column="staff_no"/>
        <result property="timestamp" column="timestamp"/>
        <result property="operationClass1" column="operation_class1"/>
        <result property="operationClass2" column="operation_class2"/>
        <result property="operationClass3" column="operation_class3"/>
        <result property="operationClass4" column="operation_class4"/>
        <result property="operationClass5" column="operation_class5"/>
        <result property="ip" column="ip"/>
        <result property="sysId" column="sys_id"/>
        <result property="sysName" column="sys_name"/>
        <result property="sysVersion" column="sys_version"/>
        <result property="onlineTime" column="online_time"/>
        <result property="vipFlag" column="vip_flag"/>
  </resultMap>
</mapper>