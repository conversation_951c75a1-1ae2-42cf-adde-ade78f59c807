package com.operation.electric.yykb.service.impl;

import cn.hutool.core.date.DateTime;
import cn.stylefeng.roses.core.util.ToolUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.operation.electric.common.core.util.R;
import com.operation.electric.yykb.api.entity.DwIgwSysDetailEntity;
import com.operation.electric.yykb.api.utils.NginxConfigGenerator;
import com.operation.electric.yykb.entity.DwIgwSysApplicationLogsEntity;
import com.operation.electric.yykb.mapper.DwIgwSysDetailMapper;
import com.operation.electric.yykb.service.DwIgwSysApplicationLogsService;
import com.operation.electric.yykb.service.DwIgwSysDetailService;
import com.operation.electric.yykb.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * igw应用列表
 *
 * <AUTHOR>
 * @date 2024-03-25 15:30:42
 */
@Slf4j
@Service
public class DwIgwSysDetailServiceImpl extends ServiceImpl<DwIgwSysDetailMapper, DwIgwSysDetailEntity> implements DwIgwSysDetailService {
	@Autowired
	DwIgwSysApplicationLogsService dwIgwSysApplicationLogsService;
	@Autowired
	private RedisUtils redisUtils;
//	@Autowired
//	private DwIgwSysDetailService dwIgwSysDetailService;

    @Override
    @Cacheable(value = "dwSysList#86400", key = "'sysMenu' + '-' + #sysType", sync = true)
    public List<DwIgwSysDetailEntity> getSysList(String sysType) {
        QueryWrapper<DwIgwSysDetailEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(DwIgwSysDetailEntity::getSysType, sysType).orderByAsc(DwIgwSysDetailEntity::getSortNm).last("limit 8");
        List<DwIgwSysDetailEntity> dwIgwSysDetailEntities = baseMapper.selectList(qw);
        return dwIgwSysDetailEntities;
    }

    @Override
    @Cacheable(value = "dwSysListAll#86400", key = "#param.getSysType()+'-'+#param.getSysClass()", sync = true)
    public List<DwIgwSysDetailEntity> getSysListAll(DwIgwSysDetailEntity param) {
        if (ToolUtil.isEmpty(param.getSysType())) {
            param.setSysType("prod");
        }
        QueryWrapper<DwIgwSysDetailEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(DwIgwSysDetailEntity::getSysType, param.getSysType()).orderByAsc(DwIgwSysDetailEntity::getSortNm);
        if (StringUtils.isNotBlank(param.getSysClass()) && !"null".equals(param.getSysClass())) {
            qw.lambda().eq(DwIgwSysDetailEntity::getSysClass, param.getSysClass());
        }
        List<DwIgwSysDetailEntity> dwIgwSysDetailEntities = baseMapper.selectList(qw);
        return dwIgwSysDetailEntities;
    }

    @Override
    public void operationConfigurationFile() {
        String str = "";
        List<DwIgwSysDetailEntity> dwIgwSysDetailEntities = this.baseMapper.selectList(Wrappers.emptyWrapper());
        for (DwIgwSysDetailEntity dwIgwSysDetailEntity : dwIgwSysDetailEntities) {
            if (StringUtils.isNotBlank(dwIgwSysDetailEntity.getConfigurationText())) {
                str += dwIgwSysDetailEntity.getConfigurationText() + "\n";
            }
        }
        // 目标文件路径
        String filePath = "/etc/nginx/conf.d/locations.conf";
        NginxConfigGenerator.createNginxConfigFile(filePath, str);
        NginxConfigGenerator.restartNginx();
    }

    @Override
    public R monitor(String appId) {
    	//先要检测一下是不是已经上架的应用 未上架应用直接进行拦截
		List<DwIgwSysDetailEntity> detailByAppID = this.findDetailByAppID(appId);
		if(null == detailByAppID || detailByAppID.size() == 0){
			return R.failed("请确认应用是否已上线");
		}
		Integer monitor = baseMapper.monitor(appId);
		if(monitor.intValue() > 0){
			return R.ok( "心跳正常");
		}
		return R.failed("调用失败！");
    }

    @Override
    public String monitoringAllData() {
        List<DwIgwSysDetailEntity> dwIgwSysDetailEntities = baseMapper.monitoringAllData();
        for (DwIgwSysDetailEntity dwIgwSysDetailEntity : dwIgwSysDetailEntities) {
            baseMapper.monitorError(dwIgwSysDetailEntity.getAppId());
        }
        return "成功";
    }

    @Override
    public Integer quantitativeRestriction() {
        //统计生产环境常用的应用
        Integer count = this.baseMapper.quantitativeRestriction();
        if (count >= 8) {
            return 0;
        }
        return 1;
    }

    @Override
    public List<String> findAll() {
        return this.baseMapper.findAll();
    }

	@Override
	public String dwIgwSysApplicationLineStatusChange(DwIgwSysDetailEntity detailEntity) {
    	//应用上线+日志记录
		if(StringUtils.isEmpty(detailEntity.getOnline()) || StringUtils.isEmpty(detailEntity.getXh())){
					return "非法操作";
		}
		//根据Xh查询一下该应用的目前情况，避免误操作
		QueryWrapper<DwIgwSysDetailEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("xh",detailEntity.getXh());
		DwIgwSysDetailEntity queryDwIgwSysDetailEntity = this.getOne(queryWrapper);

		if(ObjectUtils.isEmpty(queryDwIgwSysDetailEntity)){
			return "非正规途径操作";
		}
		DwIgwSysApplicationLogsEntity dwIgwSysApplicationLogsEntity = null;
		switch (detailEntity.getOnline()){
			case "TRUE":
				dwIgwSysApplicationLogsEntity = this.changeLineStatus(queryDwIgwSysDetailEntity , detailEntity.getOnline()
						, detailEntity.getCreateBy() , "上线" , "TRUE" , "prod" , 1);
				break;
			case "FALSE":
				dwIgwSysApplicationLogsEntity = this.changeLineStatus(queryDwIgwSysDetailEntity , detailEntity.getOnline()
						, detailEntity.getCreateBy() , "下线" , "FALSE" , "test" , 0);
					break;
			default:
				dwIgwSysApplicationLogsEntity = this.changeLineStatus(queryDwIgwSysDetailEntity , detailEntity.getOnline()
						, detailEntity.getCreateBy() , "非法上下线" , "/" , "/" , 0);
				break;
		}
		//插入日志
		this.dwIgwSysApplicationLogsService.save(dwIgwSysApplicationLogsEntity);
//		redisUtils.setCacheObject("heart:" + heartMessage.getAppKey(), one, 5, java.util.concurrent.TimeUnit.MINUTES);
		return dwIgwSysApplicationLogsEntity.getAppOpreationResult();
	}

	@Override
	public List<DwIgwSysDetailEntity> findDetailByAppID(String appId) {
		return this.baseMapper.findDetailByAppID((appId));
	}

	/**
	 *
	 * @param queryDwIgwSysDetailEntity 根据主键查询出来的对象
	 * @param preOptionLine 前端传来的操作指令 TRUE or FALSE （上线或下线）
	 * @param createBy 操作热
	 * @param lineStatus 上线OR下线操作描述
	 * @param onLineStatus 在线状态描述（TRUE or FALSE）
	 * @param sysTypeStatus 环境更改
	 * @return
	 */
	public DwIgwSysApplicationLogsEntity changeLineStatus(DwIgwSysDetailEntity queryDwIgwSysDetailEntity , String preOptionLine , String  createBy, String lineStatus ,
								 String onLineStatus , String sysTypeStatus , Integer applicationStatus){
		DwIgwSysApplicationLogsEntity dwIgwSysApplicationLogsEntity = new DwIgwSysApplicationLogsEntity();
		if(null != queryDwIgwSysDetailEntity.getOnline() && queryDwIgwSysDetailEntity.getOnline().equals(preOptionLine)){
			throw new RuntimeException("重复"+lineStatus+"告警！！！！！");
		}
		dwIgwSysApplicationLogsEntity.setAppId(queryDwIgwSysDetailEntity.getXh());
		dwIgwSysApplicationLogsEntity.setAppName(queryDwIgwSysDetailEntity.getSysName());
		dwIgwSysApplicationLogsEntity.setAppEnv(queryDwIgwSysDetailEntity.getSysType());
		dwIgwSysApplicationLogsEntity.setAppFac(queryDwIgwSysDetailEntity.getSysFac());
		dwIgwSysApplicationLogsEntity.setAppCreateBy(createBy);
		dwIgwSysApplicationLogsEntity.setAppCreateTime(LocalDateTime.now());
		dwIgwSysApplicationLogsEntity.setAppOpreationDetail("系统"+lineStatus);
		dwIgwSysApplicationLogsEntity.setAppOpreationResult(lineStatus+"成功");
		boolean onLineResult = false;
		if("TRUE".equals(onLineStatus) || "FALSE".equals(onLineStatus) ){
			queryDwIgwSysDetailEntity.setOnline(onLineStatus);
			queryDwIgwSysDetailEntity.setSysType(sysTypeStatus);
			queryDwIgwSysDetailEntity.setApplicationState(applicationStatus);
			//如果是在线应用
			if("TRUE".equals(onLineStatus)){
				queryDwIgwSysDetailEntity.setStateUpdateTime(LocalDateTime.now());
			}
			onLineResult =  this.updateById(queryDwIgwSysDetailEntity);
		}
		//应用上下架/上下线情况
		if(!onLineResult){
			dwIgwSysApplicationLogsEntity.setAppOpreationResult(lineStatus+"失败");
		}
		return dwIgwSysApplicationLogsEntity;
	}
}