<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.operation.electric.yykb.mapper.LogDetailMapper">
	<resultMap id="logDetailMap" type="com.operation.electric.yykb.api.entity.LogDetailEntity">
		<id property="id" column="id"/>
		<result property="createdAt" column="created_at"/>
		<result property="updatedAt" column="updated_at"/>
		<result property="staffNo" column="staff_no"/>
		<result property="timestamp" column="timestamp"/>
		<result property="operationClass1" column="operation_class1"/>
		<result property="operationClass2" column="operation_class2"/>
		<result property="operationClass3" column="operation_class3"/>
		<result property="operationClass4" column="operation_class4"/>
		<result property="operationClass5" column="operation_class5"/>
		<result property="ip" column="ip"/>
		<result property="sysId" column="sys_id"/>
		<result property="sysName" column="sys_name"/>
		<result property="sysVersion" column="sys_version"/>
		<result property="onlineTime" column="online_time"/>
		<result property="vipFlag" column="vip_flag"/>
		<result property="serverIp" column="server_ip"/>
		<result property="responseTime" column="response_time"/>
	</resultMap>

	<resultMap id="logDetailDTOMap" type="com.operation.electric.yykb.api.dto.LogDetailDTO">
		<id property="id" column="id"/>
		<result property="createdAt" column="created_at"/>
		<result property="updatedAt" column="updated_at"/>
		<result property="staffNo" column="staff_no"/>
		<result property="timestamp" column="timestamp"/>
		<result property="operationClass1" column="operation_class1"/>
		<result property="operationClass2" column="operation_class2"/>
		<result property="operationClass3" column="operation_class3"/>
		<result property="operationClass4" column="operation_class4"/>
		<result property="operationClass5" column="operation_class5"/>
		<result property="ip" column="ip"/>
		<result property="sysId" column="sys_id"/>
		<result property="sysName" column="sys_name"/>
		<result property="sysVersion" column="sys_version"/>
		<result property="onlineTime" column="online_time"/>
		<result property="vipFlag" column="vip_flag"/>
		<result property="serverIp" column="server_ip"/>
		<result property="responseTime" column="response_time"/>
		<result property="staffNm" column="staff_nm"/>
		<result property="stdOrgNm" column="std_org_nm"/>
		<result property="stdDeptNm" column="std_dept_nm"/>
		<result property="stdTeamNm" column="std_team_nm"/>
	</resultMap>

	<select id="selectLogDetailAndLogStaffInSysName" resultMap="logDetailDTOMap">
		SELECT
		l1.*,
		l2.staff_nm,
		l2.std_org_nm,
		l2.std_dept_nm,
		l2.std_team_nm
		FROM
		log_detail l1
		 JOIN log_staff l2 ON l1.staff_no = l2.staff_no
		where 1=1
		<!--        <if test="params.sysNames != null">-->
		<!--            AND l1.operation_class1 in-->
		<!--            <foreach collection="params.sysNames" index="index" item="sysName" open="(" separator="," close=")">-->
		<!--                #{sysName}-->
		<!--            </foreach>-->
		<!--        </if>-->
		<if test="params.ip != null">
			AND l1.ip like CONCAT('%',#{params.ip},'%')
		</if>

		<if test="params.operationClass1 != null and params.operationClass1.trim() != ''">
			AND l1.operation_class1 like CONCAT('%',#{params.operationClass1},'%')
		</if>
		<if test="params.operationClass2 != null and params.operationClass2.trim() != ''">
			AND l1.operation_class2 like CONCAT('%',#{params.operationClass2},'%')
		</if>
		<if test="params.operationClass3 != null and params.operationClass3.trim() != ''">
			AND l1.operation_class3 like CONCAT('%',#{params.operationClass3},'%')
		</if>
		<if test="params.operationClass4 != null and params.operationClass4.trim() != ''">
			AND l1.operation_class4 like CONCAT('%',#{params.operationClass4},'%')
		</if>
		<if test="params.operationClass5 != null and params.operationClass5.trim() != ''">
			AND l1.operation_class5 like CONCAT('%',#{params.operationClass5},'%')
		</if>
		<if test="params.staffNo != null and params.staffNo.trim() != ''">
			AND l1.staff_no like CONCAT('%',#{params.staffNo},'%')
		</if>
		<if test="params.stdDeptNm != null and params.stdDeptNm.trim() != ''">
			AND l2.std_dept_nm like CONCAT('%',#{params.stdDeptNm},'%')
		</if>
		<if test="params.stdOrgNm != null and params.stdOrgNm.trim() != ''">
			AND l2.std_org_nm like CONCAT('%',#{params.stdOrgNm},'%')
		</if>
		<if test="params.stdTeamNm != null and params.stdTeamNm.trim() != ''">
			AND l2.std_team_nm like CONCAT('%',#{params.stdTeamNm},'%')
		</if>
		<if test="params.sysName != null and params.sysName.trim() != '' ">
			AND l1.sys_name like CONCAT('%',#{params.sysName},'%')
		</if>
		<if test="params.sysVersion != null">
			AND l1.sys_version =#{params.sysVersion}
		</if>
		<if test="params.onlineTime != null">
			AND l1.online_time =#{params.onlineTime}
		</if>
		<if test="params.serverIp != null">
			AND l1.server_ip =#{params.serverIp}
		</if>
		<if test="params.responseTime != null">
			AND l1.response_time =#{params.responseTime}
		</if>
		<if test="params.staffNm != null and params.staffNm.trim() != '' ">
			AND l2.staff_nm =#{params.staffNm}
		</if>

		order by l1.created_at desc
	</select>
</mapper>
