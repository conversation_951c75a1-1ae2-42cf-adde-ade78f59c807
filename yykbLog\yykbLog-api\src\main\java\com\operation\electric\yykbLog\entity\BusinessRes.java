package com.operation.electric.yykbLog.entity;

import lombok.*;

import java.io.Serializable;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class BusinessRes<T extends Serializable> implements Serializable {
    private Integer code;

    private String message;

    private T data;

    @Override
    public String toString() {
        return "BusinessRes{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }
}
