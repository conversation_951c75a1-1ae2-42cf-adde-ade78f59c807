<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.operation.electric.app.mapper.AppArticleCollectMapper">

  <resultMap id="appArticleCollectMap" type="com.operation.electric.app.api.entity.AppArticleCollectEntity">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="articleId" column="article_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
  </resultMap>
</mapper>
