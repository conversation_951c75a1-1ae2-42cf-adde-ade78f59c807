package com.operation.electric.yykbLog.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 重要用户
 *
 * <AUTHOR>
 * @date 2024-03-21 17:24:45
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("log_important_staff")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "重要用户")
public class LogImportantStaffEntity extends Model<LogImportantStaffEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键")
    private Long id;

	/**
	* OA账号
	*/
    @Schema(description="OA账号")
    private String staffNo;

	/**
	* 用户名称
	*/
    @Schema(description="用户名称")
    private String staffName;

	/**
	* 单位
	*/
    @Schema(description="单位")
    private String staffUnit;
}