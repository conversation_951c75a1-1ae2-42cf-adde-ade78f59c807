package com.operation.electric.yykb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.operation.electric.yykb.entity.LogDetailEntity;
import com.operation.electric.yykb.entity.TreeNode;
import com.operation.electric.yykb.mapper.LogDetailMapper;
import com.operation.electric.yykb.service.LogDetailService;
import com.operation.electric.yykb.service.LogStaffService;
import com.operation.electric.yykb.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 第三方系统日志
 *
 * <AUTHOR>
 * @date 2024-11-06 11:15:41
 */
@Service
public class LogDetailServiceImpl extends ServiceImpl<LogDetailMapper, LogDetailEntity> implements LogDetailService {

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private LogDetailMapper logDetailMapper;

    @Autowired
    private LogStaffService logStaffService;

    @Override
    public List<TreeNode> getOrgMessage() {
        List<TreeNode> orgTree = redisUtils.getCacheList("orgTree");
        if (orgTree != null){
            return orgTree;
        }
        List<TreeNode> list = logStaffService.selectOrgOne();
        if (list.size() != 0){
            for (TreeNode treeNode : list) {
                List<TreeNode> treeNodes = logStaffService.selectOrgTwo(treeNode.getOrgNo());
                if (treeNodes.size() != 0){
                    treeNodes.forEach(treeNode1 -> {
                        List<TreeNode> treeNodes1 = logStaffService.selectOrgThree(treeNode1.getOrgNo());
                        if (treeNodes1.size() != 0){
                            treeNodes1.forEach(treeNode2 -> {
                                List<TreeNode> treeNodes2 = logStaffService.selectOrgFour(treeNode2.getOrgNo());
                                treeNode2.setChildNode(treeNodes2);
                            });
                        }
                        treeNode1.setChildNode(treeNodes1);
                    });
                }
                treeNode.setChildNode(treeNodes);
            }
        }
        redisUtils.setCacheObject("orgTree",list,12, TimeUnit.HOURS);
        return list;
    }

    @Override
    public List<String> selectStaffByOrgNo(String orgId) {
        return logDetailMapper.selectStaffByOrgNo(orgId);
    }

    @Override
    public List<String> getSystemInfo() {
        List<String> list = logDetailMapper.selectSystemInfoList();
        return list;
    }
}
