package com.xxl.job.admin.jobhandler;

import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Name:ticket-center-api
 * User: YiJiangHua
 * Date: 2025/1/14
 * Time: 17:19
 */
@Component
public class IgwSysDetailXxlJob {
	private static Logger logger = LoggerFactory.getLogger(IgwSysDetailXxlJob.class);

	@XxlJob("checkApplicationHeartStatus")
	public void checkApplicationHeartStatus(){
		System.out.println("请问我刷新了吗");
	}
}
