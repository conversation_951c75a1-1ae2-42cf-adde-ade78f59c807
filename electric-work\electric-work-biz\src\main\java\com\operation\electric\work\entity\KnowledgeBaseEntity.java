package com.operation.electric.work.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 知识库表(未入库的知识库文档)
 *
 * <AUTHOR>
 * @date 2024-03-12 16:02:18
 */
@Data
@TableName("knowledge_base")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "知识库表(未入库的知识库文档)")
public class KnowledgeBaseEntity extends Model<KnowledgeBaseEntity> {


	/**
	* 主键id
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键id")
    private Long id;

	/**
	* 文件名(关联electricx的sys_file表)
	*/
    @Schema(description="文件名(关联electricx的sys_file表)")
    private String fileName;

	/**
	* 状态(0:未入库  1:已入库)
	*/
    @Schema(description="状态(0:未入库  1:已入库)")
    private Long status;

	/**
	* 文件原始名称
	*/
    @Schema(description="文件原始名称")
    private String originalName;

	/**
	 * 文件url
	 */
	@Schema(description="文件原始名称")
	private String url;

	/**
	 * 单段文本最大长度
	 */
	@Schema(description="单段文本最大长度")
	private Long maxLength;

	/**
	 * 相邻文本重合长度
	 */
	@Schema(description="相邻文本重合长度")
	private Long coincidentLength;

	/**
	 * 开启中文标题加强
	 */
	@Schema(description="开启中文标题加强")
	private Boolean zhTitleEnhance;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 上传时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="上传时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 删除标志
	*/
    @TableLogic
		@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标志")
    private String delFlag;

	/**
	* 所属租户
	*/
    @Schema(description="所属租户")
    private Long tenantId;
}
