package com.operation.electric.yykbLog.logRest.v1.vm;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 应用系统档案
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
@TableName("log_sys_info")
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@Data
public class AppInfoVm implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 系统名称
     */
    @TableField("sys_name")
    private String sysName;

    @TableField("sys_id")
    private String sysId;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private Date createdAt;


    /**
     * 更新时间
     */
    @TableField("updated_at")
    private Date updatedAt;


    /**
     * 是否开启
     */
    @TableField("status")
    private Integer status;

    /**
     * 应用唯一编号
     */
    @TableField("app_key")
    private String appKey;


    @Override
    public String toString() {
        return "AppInfo{" +
        ", sysName=" + sysName +
        ", createdAt=" + createdAt +
        ", updatedAt=" + updatedAt +
        ", status=" + status +
        ", appKey=" + appKey +
        "}";
    }
}
