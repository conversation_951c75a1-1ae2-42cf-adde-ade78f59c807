package com.operation.electric.yykb.api.entity;

import cn.stylefeng.roses.kernel.model.response.ErrorResponseData;
import cn.stylefeng.roses.kernel.model.response.ResponseData;

import java.io.Serializable;

public class ResponseDataSeria extends ResponseData implements Serializable {

    public ResponseDataSeria() {
    }

    public ResponseDataSeria(Boolean success, Integer code, String message, Object data) {
        setSuccess(success);
        setCode(code);
        setMessage(message);
        setData(data);
    }

    public static SuccessResponseDataSeria successes() {
        return new SuccessResponseDataSeria();
    }

    public static SuccessResponseDataSeria successes(Object object) {
        return new SuccessResponseDataSeria(object);
    }

    public static SuccessResponseDataSeria successes(Integer code, String message, Object object) {
        return new SuccessResponseDataSeria(code, message, object);
    }

    public static ErrorResponseData error(String message) {
        return new ErrorResponseData(message);
    }

    public static ErrorResponseData error(Integer code, String message) {
        return new ErrorResponseData(code, message);
    }

    public static ErrorResponseData error(Integer code, String message, Object object) {
        return new ErrorResponseData(code, message, object);
    }
}
