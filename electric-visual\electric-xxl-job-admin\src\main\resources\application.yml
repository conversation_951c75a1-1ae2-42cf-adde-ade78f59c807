# 此配置只适合开发测试环境，详细配置参考： http://t.cn/A64RaHJm
server:
  port: 9080
  servlet:
    context-path: /xxl-job-admin

spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        server-addr: ${NACOS_HOST:localhost}:${NACOS_PORT:18848}
        metadata:
          management.context-path: ${server.servlet.context-path}/actuator
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
  config:
    import:
      - optional:nacos:<EMAIL>@.yml
      - optional:nacos:${spring.application.name}-@profiles.active@.yml
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      username: ${MYSQL_USER:root}
      password: ${MYSQL_PWD:root}
      url: jdbc:mysql://${MYSQL_HOST:127.0.0.1}:${MYSQL_PORT:3306}/${MYSQL_DB:electricx_job}?characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=GMT%2B8&allowMultiQueries=true&allowPublicKeyRetrieval=true
  mvc:
    static-path-pattern: /static/**
  freemarker:
    suffix: .ftl
    request-context-attribute: request
    settings:
      number_format: 0.##########
    template-loader-path: classpath:/templates/
  mail:
    host: smtp.mxhichina.com
    port: 465
    from: <EMAIL>
    username: <EMAIL>
    password: xxxx
    properties:
      mail:
        smtp:
          auth: true
          ssl.enable: true
          starttls.enable: false
          required: false
