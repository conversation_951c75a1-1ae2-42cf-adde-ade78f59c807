# 使用说明 V5.2
# 1. 使用docker-compose  宿主机不需要配置host来发现
# 2. 无需修改源码，根目录  docker-compose up 即可
# 3. 静静等待服务启动

version: '3'
services:
  electric-mysql:
    build:
      context: ./db
    environment:
      MYSQL_ROOT_HOST: "%"
      MYSQL_ROOT_PASSWORD: root
    restart: always
    container_name: electric-mysql
    image: electric-mysql
    command: --lower_case_table_names=1
    ports:
      - 3311:3306
    networks:
      - spring_cloud_default

  electric-redis:
    container_name: electric-redis
    image: redis:6.2.6
    restart: always
    ports:
      - 6379:6379
    networks:
      - spring_cloud_default

  electric-register:
    build:
      context: ./electric-register
    restart: always
    container_name: electric-register
    image: electric-register
    ports:
      - 8848:8848
    networks:
      - spring_cloud_default

  electric-gateway:
    build:
      context: ./electric-gateway
    restart: always
    container_name: electric-gateway
    image: electric-gateway
    ports:
      - 9999:9999
    networks:
      - spring_cloud_default

  electric-auth:
    build:
      context: ./electric-auth
    restart: always
    container_name: electric-auth
    image: electric-auth
    networks:
      - spring_cloud_default

  electric-upms:
    build:
      context: ./electric-upms/electric-upms-biz
    restart: always
    container_name: electric-upms
    image: electric-upms
    networks:
      - spring_cloud_default

  electric-flow-task:
    build:
      context: ./electric-flow/electric-flow-task/electric-flow-task-biz
    restart: always
    container_name: electric-flow-task
    image: electric-flow-task
    networks:
      - spring_cloud_default

  electric-flow-engine:
    build:
      context: ./electric-flow/electric-flow-engine/electric-flow-engine-biz
    restart: always
    container_name: electric-flow-engine
    image: electric-flow-engine
    networks:
      - spring_cloud_default

  electric-app-server:
    build:
      context: ./electric-app-server/electric-app-server-biz
    restart: always
    container_name: electric-app-server
    image: electric-app-server
    networks:
      - spring_cloud_default

  electric-monitor:
    build:
      context: ./electric-visual/electric-monitor
    restart: always
    image: electric-monitor
    container_name: electric-monitor
    ports:
      - 5001:5001
    networks:
      - spring_cloud_default

  electric-daemon-quartz:
    build:
      context: ./electric-visual/electric-daemon-quartz
    restart: always
    image: electric-daemon-quartz
    container_name: electric-daemon-quartz
    networks:
      - spring_cloud_default

  electric-daemon-elastic-job:
    build:
      context: ./electric-visual/electric-daemon-elastic-job
    restart: always
    image: electric-daemon-elastic-job
    container_name: electric-daemon-elastic-job
    networks:
      - spring_cloud_default

  electric-codegen:
    build:
      context: ./electric-visual/electric-codegen
    restart: always
    image: electric-codegen
    container_name: electric-codegen
    networks:
      - spring_cloud_default

  electric-mp-platform:
    build:
      context: ./electric-visual/electric-mp-platform
    restart: always
    image: electric-mp-platform
    container_name: electric-mp-platform
    networks:
      - spring_cloud_default

  electric-pay-platform:
    build:
      context: ./electric-visual/electric-pay-platform
    restart: always
    image: electric-pay-platform
    container_name: electric-pay-platform
    networks:
      - spring_cloud_default

  electric-report-platform:
    build:
      context: ./electric-visual/electric-report-platform
    restart: always
    image: electric-report-platform
    container_name: electric-report-platform
    ports:
      - 9095:9095
    networks:
      - spring_cloud_default

  electric-jimu-platform:
    build:
      context: ./electric-visual/electric-jimu-platform
    restart: always
    image: electric-jimu-platform
    container_name: electric-jimu-platform
    ports:
      - 5008:5008
    networks:
      - spring_cloud_default

networks:
  spring_cloud_default:
    name:  spring_cloud_default
    driver: bridge
