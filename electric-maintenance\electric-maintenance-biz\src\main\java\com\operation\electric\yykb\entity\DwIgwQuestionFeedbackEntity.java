package com.operation.electric.yykb.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 问题反馈表
 *
 * <AUTHOR>
 * @date 2025-06-20 11:50:53
 */
@Data
@TableName("dw_igw_question_feedback")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "问题反馈表")
public class DwIgwQuestionFeedbackEntity extends Model<DwIgwQuestionFeedbackEntity> {


	/**
	* 问题ID
	*/
    @TableId(type = IdType.AUTO)
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="问题ID")
    private Long id;

	/**
	* 反馈的问题内容
	*/
    @Schema(description="反馈的问题内容")
    private String questionContent;

	/**
	* 联系电话
	*/
    @Schema(description="联系电话")
    private String telephone;

	/**
	* 反馈创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="反馈创建时间")
    private LocalDateTime createTime;
}