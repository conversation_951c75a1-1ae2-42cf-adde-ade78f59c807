package com.operation.electric.yykbLog.exception;

public class StaffNotFoundException extends RuntimeException {
    public StaffNotFoundException(String staffNo, String sysId, Throwable cause) {
        super(msgFormat(staffNo, sysId));
    }

    public StaffNotFoundException(String staffNo, String sysId) {
        super(msgFormat(staffNo, sysId));
    }

    static String msgFormat(String staffNo, String sysId) {
        return String.format("系统编号为%s中未找到对应的OA账号%s，请先在系统编号为%s中登记OA账号", sysId, staffNo, sysId);
    }
}
