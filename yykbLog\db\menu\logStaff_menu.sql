-- 该脚本不要直接执行， 注意维护菜单的父节点ID 默认 父节点-1 , 默认租户 1

-- 菜单SQL
insert into sys_menu ( menu_id,parent_id, path, permission, menu_type, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50020, '-1', '/yykbLog/logStaff/index', '', '0', 'icon-bangzhushouji', '0', null , '8', null , '日志系统人员基础档案表管理', 1);

-- 菜单对应按钮SQL
insert into sys_menu ( menu_id,parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50021,50020, 'yykbLog_logStaff_view', '1', null, '1',  '0', null, '0', null, '日志系统人员基础档案表查看', 1);

insert into sys_menu ( menu_id,parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50022,50020, 'yykbLog_logStaff_add', '1', null, '1',  '0', null, '1', null, '日志系统人员基础档案表新增', 1);

insert into sys_menu (menu_id, parent_id, permission, menu_type, path, icon,  del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50023,50020, 'yykbLog_logStaff_edit', '1', null, '1',  '0', null, '2', null, '日志系统人员基础档案表修改', 1);

insert into sys_menu (menu_id, parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50024,50020, 'yykbLog_logStaff_del', '1', null, '1',  '0', null, '3', null, '日志系统人员基础档案表删除', 1);

insert into sys_menu ( menu_id,parent_id, permission, menu_type, path, icon, del_flag, create_time, sort_order, update_time, name, tenant_id)
values (50025,50020, 'yykbLog_logStaff_export', '1', null, '1',  '0', null, '3', null, '导入导出', 1);