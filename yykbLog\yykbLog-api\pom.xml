<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
            http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.operation</groupId>
		<artifactId>yykbLog</artifactId>
		<version>5.3.0</version>
	</parent>

	<artifactId>yykbLog-api</artifactId>

	<dependencies>
		<!-- 连表查询注解 -->
		<dependency>
			<groupId>com.github.yulichang</groupId>
			<artifactId>mybatis-plus-join-annotation</artifactId>
		</dependency>
		<!--core 工具类-->
		<dependency>
			<groupId>com.operation</groupId>
			<artifactId>electric-common-core</artifactId>
		</dependency>
		<!--mybatis plus extension,包含了mybatis plus core-->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-extension</artifactId>
		</dependency>
		<!--feign 工具类-->
		<dependency>
			<groupId>com.operation</groupId>
			<artifactId>electric-common-feign</artifactId>
		</dependency>
		<!-- excel 导入导出 -->
		<dependency>
			<groupId>com.operation</groupId>
			<artifactId>electric-common-excel</artifactId>
		</dependency>
		<!--选配: swagger文档-->
		<dependency>
			<groupId>com.operation</groupId>
			<artifactId>electric-common-swagger</artifactId>
		</dependency>
		<!--必备：xss 过滤模块-->
		<dependency>
			<groupId>com.operation</groupId>
			<artifactId>electric-common-xss</artifactId>
		</dependency>
		<!--monitor json 操作-->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.3.4</version>
		</dependency>
	</dependencies>
</project>
