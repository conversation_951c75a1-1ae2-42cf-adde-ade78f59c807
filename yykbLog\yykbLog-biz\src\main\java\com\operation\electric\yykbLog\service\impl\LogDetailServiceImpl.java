package com.operation.electric.yykbLog.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.operation.electric.yykbLog.dto.StaffResult;
import com.operation.electric.yykbLog.entity.LogDetailEntity;
import com.operation.electric.yykbLog.exception.StaffNotFoundException;
import com.operation.electric.yykbLog.logRest.v1.vm.DetailVm;
import com.operation.electric.yykbLog.mapper.LogDetailMapper;
import com.operation.electric.yykbLog.service.LogDetailService;
import com.operation.electric.yykbLog.service.LogStaffService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 日志系统，系统使用详情日志记录信息
 *
 * <AUTHOR>
 * @date 2024-03-21 16:57:27
 */
@Service
public class LogDetailServiceImpl extends ServiceImpl<LogDetailMapper, LogDetailEntity> implements LogDetailService {

    @Autowired
    private LogStaffService staffService;
    @Override
    public DetailVm addDetail(DetailVm vm) {
        StaffResult bySpec = staffService.findBySpec(vm.getStaffNo(), vm.getSysId());
        if (bySpec == null) {
            throw new StaffNotFoundException(vm.getStaffNo(), vm.getSysId());
        }
        LogDetailEntity detail = new LogDetailEntity();
        BeanUtil.copyProperties(vm, detail);
        save(detail);
        vm.setDetailId(detail.getId());
        return vm;
    }
}