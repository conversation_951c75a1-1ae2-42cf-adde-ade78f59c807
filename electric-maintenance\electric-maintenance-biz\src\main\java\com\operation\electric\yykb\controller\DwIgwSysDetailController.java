package com.operation.electric.yykb.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.stylefeng.roses.kernel.model.response.ResponseData;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.operation.electric.common.core.util.R;
import com.operation.electric.common.log.annotation.SysLog;
import com.operation.electric.common.security.annotation.Inner;
import com.operation.electric.yykb.api.entity.DwIgwSysDetailEntity;
import com.operation.electric.yykb.api.entity.ResponseDataSeria;
import com.operation.electric.yykb.entity.DwIgwSysApplicationClassEntity;
import com.operation.electric.yykb.entity.DwIgwSysApplicationDirectionEntity;
import com.operation.electric.yykb.service.DwIgwSysApplicationClassService;
import com.operation.electric.yykb.service.DwIgwSysApplicationDirectionService;
import com.operation.electric.yykb.service.DwIgwSysDetailService;
import com.operation.electric.yykb.service.FormCategoriesService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import com.operation.electric.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * igw应用列表
 *
 * <AUTHOR>
 * @date 2024-03-25 15:30:42
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/dwIgwSysDetail")
@Tag(description = "dwIgwSysDetail", name = "igw应用列表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class DwIgwSysDetailController {

    private final DwIgwSysDetailService dwIgwSysDetailService;

    private final StringRedisTemplate redisTemplate;

	private final FormCategoriesService formCategoriesService;

	private final DwIgwSysApplicationClassService dwIgwSysApplicationClassService;

	private final DwIgwSysApplicationDirectionService dwIgwSysApplicationDirectionService;
    /**
     * 分页查询
     *
     * @param page           分页对象
     * @param dwIgwSysDetail igw应用列表
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysDetail_view')")
    public R getDwIgwSysDetailPage(@ParameterObject Page page, @ParameterObject DwIgwSysDetailEntity dwIgwSysDetail) {
        LambdaQueryWrapper<DwIgwSysDetailEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StrUtil.isNotBlank(dwIgwSysDetail.getSysName())
				, DwIgwSysDetailEntity::getSysName, dwIgwSysDetail.getSysName())
				.like(StrUtil.isNotBlank(dwIgwSysDetail.getSysFac())
						,DwIgwSysDetailEntity::getSysFac,dwIgwSysDetail.getSysFac())
				.like(StrUtil.isNotBlank(dwIgwSysDetail.getSysFacPeo())
						,DwIgwSysDetailEntity::getSysFac,dwIgwSysDetail.getSysFacPeo())
				.like(StrUtil.isNotBlank(dwIgwSysDetail.getSysFacPeoMail())
						,DwIgwSysDetailEntity::getSysFac,dwIgwSysDetail.getSysFacPeoMail())
				.eq(dwIgwSysDetail.getApplicationState() != null , DwIgwSysDetailEntity::getApplicationState , dwIgwSysDetail.getApplicationState())
				.like(StrUtil.isNotBlank(dwIgwSysDetail.getOnline()),DwIgwSysDetailEntity::getOnline,dwIgwSysDetail.getOnline())
				.like(isEmptyNull(dwIgwSysDetail.getApplicationClass()),DwIgwSysDetailEntity::getApplicationClass,dwIgwSysDetail.getApplicationClass())
				.like(isEmptyNull(dwIgwSysDetail.getApplicationDirect()),DwIgwSysDetailEntity::getApplicationDirect,dwIgwSysDetail.getApplicationDirect())
                .eq(StrUtil.isNotBlank(dwIgwSysDetail.getSysType()), DwIgwSysDetailEntity::getSysType, dwIgwSysDetail.getSysType());
		Page<DwIgwSysDetailEntity> pageData = dwIgwSysDetailService.page(page, wrapper);
		if(0 == pageData.getTotal()){
			return R.ok(pageData);
		}
		List<DwIgwSysApplicationClassEntity> classList = this.dwIgwSysApplicationClassService.list();
		List<DwIgwSysApplicationDirectionEntity> directionList = this.dwIgwSysApplicationDirectionService.list();
		List<DwIgwSysDetailEntity> records = pageData.getRecords();
		//返回所属分类的名称
		for (DwIgwSysDetailEntity sysDetailEntity : records) {
			String className = classList.stream().filter(obj -> obj.getId().longValue() == sysDetailEntity.getApplicationClass())
					.map(DwIgwSysApplicationClassEntity::getApplicationName)
					.findFirst().orElse(null);
			sysDetailEntity.setSysClass(className);
			String directName = directionList.stream().filter(obj -> obj.getId().longValue() == sysDetailEntity.getApplicationDirect())
					.map(DwIgwSysApplicationDirectionEntity::getName)
					.findFirst().orElse(null);
			sysDetailEntity.setSysDirect(directName);
		}
		pageData.setRecords(records);
		return R.ok(pageData);
    }

    public boolean isEmptyNull(Long param){
		if (null == param || param.longValue() != 0) {
			return false;
		}
    	return true;
	}

    /**
     * 通过id查询igw应用列表
     *
     * @param xh id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{xh}")
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysDetail_view')")
    public R getById(@PathVariable("xh") String xh) {
        return R.ok(dwIgwSysDetailService.getById(xh));
    }

    /**
     * 新增igw应用列表
     *
     * @param dwIgwSysDetail igw应用列表
     * @return R
     */
    @Operation(summary = "新增igw应用列表", description = "新增igw应用列表")
    @SysLog("新增igw应用列表")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysDetail_add')")
    @CacheEvict(value = {"dwSysListAll", "dwSysList"}, allEntries = true)
    public R save(@RequestBody DwIgwSysDetailEntity dwIgwSysDetail) {
        dwIgwSysDetail.setXh(IdUtil.simpleUUID());
        if (StringUtils.isNotBlank(dwIgwSysDetail.getImgUrl())) {
            String imgUrl = dwIgwSysDetail.getImgUrl();
            String substring = imgUrl.substring(imgUrl.indexOf("=") + 1);
            dwIgwSysDetail.setSysImgUrl("/upload/" + substring);
        }
        //配置文件
        dwIgwSysDetailService.operationConfigurationFile();
        return R.ok(dwIgwSysDetailService.save(dwIgwSysDetail));
    }


    /**
     * 应用列表展示
     *
     * <AUTHOR>
     * @Date 2022-12-06
     */

    @ResponseBody
    @RequestMapping("/dwSystem/list")
    public ResponseData getSysList(@RequestParam(name = "sysType", defaultValue = "prod", required = false) String sysType) {
        List<DwIgwSysDetailEntity> dwIgwSysDetailEntities = dwIgwSysDetailService.getSysList(sysType);
        return ResponseDataSeria.successes(dwIgwSysDetailEntities);
    }

    /**
     * 应用列表展示所有
     *
     * <AUTHOR>
     * @Date 2022-12-06
     */

    @ResponseBody
    @RequestMapping("/dwSystem/listAll")
    public ResponseData getSysListAll(@Valid DwIgwSysDetailEntity param) {
        List<DwIgwSysDetailEntity> dwIgwSysDetailEntities = dwIgwSysDetailService.getSysListAll(param);
        return ResponseDataSeria.successes(dwIgwSysDetailEntities);
    }

    /**
     * 修改igw应用列表
     *
     * @param dwIgwSysDetail igw应用列表
     * @return R
     */
    @Operation(summary = "修改igw应用列表", description = "修改igw应用列表")
    @SysLog("修改igw应用列表")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysDetail_edit')")
    @CacheEvict(value = {"dwSysListAll", "dwSysList"}, allEntries = true)
    public R updateById(@RequestBody DwIgwSysDetailEntity dwIgwSysDetail) {
        if (StringUtils.isNotBlank(dwIgwSysDetail.getImgUrl())) {
            String imgUrl = dwIgwSysDetail.getImgUrl();
            String substring = imgUrl.substring(imgUrl.indexOf("=") + 1);
            dwIgwSysDetail.setSysImgUrl("/upload/" + substring);
        }
        //配置文件
        dwIgwSysDetailService.operationConfigurationFile();
        return R.ok(dwIgwSysDetailService.updateById(dwIgwSysDetail));
    }

    /**
     * 通过id删除igw应用列表
     *
     * @param ids xh列表
     * @return R
     */
    @Operation(summary = "通过id删除igw应用列表", description = "通过id删除igw应用列表")
    @SysLog("通过id删除igw应用列表")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysDetail_del')")
    @CacheEvict(value = {"dwSysListAll", "dwSysList"}, allEntries = true)
    public R removeById(@RequestBody String[] ids) {
        return R.ok(dwIgwSysDetailService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param dwIgwSysDetail 查询条件
     * @param ids            导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('maintenance_dwIgwSysDetail_export')")
    public List<DwIgwSysDetailEntity> export(DwIgwSysDetailEntity dwIgwSysDetail, String[] ids) {
        return dwIgwSysDetailService.list(Wrappers.lambdaQuery(dwIgwSysDetail).in(ArrayUtil.isNotEmpty(ids), DwIgwSysDetailEntity::getXh, ids));
    }

    /**
     * 应用心率接口
     * @param appId
     * @return
     */
    @Inner(value = false)
    @PostMapping("/monitor/{appId}")
    public R monitor(@PathVariable String appId) {
        return dwIgwSysDetailService.monitor(appId);
    }

    /**
     * 监测应用10分钟之内是否有发送心率
     *
     * @return
     */
    @Inner(value = false)
    @GetMapping("monitoringAllData")
    public R monitoringAllData() {
        return R.ok(dwIgwSysDetailService.monitoringAllData());
    }

    /**
     * 新增/修改常用应用检测
     * 最多存在8个默认的常用应用
     */
    @GetMapping("/quantitativeRestriction")
    public R quantitativeRestriction() {
        return R.ok(dwIgwSysDetailService.quantitativeRestriction());
    }

    /**
     * 查询生产环境的所有应用
     */
    @Inner(value = false)
    @GetMapping("/findAll")
    public R findAll() {
        return R.ok(dwIgwSysDetailService.findAll());
    }


    /*
    		应用上下架接口
     */
	@Operation(summary = "应用上下架接口", description = "应用上下架接口")
	@SysLog("应用上下架接口")
	@PostMapping("/dwIgwSysApplicationLineStatusChange")
	public R dwIgwSysApplicationLineStatusChange(@RequestBody DwIgwSysDetailEntity detailEntity){
    	return R.ok(this.dwIgwSysDetailService.dwIgwSysApplicationLineStatusChange(detailEntity));
	}

	/**
	 *
	 */

	/*
			发送通知消息接口
	 */

//		/*
//			应用分类查询
//		 */
//		@SneakyThrows
//		@GetMapping("/treeList")
//		public List<FormCategoryNode> buidTreeList(){
//			return formCategoriesService.buidTreeList();
//		}
//
//		/*
//			DIY常用应用排序
//		*/
//	@ResponseExcel
//	@GetMapping("/diySortList")
//	public R diySortList(FormCategoryNode formCategoryNode){
//		return this.formCategoriesService.diySortList(formCategoryNode);
//	}

}