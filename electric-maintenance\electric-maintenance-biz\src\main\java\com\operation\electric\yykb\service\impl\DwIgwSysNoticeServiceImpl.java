package com.operation.electric.yykb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.operation.electric.yykb.entity.DwIgwSysNoticeEntity;
import com.operation.electric.yykb.mapper.DwIgwSysNoticeMapper;
import com.operation.electric.yykb.service.DwIgwSysNoticeService;
import org.springframework.stereotype.Service;
/**
 * APP首页最上方广播通知表
 *
 * <AUTHOR>
 * @date 2025-01-16 15:10:17
 */
@Service
public class DwIgwSysNoticeServiceImpl extends ServiceImpl<DwIgwSysNoticeMapper, DwIgwSysNoticeEntity> implements DwIgwSysNoticeService {
	@Override
	public void addNotice(DwIgwSysNoticeEntity dwIgwSysNotice) {
		//后续可能扩展专属消息 先预留个位置
		this.save(dwIgwSysNotice);
	}
}