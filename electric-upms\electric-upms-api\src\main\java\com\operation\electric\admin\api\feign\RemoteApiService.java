package com.operation.electric.admin.api.feign;

import com.operation.electric.admin.api.dto.UploadFileDO;
import com.operation.electric.common.core.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "remote-api", url = "http://192.168.0.11:7861")
public interface RemoteApiService {

    @PostMapping("/knowledge_base/upload_docs")
    public R uploadDocs(@RequestBody UploadFileDO uploadFileDO);
}
