package com.operation.electric.yykbLog.logRest.v2;

import cn.hutool.core.bean.BeanUtil;
import com.operation.electric.common.security.annotation.Inner;
import com.operation.electric.yykbLog.logRest.security.ApiOptionAuth;
import com.operation.electric.yykbLog.logRest.security.SecuritySessionUtils;
import com.operation.electric.yykbLog.response.RestResponseData;
import com.operation.electric.yykbLog.service.LogStaffService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Inner(false)
@RestController
@RequestMapping("/api/v2/log/staffs")
@AllArgsConstructor
@Tag(name = "应用系统员工信息", description = "用来管理应用系统的员工信息")
public class StaffV2Resource {
    private final LogStaffService staffService;

    @PostMapping("")
    @ApiOptionAuth(required = true)
    @Operation(
           parameters = {
            @Parameter(name = "APP-KEY", description = "应用系统id", in = ParameterIn.HEADER),
            @Parameter(name = "SYS-ID", description = "应用系统编码", required = true, in = ParameterIn.HEADER)
    })
    @Schema(name = "保存应用系统员工信息", description = "保存应用系统员工信息")
    @Tag(name = "v2")
    public RestResponseData<com.operation.electric.yykbLog.logRest.v1.vm.StaffVm> create(@RequestBody @Valid com.operation.electric.yykbLog.logRest.v2.vm.StaffVm vm) {
        com.operation.electric.yykbLog.logRest.v1.vm.StaffVm staff = new com.operation.electric.yykbLog.logRest.v1.vm.StaffVm();
        BeanUtil.copyProperties(vm, staff);

        staff.setSysId(SecuritySessionUtils.getSysId());

        com.operation.electric.yykbLog.logRest.v1.vm.StaffVm staffVm = staffService.addStaff(staff);
        return RestResponseData.success(staffVm);
    }

}
