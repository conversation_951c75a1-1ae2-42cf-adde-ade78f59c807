package com.operation.electric.yykbLog.logRest.security;

import com.operation.electric.yykbLog.service.LogSysInfoService;
import jakarta.servlet.http.HttpServletRequest;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;
import java.util.Optional;

@Component
@Aspect
public class ApiOptionAuthAspect {
    @Autowired
    private LogSysInfoService sysInfoService;

    @Before("@annotation(com.operation.electric.yykbLog.logRest.security.ApiOptionAuth)")
    public void before(JoinPoint joinPoint) {

        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();

        if (method != null) {
            ApiOptionAuth apiLog = method.getAnnotation(ApiOptionAuth.class);
            System.out.println("切入方法注解的title:" + apiLog.value());
            if (apiLog.required()) {
                HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder
                        .getRequestAttributes())
                        .getRequest();

                String appId = Optional.ofNullable(request.getHeader("APP-KEY")).orElse(request.getHeader("app-key"));
                String sysId = Optional.ofNullable(request.getHeader("SYS-ID")).orElse(request.getHeader("sys-id"));
                sysInfoService.checkSysInfo(appId, sysId);
            }
        }
    }
}
