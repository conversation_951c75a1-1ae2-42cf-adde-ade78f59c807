package com.operation.electric.flow.engine;

import com.operation.electric.common.feign.annotation.EnableElectricFeignClients;
import com.operation.electric.common.security.annotation.EnableElectricResourceServer;
import com.operation.electric.common.swagger.annotation.EnableOpenApi;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.ProcessEngines;
import org.flowable.engine.RepositoryService;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * <AUTHOR> archetype
 * <p>
 * 项目启动类
 */
@EnableOpenApi("engine")
@EnableElectricFeignClients
@EnableDiscoveryClient
@EnableElectricResourceServer
@SpringBootApplication
public class ElectricFlowEngineApplication {

	public static void main(String[] args) {
		SpringApplication.run(ElectricFlowEngineApplication.class, args);
	}

}
