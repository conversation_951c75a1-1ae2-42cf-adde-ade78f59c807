package com.operation.electric.yykbLog.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 日志系统人员基础档案表
 *
 * <AUTHOR>
 * @date 2024-03-21 16:54:37
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("log_staff")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "日志系统人员基础档案表")
public class LogStaffEntity extends Model<LogStaffEntity> {

 
	/**
	* id
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="id")
    private Integer id;

	/**
	* 员工编码
	*/
    @Schema(description="员工编码")
    private String staffNo;

	/**
	* 员工姓名
	*/
    @Schema(description="员工姓名")
    private String staffNm;

	/**
	* 标准单位编码
	*/
    @Schema(description="标准单位编码")
    private String stdOrgNo;

	/**
	* 标准单位名称
	*/
    @Schema(description="标准单位名称")
    private String stdOrgNm;

	/**
	* 标准部门编码
	*/
    @Schema(description="标准部门编码")
    private String stdDeptNo;

	/**
	* 标准部门名称
	*/
    @Schema(description="标准部门名称")
    private String stdDeptNm;

	/**
	* 标准班组编码
	*/
    @Schema(description="标准班组编码")
    private String stdTeamNo;

	/**
	* 标准班组名称
	*/
    @Schema(description="标准班组名称")
    private String stdTeamNm;

	/**
	* 系统编号
	*/
    @Schema(description="系统编号")
    private String sysId;

	/**
	* 系统名称
	*/
    @Schema(description="系统名称")
    private String sysName;
 
	/**
	* createdAt
	*/
    @Schema(description="创建时间")
	@TableField(fill = FieldFill.INSERT)
    private Date createdAt;
 
	/**
	* updatedAt
	*/
    @Schema(description="更新时间")
	@TableField(fill = FieldFill.UPDATE)
    private Date updatedAt;
}