<?xml version="1.0" encoding="UTF-8"?>
<!--
  Copyright 1999-2018 Alibaba Group Holding Ltd.
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at
       http://www.apache.org/licenses/LICENSE-2.0
  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
 -->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.operation</groupId>
        <artifactId>electric</artifactId>
        <version>5.3.0</version>
    </parent>

    <artifactId>electric-register</artifactId>
    <packaging>jar</packaging>
    <name>electric-register</name>
    <description>nacos 注册配置中心</description>

    <properties>
        <nacos.version>2.2.4-OEM</nacos.version>
        <rocksdb.version>7.10.2</rocksdb.version>
        <spring-boot-dependencies.version>2.7.18</spring-boot-dependencies.version>
        <spring-cloud.version>2021.0.8</spring-cloud.version>
        <spring-boot-admin.version>2.7.11</spring-boot-admin.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>io.springboot.nacos</groupId>
            <artifactId>nacos-config</artifactId>
            <version>${nacos.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-jasper</artifactId>
        </dependency>

        <dependency>
            <groupId>io.springboot.nacos</groupId>
            <artifactId>nacos-naming</artifactId>
            <version>${nacos.version}</version>
        </dependency>

        <dependency>
            <groupId>io.springboot.nacos</groupId>
            <artifactId>nacos-istio</artifactId>
            <version>${nacos.version}</version>
        </dependency>

        <dependency>
            <groupId>io.springboot.nacos</groupId>
            <artifactId>nacos-plugin-default-impl</artifactId>
            <version>${nacos.version}</version>
        </dependency>

        <dependency>
            <groupId>io.springboot.nacos</groupId>
            <artifactId>nacos-prometheus</artifactId>
            <version>${nacos.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-system</artifactId>
        </dependency>

    </dependencies>


    <!--依赖版本声明-->
    <dependencyManagement>
        <dependencies>

            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringBoot Admin -->
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>

            <dependency>
                <artifactId>rocksdbjni</artifactId>
                <groupId>org.rocksdb</groupId>
                <version>${rocksdb.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>**/*.woff</exclude>
                    <exclude>**/*.woff2</exclude>
                    <exclude>**/*.ttf</exclude>
                    <exclude>**/*.eot</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.woff</include>
                    <include>**/*.woff2</include>
                    <include>**/*.ttf</include>
                    <include>**/*.eot</include>
                </includes>
            </resource>
        </resources>
    </build>
</project>
