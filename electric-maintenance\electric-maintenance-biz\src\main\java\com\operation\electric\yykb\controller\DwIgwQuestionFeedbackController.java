package com.operation.electric.yykb.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.operation.electric.common.core.util.R;
import com.operation.electric.common.log.annotation.SysLog;
import com.operation.electric.yykb.dto.DwIgwQuestionFeedbackDto;
import com.operation.electric.yykb.entity.DwIgwQuestionFeedbackEntity;
import com.operation.electric.yykb.jobhandler.CleanUnlinkImageJob;
import com.operation.electric.yykb.service.DwIgwQuestionFeedbackService;
import com.operation.electric.yykb.vo.DwIgwQuestionFeedbackVo;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import com.operation.electric.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 问题反馈表
 *
 * <AUTHOR>
 * @date 2025-06-20 11:50:53
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/dwIgwQuestionFeedback")
@Tag(description = "dwIgwQuestionFeedbdwIgwQuestionFeedbackack", name = "问题反馈表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class DwIgwQuestionFeedbackController {

	private final DwIgwQuestionFeedbackService dwIgwQuestionFeedbackService;
	private static final Set<String> ALLOWED_EXTENSIONS = Set.of("png", "jpg", "jpeg");

	@Autowired
	private CleanUnlinkImageJob cleanUnlinkImageJob;
	/**
	 * 分页查询
	 * @param page 分页对象
	 * @param dwIgwQuestionFeedback 问题反馈表
	 * @return
	 */
	@Operation(summary = "分页查询", description = "分页查询")
	@GetMapping("/page")
	@PreAuthorize("@pms.hasPermission('electric_dwIgwQuestionFeedback_view')")
	public R getDwIgwQuestionFeedbackPage(@ParameterObject Page page, @ParameterObject DwIgwQuestionFeedbackEntity dwIgwQuestionFeedback) {
		return R.ok(dwIgwQuestionFeedbackService.getDwIgwQuestionFeedbackPage(page, dwIgwQuestionFeedback));
	}


	/**
	 * 通过id查询问题反馈表
	 * @param id id
	 * @return R
	 */
	@Operation(summary = "通过id查询", description = "通过id查询")
	@GetMapping("/{id}")
	@PreAuthorize("@pms.hasPermission('electric_dwIgwQuestionFeedback_view')")
	public R getById(@PathVariable("id") Long id) {
		return R.ok(dwIgwQuestionFeedbackService.getById(id));
	}

	/**
	 * 新增问题反馈表
	 * @param dwIgwQuestionFeedback 问题反馈表
	 * @return R
	 */
	@Operation(summary = "新增问题反馈表", description = "新增问题反馈表")
	@SysLog("新增问题反馈表")
	@PostMapping
	@PreAuthorize("@pms.hasPermission('electric_dwIgwQuestionFeedback_add')")
	public R save(@RequestBody DwIgwQuestionFeedbackEntity dwIgwQuestionFeedback) {
		return R.ok(dwIgwQuestionFeedbackService.save(dwIgwQuestionFeedback));
	}

	/**
	 * 修改问题反馈表
	 * @param dwIgwQuestionFeedback 问题反馈表
	 * @return R
	 */
	@Operation(summary = "修改问题反馈表", description = "修改问题反馈表")
	@SysLog("修改问题反馈表")
	@PutMapping
	@PreAuthorize("@pms.hasPermission('electric_dwIgwQuestionFeedback_edit')")
	public R updateById(@RequestBody DwIgwQuestionFeedbackEntity dwIgwQuestionFeedback) {
		return R.ok(dwIgwQuestionFeedbackService.updateById(dwIgwQuestionFeedback));
	}

	/**
	 * 通过id删除问题反馈表
	 * @param ids id列表
	 * @return R
	 */
	@Operation(summary = "通过id删除问题反馈表", description = "通过id删除问题反馈表")
	@SysLog("通过id删除问题反馈表")
	@DeleteMapping
	@PreAuthorize("@pms.hasPermission('electric_dwIgwQuestionFeedback_del')")
	public R removeById(@RequestBody Long[] ids) {
		return R.ok(dwIgwQuestionFeedbackService.removeBatchByIds(CollUtil.toList(ids)));
	}


	/**
	 * 导出excel 表格
	 * @param dwIgwQuestionFeedback 查询条件
	 * @param ids 导出指定ID
	 * @return excel 文件流
	 */
	@ResponseExcel
	@GetMapping("/export")
	@PreAuthorize("@pms.hasPermission('electric_dwIgwQuestionFeedback_export')")
	public List<DwIgwQuestionFeedbackEntity> export(DwIgwQuestionFeedbackEntity dwIgwQuestionFeedback, Long[] ids) {
		return dwIgwQuestionFeedbackService.list(Wrappers.lambdaQuery(dwIgwQuestionFeedback).in(ArrayUtil.isNotEmpty(ids), DwIgwQuestionFeedbackEntity::getId, ids));
	}

	@PostMapping("/upload")
	public R<String> uploadImage(@RequestParam("image") MultipartFile file, @RequestParam("sessionId") String sessionId) {
		String extension = FilenameUtils.getExtension(file.getOriginalFilename());
		if (!ALLOWED_EXTENSIONS.contains(extension.toLowerCase())) {
			throw new IllegalArgumentException("Invalid file type");
		}
		try { // 存储图片到服务器
			String filePath = dwIgwQuestionFeedbackService.saveImage(file, sessionId); // 返回响应给前端
			return R.ok(filePath);
		} catch (Exception e) {
			return R.failed("文件上传失败");
		} // 返回响应给前端。
	}

	// 提交反馈接口
	@PostMapping("/submitFeedback")
	public R<DwIgwQuestionFeedbackEntity> submitFeedback(
			@RequestBody DwIgwQuestionFeedbackVo feedbackVo) {
		DwIgwQuestionFeedbackEntity feedback = dwIgwQuestionFeedbackService.submitFeedback(feedbackVo.getContent(), feedbackVo.getContactPhone(), feedbackVo.getSessionId());
		return R.ok(feedback).setMsg("提交成功");
	}




	@GetMapping("/cleanUnlinkedImages")
	public void cleanUnlinkedImages() {
		cleanUnlinkImageJob.cleanUnlinkedImages();
	}



}