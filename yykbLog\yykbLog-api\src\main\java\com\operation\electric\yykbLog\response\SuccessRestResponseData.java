package com.operation.electric.yykbLog.response;



public class SuccessRestResponseData<T> extends RestResponseData<T> {
    public SuccessRestResponseData() {
        super(true, DEFAULT_SUCCESS_CODE, "请求成功", (T)null);
    }

    public SuccessRestResponseData(T object) {
        super(true, DEFAULT_SUCCESS_CODE, "请求成功", object);
    }

    public SuccessRestResponseData(Integer code, String message, T object) {
        super(true, code, message, object);
    }
}
