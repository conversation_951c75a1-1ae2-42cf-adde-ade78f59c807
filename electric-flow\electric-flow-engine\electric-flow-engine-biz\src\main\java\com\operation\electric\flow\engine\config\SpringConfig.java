package com.operation.electric.flow.engine.config;

import org.flowable.engine.ProcessEngine;
import org.flowable.engine.ProcessEngineConfiguration;
import org.flowable.engine.TaskService;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * @Author: zhuoxaing.peng
 * @Date: 2025-05-26 12:04
 * @description:
 * @Version: 1.0
 */
@Configuration
public class SpringConfig {

	@Bean
	public ProcessEngineConfiguration processEngineConfiguration(DataSource dataSource,
																 PlatformTransactionManager transactionManager) {
		SpringProcessEngineConfiguration config = new SpringProcessEngineConfiguration();
		config.setDataSource(dataSource);
		config.setTransactionManager(transactionManager);
		config.setDatabaseSchemaUpdate(ProcessEngineConfiguration.DB_SCHEMA_UPDATE_TRUE);
		// 其他配置...
		return config;
	}

	@Bean
	@Primary
	public ProcessEngine processEngine(ProcessEngineConfiguration config) {
		return config.buildProcessEngine();
	}

	@Bean
	public TaskService taskService(ProcessEngine processEngine) {
		return processEngine.getTaskService();
	}

}
