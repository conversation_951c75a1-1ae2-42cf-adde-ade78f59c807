package com.operation.electric.yykbLog.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

/**
 * 应用系统档案
 *
 * <AUTHOR>
 * @date 2024-03-21 17:21:46
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("log_sys_info")
@EqualsAndHashCode(callSuper = true)
@Builder(toBuilder = true)
@Schema(description = "应用系统档案")
public class LogSysInfoEntity extends Model<LogSysInfoEntity> {

 
	/**
	* id
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="id")
    private Integer id;

	/**
	* 系统名称
	*/
    @Schema(description="系统名称")
    private String sysName;

	/**
	* 盐
	*/
    @Schema(description="盐")
    private String salt;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
	@TableField(fill = FieldFill.INSERT)
    private Date createdAt;

	/**
	* 创建者
	*/
    @Schema(description="创建者")
    private Long creatorId;

	/**
	* 更新时间
	*/
    @Schema(description="更新时间")
	@TableField(fill = FieldFill.UPDATE)
    private Date updatedAt;

	/**
	* 更新人
	*/
    @Schema(description="更新人")
    private Long updatorId;

	/**
	* 是否开启
	*/
    @Schema(description="是否开启")
    private Integer status;

	/**
	* 应用唯一编号
	*/
    @Schema(description="应用唯一编号")
    private String appKey;

	/**
	* 应用密钥
	*/
    @Schema(description="应用密钥")
    private String appSecret;

	/**
	* 系统编码
	*/
    @Schema(description="系统编码")
    private String sysId;
}